import { canStack, findAvailableSlot, getTargetInventory, getTotalWeight, isSlotWithItem } from '../helpers';
import { validateMove } from '../thunks/validateItems';
import { store } from '../store';
import { DragSource, DropTarget, InventoryType, SlotWithItem } from '../typings';
import { moveSlots, setItemAmount, stackSlots, swapSlots } from '../store/inventory';
import { Items } from '../store/items';

export const onDrop = (source: DragSource, target?: DropTarget) => {

  const { inventory: state } = store.getState();
  
  const { sourceInventory, targetInventory } = getTargetInventory(state, source.inventoryid, target?.inventoryid);
  
  if(targetInventory.type == InventoryType.CRAFTING || targetInventory.type == InventoryType.SHOP) {
    return;
  }
  const sourceSlot = sourceInventory.items[source.item.slot - 1] as SlotWithItem;

  const sourceData = Items[sourceSlot.name];

  var match = false;
  
  if (sourceData === undefined) return console.error(`${sourceSlot.name} item data undefined!`);
  
  // If dragging from container slot
  if (sourceSlot.metadata?.container !== undefined) {
    // Prevent storing container in container
    if (targetInventory.type === InventoryType.CONTAINER)
      return console.log(`Cannot store container ${sourceSlot.name} inside another container`);

    // Prevent dragging of container slot when opened
    // state.targetInventories.forEach(element => {
    //   if (element.id == sourceSlot.metadata.container)
    //     match = true;
    // });
    // if(match)
    //   return console.log(`Cannot move container ${sourceSlot.name} when opened`);
  }

  const targetSlot = target
    ? targetInventory.items[target.item.slot - 1]
    : findAvailableSlot(sourceSlot, sourceData, targetInventory.items);

  if (targetSlot === undefined) return console.error('Target slot undefined!');

  // If dropping on container slot when opened
  // if (targetSlot.metadata?.container !== undefined) {
  //   state.targetInventories.forEach(element => {
  //     if(element.id == targetSlot.metadata.container)
  //       match = true;
  //   });
  //   if(match)
  //     return console.log(`Cannot swap item ${sourceSlot.name} with container ${targetSlot.name} when opened`);
  // }

  const count =
    state.shiftPressed && sourceSlot.count > 1 && sourceInventory.type !== 'shop'
      ? Math.floor(sourceSlot.count / 2)
      : state.itemAmount === 0 || state.itemAmount > sourceSlot.count
      ? sourceSlot.count
      : state.itemAmount;
  
  // if( targetInventory.maxWeight < (getTotalWeight(targetInventory.items) - (targetSlot.weight / targetSlot.count) + (sourceSlot.weight / sourceSlot.count * count))) {
  //   return console.log('maxweightoverflow');
  // } else if( sourceInventory.maxWeight < (getTotalWeight(sourceInventory.items) + (targetSlot.weight / targetSlot.count) - (sourceSlot.weight / sourceSlot.count * count))) {
  //   return console.log('maxweightoverflow');
  // }

  const data = {
    fromSlot: sourceSlot,
    toSlot: targetSlot,
    fromType: sourceInventory.type,
    toType: targetInventory.type,
    fromId: sourceInventory.id,
    toId: targetInventory.id,
    count: count,
  };
  
  isSlotWithItem(targetSlot, true)
    ? sourceData.stack && canStack(sourceSlot, targetSlot)
      ? store.dispatch(
          stackSlots({
            ...data,
            toSlot: targetSlot,
          })
        )
      : store.dispatch(
          swapSlots({
            ...data,
            toSlot: targetSlot,
          })
        )
    : store.dispatch(moveSlots(data));
  
  store.dispatch(
    validateMove({
      ...data,
      fromSlot: sourceSlot.slot,
      toSlot: targetSlot.slot,
    })
  );

  store.dispatch(setItemAmount(0))
};
