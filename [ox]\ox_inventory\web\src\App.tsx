import InventoryComponent from './components/inventory';
import useNuiEvent from './hooks/useNuiEvent';
import { Items } from './store/items';
import { Locale } from './store/locale';
import { inventoryimagepath, setImagePath, setInventoryImagePath } from './store/imagepath';
import { setAdditionalMetadata, setupInventory } from './store/inventory';
import { Inventory } from './typings';
import { useAppDispatch } from './store';
import { debugData } from './utils/debugData';
import DragPreview from './components/utils/DragPreview';
import { fetchNui } from './utils/fetchNui';
import { useDragDropManager } from 'react-dnd';
import KeyPress from './components/utils/KeyPress';
import { PlayerData } from './store/playerData';
import { bodyPart } from './typings/damage';
import { setDamage } from './store/damage';
import { setPreferences } from './store/preferences';

debugData([
  {
    action: 'setupInventory',
    data: {
      targetInventories: [
        {
          id: 'ground',
          type: 'crafting',
          slots: 80,
          label: 'Ground',
          items: [
            {
              slot: 1,
              name: 'lockpick',
              weight: 500,
              price: 300,
              duration: 5000,
              ingredients: {
                iron: 5,
                copper: 12,
                powersaw: 0.1,
              },
              metadata: {
                description: 'Simple lockpick that breaks easily and can pick basic door locks',
              },
            },
          ],
        },
        {
          id: 'shop',
          type: 'shop',
          slots: 80,
          label: 'Motel Stash',
          items: [
            {
              slot: 1,
              name: 'lockpick',
              weight: 500,
              price: 300,
              ingredients: {
                iron: 5,
                copper: 12,
                powersaw: 0.1,
              },
              metadata: {
                description: 'Simple lockpick that breaks easily and can pick basic door locks',
              },
            },
          ],
        },
      ],
      playerInventories: [
        {
          id: '1',
          type: 'player',
          slots: 36,
          label: 'Macie',
          weight: 12500,
          maxWeight: 80000,
          items: [
            {
              "stack":false,
              "close":false,
              "metadata":{
                "label":"Flashlight",
                "components":[],
                "durability":50.30000000000048
              },
              "weight":125,
              "count":1,
              "name":"WEAPON_FLASHLIGHT",
              "slot":1
            },
            {
              "stack":true,
              "weight":2,
              "count":1,
              "close":true,
              "metadata":{
                "label":"Banana Bag",
                "description":"Great for delivering nutrients to those on the brink.",
              },
              "name":"bananabag",
              "slot":2
            },
            {
              "stack":true,
              "weight":2,
              "count":1,
              "close":true,
              "metadata":{
                "label":"Coconut Water",
                "description":"For those pesky splinters or unwanted guest hairs.",
                type: 'Special',
                durability:150,
              },
              "name":"coconutwater",
              "slot":3
            },
            {
              "stack":true,
              "weight":230,
              "count":2,
              "close":true,
              "metadata":{
                label: 'Well Done Bandage',
                image: 'bandage_welldone',
                "description":"A themed bandage, hope it can still stop the bleeding...",
                quality: 87
              },
              "name":"bandage",
              "slot":4
            },
            {
              "stack":false,
              "close":false,
              "metadata":{
                "label":"Pistol",
                "components":[],
                "serial":"978747WUM828591",
                "ammo":11,
                "registered":"Macie O'Flannigan",
                "durability":98.20000000000008,
                quality: 87
              },
              "weight":71185,
              "count":1,
              "name":"WEAPON_PISTOL",
              "slot":5
            },
            {
              "stack":true,
              "weight":180,
              "count":6,
              "close":true,
              "metadata":{
                "label":"Full Coverage Insurance Note",
                "description":"Use this to stop nancy from taking almost any of your stuff.",
              },
              "name":"advinsurancenote",
              "slot":6
            },
            {
              "stack":false,
              "weight":190
              ,"count":1,
              "close":true,
              "metadata":{
                "label":"iPhone",
                "description":"iphone Pro Max",
                "eSIMNumber":"14127912",
                "phoneID":"GKS202412EW1I",
                "phoneLang":"en"
              },
              "name":"iphone",
              "slot":7
            },
            {
              "stack":false,
              "close":false,
              "metadata":{
                "label":"BRAWL CHAIR",
                "components":[],
                "durability":99.1
              },
              "weight":3000,
              "count":1,
              "name":"WEAPON_CHAIR",
              "slot":8
            },
            {
              "stack":false,
              "close":false,
              "metadata":{
                "label":"Knife",
                "components":[],
                "durability":99.50000000000004
              },
              "weight":300,
              "count":1,
              "name":"WEAPON_KNIFE",
              "slot":9
            },
            {
              "stack":true,
              "close":true,
              "metadata":{
                "label":".50 AE",
              },
              "weight":2500,
              "count":100,
              "name":"ammo-50",
              "slot":10
            },
            {
              "stack":false,
              "weight":165,
              "count":1,
              "close":true,
              "metadata":{
                "label":"Medical Bag",
                "description":"A comprehensive medical kit for treating injuries and ailments. Warning: Does not include a medical degree.",
              },
              "name":"medbag",
              "slot":11
            },
            {"stack":true,
              "weight":2820,
              "count":94,
              "close":true,
              "metadata":{
                "label":"Basic Insurance Note",
                "description":"Use this to stop nancy from taking most of your stuff.",
              },
              "name":"insurancenote",
              "slot":12
            },
            {
              "stack":true,
              "weight":230,
              "count":2,
              "close":true,
              "metadata":{
                "label":"Lockpicks",
                "description":"If you lose your keys a lot this is very useful... Also useful to open your beers",
              },
              "name":"lockpick",
              "slot":13
            },
            {
              "stack":true,
              "close":true,
              "metadata":{
                "label":"9mm",
                quality: 87
              },
              "weight":105,
              "count":21,
              "name":"ammo-9",
              "slot":14
            },
            {
              "stack":true,
              "label":"Money",
              "weight":0,
              "count":10117459,
              "close":true,
              "description":"The root of all evil and payer of rent",
              "metadata":{

              },
              "name":"money",
              "slot":15
            },
            {
              "stack":true,
              "weight":225,
              "count":1,
              "close":true,
              "metadata":{
                "label":"Defibrillator",
                "description":"Bringing you back from the brink, one shock at a time!",
              },
              "name":"defib",
              "slot":16
            },
            {
              "stack":true,
              "weight":1,
              "count":1,
              "close":true,
              "metadata":{
                "label":"HotTub 1",
                "description":"A stylish Hot tub ",
              },
              "name":"hottub1",
              "slot":17
            },
            {
              "stack":true,
              "weight":30,
              "count":1,
              "close":true,
              "metadata":{
                "label":"CPR Training Certificate",
                "description":"This proves that you learned to perform CPR from a medical institution, probably at least.",
              },
              "name":"cprcertificate",
              "slot":18
            },
            {
              "close":true,
              "slot":23,
              "name":"badge_sasp",
              "metadata": {
                "nationality":"USA",
                "firstname":"Ashley",
                "sex":"F",
                "lastname":"Kelly",
                "mugShot":"none",
                "badge": {
                  "img":"badge_sasp",
                  "grade":"Commissioner"
                },
                "birthdate":"1990-3-12",
                "citizenid":"UP03GJVB",
                "cardtype":"badge_sasp",
                "description":"For official use by San Andreas State Police only",
                "label":"SASP Badge",
              },
              "weight":444,
              "stack":false,
              "count":1
            }
          ],
        },
        {
          id: 'test',
          type: 'container',
          slots: 25,
          label: 'Backpack',
          weight: 12500,
          maxWeight: 30000,
          items: [
            {
              slot: 14,
              name: 'ammo-rifle',
              weight: 100,
              metadata: {
                description: `name: Svetozar Miletic  \n Gender: Male`,
                ammo: 3,
                mustard: '60%',
                ketchup: '30%',
                mayo: '10%',
              },
              count: 15,
            },
            { slot: 15, name: 'WEAPON_ASSAULTRIFLE', weight: 2500, count: 1, metadata: { durability: 93, label: 'Assault Rifle', description: 'It\'s powerful and saw-like.' } },
            { },
            {  },
            {  },
            {  },
          ],
        },
        {
          id: 'Lockbox',
          type: 'container',
          slots: 10,
          label: 'Lockbox',
          weight: 3000,
          maxWeight: 5000,
          items: [
            {
              slot: 1,
              name: 'lockpick',
              count: 25,
              weight: 500,
              metadata: {
                description: 'Simple lockpick that breaks easily and can pick basic door locks',
              },
            },
          ],
        },
      ]
    },
  },
]);
debugData([
    {
        action: 'DamageCall',
        data: {
            head: {
                label:  "HEAD",
                limp:   false,
                level:  4,
                bleed:  4,
                type:   "shot",
            },
            neck: {
                label:  "NECK",
                limp:   false,
                level:  0,
                bleed:  0,
                type:   "",
            },
            spine: {
                label:  "SPINE",
                limp:   false,
                level:  0,
                bleed:  0,
                type:   "",
            },
            upper_body: {
                label:  "UPPER BODY",
                limp:   false,
                level:  0,
                bleed:  0,
                type:   "",
            },
            lower_body: {
                label:  "LOWER BODY",
                limp:   false,
                level:  0,
                bleed:  0,
                type:   "",
            },
            left_arm: {
                label:  "LEFT ARM",
                limp:   false,
                level:  3,
                bleed:  3,
                type:   "stabbed",
            },
            left_leg: {
                label:  "LEFT LEG",
                limp:   false,
                level:  1,
                bleed:  1,
                type:   "burned",
            },
            right_arm: {
                label:  "RIGHT ARM",
                limp:   true,
                level:  2,
                bleed:  2,
                type:   "beat",
            },
            right_leg: {
                label:  "RIGHT LEG",
                limp:   true,
                level:  0,
                bleed:  0,
                type:   "",
            },
        }
    }
]);

debugData([
  {
    action: 'init',
    data: {
      locale: {   
        ui_use: "Use",
        ui_give: "Give",
        ui_close: "Close",
        ui_drop: "Drop",
        ui_removeattachments: "Remove attachments",
        ui_copy: "Copy serial number",
        ui_durability: "Durability",
        ui_ammo: "Ammo",
        ui_serial: "Serial number",
        ui_components: "Components",
        ui_tint: "Tint",
        ui_usefulcontrols: "Useful Controls",
        ui_rmb: "Open item context menu",
        ui_ctrl_lmb: "Fast move a stack of items into another inventory",
        ui_shift_drag: "Split item quantity into half",
        ui_ctrl_shift_lmb: "Fast move half a stack of items into another inventory",
        ui_alt_lmb: "Fast use an item",
        ui_ctrl_c: "When hovering over a weapon, copies it's serial number",
        ui_remove_ammo: "Remove ammo",
      },
      playerData: {
        h1 : "Macie O'Flannigan",
        h2 : "KG30GV2S",
        t1 : "Date of Birth",
        b1 : "1969-04-20",
        t2 : "Motel Room",
        b2 : "2514",
        t3 : "Bank",
        b3 : "1716417",
        t4 : "Job",
        b4 : "Ambulance - Assistant HR [Off Duty]",
      },
      prefs: {
        PrimaryColor: '#9500ff',
      }
    },
  },
]);

const App: React.FC = () => {
  const dispatch = useAppDispatch();
  const manager = useDragDropManager();

  dispatch(setAdditionalMetadata([{
    metadata: 'quality',
    value: 'Quality',
  }]))

  dispatch(setAdditionalMetadata([{
    metadata: 'citizenid',
    value: 'CID',
  }]))

  dispatch(setAdditionalMetadata([{
    metadata: 'firstname',
    value: 'FirstName',
  }]))

  dispatch(setAdditionalMetadata([{
    metadata: 'lastname',
    value: 'Lastname',
  }]))

  dispatch(setAdditionalMetadata([{
    metadata: 'birthdate',
    value: 'DOB',
  }]))

  dispatch(setAdditionalMetadata([{
    metadata: 'sex',
    value: 'Gender',
  }]))

  dispatch(setAdditionalMetadata([{
    metadata: 'badge',
    value: 'Badge',
  }]))

  useNuiEvent<{
    locale: { [key: string]: string };
    items: typeof Items;
    playerData: typeof PlayerData;
    prefs: {[key: string]: any};
    playerInventories: Array<Inventory>;
    targetInventories: Array<Inventory>;
    imagepath: string;
    inventoryimagepath: string;
  }>('init', ({ locale, items, playerData, prefs, playerInventories, targetInventories, imagepath, inventoryimagepath }) => {
    for (const name in locale) Locale[name] = locale[name];
    for (const name in items) Items[name] = items[name];
    for (const name in playerData) PlayerData[name] = playerData[name];
    dispatch(setPreferences(prefs))
    setImagePath(imagepath);
    setInventoryImagePath(inventoryimagepath);
    dispatch(setupInventory({ playerInventories, targetInventories }));
  });
    
  useNuiEvent<{
      head:       bodyPart,
      neck:       bodyPart,
      spine:      bodyPart,
      upperBody:  bodyPart,
      lowerBody:  bodyPart,
      left_arm:   bodyPart,
      left_leg:   bodyPart,
      right_arm:  bodyPart,
      right_leg:  bodyPart,
    }>('DamageCall', (data) => {
      // console.log("DamageCall", data)
      dispatch(setDamage(data))
    });

  fetchNui('uiLoaded', {});

  useNuiEvent('closeInventory', () => {
    manager.dispatch({ type: 'dnd-core/END_DRAG' });
  });

  return (
    <div className="app-wrapper">
      <InventoryComponent />
      <DragPreview />
      <KeyPress />
    </div>
  );
};

addEventListener("dragstart", function(event) {
  event.preventDefault()
})

export default App;
