import { useAppSelector } from '../../store';
import { PlayerData } from '../../store/playerData';

const PlayerInfo: React.FC = () => {
  const dateFormatter = new Intl.DateTimeFormat('en-US', { month: 'long', day: 'numeric', year: 'numeric' });
  const currencyFormatter = new Intl.NumberFormat('en-US', { style: 'currency', currency: 'USD', maximumFractionDigits: 0 });
  const preferences = useAppSelector((state) => state.preferences)

  return (
    <div className="stats">
      <div className="info">
        <span className="info-bubble"></span>
        <span className="info-label">{PlayerData.h1}</span>
      </div>
      <div className="person">
        <span className={"name"+( preferences.StreamerMode ? " censor" : "")}>{PlayerData.h2}</span>
      </div>
      <div className="stat dob">
        <p className="label">{PlayerData.t1}</p>
        <p className={"value"+( preferences.StreamerMode ? " censor" : "")}>{PlayerData.b1}</p>
      </div>
      <div className="stat money right">
        <div>
          <p className="label">{PlayerData.t2}</p>
          <p className={"value"+(preferences.StreamerMode ? " censor" : "")}>{PlayerData.b2}</p>
        </div>
      </div>
      <div className="stat phone">
        <p className="label">{PlayerData.t3}</p>
        <p className={"value"+(preferences.StreamerMode ? " censor" : "")}>{currencyFormatter.format(PlayerData.b3)}</p>
      </div>
      <div className="stat home right">
        <p className="label">{PlayerData.t4}</p>
        <p className={"value"+(preferences.StreamerMode ? " censor" : "")}>{PlayerData.b4}</p>
      </div>
    </div>
  );
};

export default PlayerInfo;
