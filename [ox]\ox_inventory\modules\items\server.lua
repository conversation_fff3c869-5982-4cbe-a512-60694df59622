if not lib then return end

local Items = {}
local ItemList = require 'modules.items.shared' --[[@as table<string, OxServerItem>]]
local Utils = require 'modules.utils.server'

TriggerEvent('ox_inventory:itemList', ItemList)

Items.containers = require 'modules.items.containers'

-- Possible metadata when creating garbage
local trash = {
	{description = 'A discarded burger carton.', weight = 50, image = 'trash_burger'},
	{description = 'An empty soda can.', weight = 20, image = 'trash_can'},
	{description = 'A mouldy piece of bread.', weight = 70, image = 'trash_bread'},
	{description = 'An empty chips bag.', weight = 5, image = 'trash_chips'},
	{description = 'A slightly used pair of panties.', weight = 20, image = 'panties'},
	{description = 'An old rolled up newspaper.', weight = 200, image = 'WEAPON_ACIDPACKAGE'},
}

---@param _ table?
---@param name string?
---@return table?
local function getItem(_, name)
    if not name then return ItemList end

	if type(name) ~= 'string' then return end

    name = name:lower()

    if name:sub(0, 7) == 'weapon_' then
        name = name:upper()
    end

    return ItemList[name]
end

setmetatable(Items --[[@as table]], {
	__call = getItem
})

---@cast Items +fun(itemName: string): OxServerItem
---@cast Items +fun(): table<string, OxServerItem>

-- Support both names
exports('Items', function(item) return getItem(nil, item) end)
exports('ItemList', function(item) return getItem(nil, item) end)

local Inventory

CreateThread(function()
	Inventory = require 'modules.inventory.server'

	if not lib then return end

	if shared.framework == 'esx' then
		local success, items = pcall(MySQL.query.await, 'SELECT * FROM items')

		if success and items and next(items) then
			local dump = {}
			local count = 0

			for i = 1, #items do
				local item = items[i]

				if not ItemList[item.name] then
					item.close = item.closeonuse == nil and true or item.closeonuse
					item.stack = item.stackable == nil and true or item.stackable
					item.description = item.description
					item.weight = item.weight or 0
					dump[i] = item
					count += 1
				end
			end

			if table.type(dump) ~= "empty" then
				local file = {string.strtrim(LoadResourceFile(shared.resource, 'data/items.lua'))}
				file[1] = file[1]:gsub('}$', '')

				---@todo separate into functions for reusability, properly handle nil values
				local itemFormat = [[

	[%q] = {
		label = %q,
		weight = %s,
		stack = %s,
		close = %s,
		description = %q
	},
]]
				local fileSize = #file

				for _, item in pairs(dump) do
					if not ItemList[item.name] then
						fileSize += 1

						local itemStr = itemFormat:format(item.name, item.label, item.weight, item.stack, item.close, item.description and json.encode(item.description) or 'nil')
						-- temporary solution for nil values
						itemStr = itemStr:gsub('[%s]-[%w]+ = "?nil"?,?', '')
						file[fileSize] = itemStr
						ItemList[item.name] = item
					end
				end

				file[fileSize+1] = '}'

				SaveResourceFile(shared.resource, 'data/items.lua', table.concat(file), -1)
				shared.info(count, 'items have been copied from the database.')
				shared.info('You should restart the resource to load the new items.')
			end

			shared.info('Database contains', #items, 'items.')
		end

		Wait(500)

	elseif shared.framework == 'qb' then
		local QBCore = exports['qb-core']:GetCoreObject()
		local items = QBCore.Shared.Items

		if items and table.type(items) ~= 'empty' then
			local dump = {}
			local count = 0
			local ignoreList = {
				"weapon_",
				"pistol_",
				"pistol50_",
				"revolver_",
				"smg_",
				"combatpdw_",
				"shotgun_",
				"rifle_",
				"carbine_",
				"gusenberg_",
				"sniper_",
				"snipermax_",
				"tint_",
				"_ammo"
			}

			local function checkIgnoredNames(name)
				for i = 1, #ignoreList do
					if string.find(name, ignoreList[i]) then
						return true
					end
				end
				return false
			end

			for k, item in pairs(items) do
				-- Explain why this wouldn't be table to me, because numerous people have been getting "attempted to index number" here
				if type(item) == 'table' then
					-- Some people don't assign the name property, but it seemingly always matches the index anyway.
					if not item.name then item.name = k end

					if not ItemList[item.name] and not checkIgnoredNames(item.name) then
						item.close = item.shouldClose == nil and true or item.shouldClose
						item.stack = not item.unique and true
						item.description = item.description
						item.weight = item.weight or 0
						dump[k] = item
						count += 1
					end
				end
			end

			if table.type(dump) ~= 'empty' then
				local file = {string.strtrim(LoadResourceFile(shared.resource, 'data/items.lua'))}
				file[1] = file[1]:gsub('}$', '')

				---@todo separate into functions for reusability, properly handle nil values
				local itemFormat = [[

	[%q] = {
		label = %q,
		weight = %s,
		stack = %s,
		close = %s,
		description = %q,
		client = {
			status = {
				hunger = %s,
				thirst = %s,
				stress = %s
			},
			image = %q,
		}
	},
]]

				local fileSize = #file

				for _, item in pairs(dump) do
					if not ItemList[item.name] then
						fileSize += 1

						---@todo cry
						local itemStr = itemFormat:format(item.name, item.label, item.weight, item.stack, item.close, item.description or 'nil', item.hunger or 'nil', item.thirst or 'nil', item.stress or 'nil', item.image or 'nil')
						-- temporary solution for nil values
						itemStr = itemStr:gsub('[%s]-[%w]+ = "?nil"?,?', '')
						-- temporary solution for empty status table
						itemStr = itemStr:gsub('[%s]-[%w]+ = %{[%s]+%},?', '')
						-- temporary solution for empty client table
						itemStr = itemStr:gsub('[%s]-[%w]+ = %{[%s]+%},?', '')
						file[fileSize] = itemStr
						ItemList[item.name] = item
					end
				end

				file[fileSize+1] = '}'

				SaveResourceFile(shared.resource, 'data/items.lua', table.concat(file), -1)
				shared.info(count, 'items have been copied from the QBCore.Shared.Items.')
				shared.info('You should restart the resource to load the new items.')
			end
		end

		Wait(500)
	end

	local count = 0

	Wait(1000)

	for _ in pairs(ItemList) do
		count += 1
	end

	shared.info(('Inventory has loaded %d items'):format(count))
	collectgarbage('collect') -- clean up from initialisation
	shared.ready = true
end)

local function GenerateText(num)
	local str
	repeat str = {}
		for i = 1, num do str[i] = string.char(math.random(65, 90)) end
		str = table.concat(str)
	until str ~= 'POL' and str ~= 'EMS'
	return str
end

local function GenerateSerial(text)
	if text and text:len() > 3 then
		return text
	end

	return ('%s%s%s'):format(math.random(100000,999999), text == nil and GenerateText(3) or text, math.random(100000,999999))
end

local function setItemDurability(item, metadata)
	local degrade = item.degrade

	if degrade then
		metadata.durability = os.time()+(degrade * 60)
		metadata.degrade = degrade
	elseif item.durability then
		metadata.durability = 100
	end

	return metadata
end

local TriggerEventHooks = require 'modules.hooks.server'

---@param inv inventory
---@param item OxServerItem
---@param metadata any
---@param count number
---@return table, number
---Generates metadata for new items being created through AddItem, buyItem, etc.
function Items.Metadata(inv, item, metadata, count)
	if type(inv) ~= 'table' then inv = Inventory(inv) end
	if not item.weapon then metadata = not metadata and {} or type(metadata) == 'string' and {type=metadata} or metadata end
	if not count then count = 1 end

	---@cast metadata table<string, any>

	if item.weapon then
		if type(metadata) ~= 'table' then metadata = {} end
		if not metadata.durability then metadata.durability = 100 end
		if not metadata.ammo and item.ammoname then metadata.ammo = 0 end
		if not metadata.components then metadata.components = {} end

		if metadata.registered ~= false and (metadata.ammo or item.name == 'WEAPON_STUNGUN') then
			local registered = type(metadata.registered) == 'string' and metadata.registered or inv?.player?.name
			metadata.registered = registered
			metadata.serial = GenerateSerial(metadata.serial)
		end

		if item.hash == `WEAPON_PETROLCAN` or item.hash == `WEAPON_HAZARDCAN` or item.hash == `WEAPON_FERTILIZERCAN` or item.hash == `WEAPON_FIREEXTINGUISHER` then
			metadata.ammo = metadata.durability
		end
	else
		local container = Items.containers[item.name]

		if container then
			count = 1
			metadata.container = metadata.container or GenerateText(3)..os.time()
			metadata.size = container.size
		elseif not next(metadata) then
			if item.name == 'identification' then
				count = 1
				metadata = {
					type = inv.player.name,
					description = locale('identification', (inv.player.sex) and locale('male') or locale('female'), inv.player.dateofbirth)
				}
			elseif item.name == 'garbage' then
				local trashType = trash[math.random(1, #trash)]
				metadata.image = trashType.image
				metadata.weight = trashType.weight
				metadata.description = trashType.description
			end
		end

		if not metadata.durability then
			metadata = setItemDurability(ItemList[item.name], metadata)
		end
	end

	if count > 1 and not item.stack then
		count = 1
	end

	local response = TriggerEventHooks('createItem', {
		inventoryId = inv and inv.id,
		metadata = metadata,
		item = item,
		count = count,
	})

	if type(response) == 'table' then
		metadata = response
	end

	if metadata.imageurl and Utils.IsValidImageUrl then
		if Utils.IsValidImageUrl(metadata.imageurl) then
			Utils.DiscordEmbed('Valid image URL', ('Created item "%s" (%s) with valid url in "%s".\n%s\nid: %s\nowner: %s'):format(metadata.label or item.label, item.name, inv.label, metadata.imageurl, inv.id, inv.owner, metadata.imageurl), metadata.imageurl, 65280)
		else
			Utils.DiscordEmbed('Invalid image URL', ('Created item "%s" (%s) with invalid url in "%s".\n%s\nid: %s\nowner: %s'):format(metadata.label or item.label, item.name, inv.label, metadata.imageurl, inv.id, inv.owner, metadata.imageurl), metadata.imageurl, 16711680)
			metadata.imageurl = nil
		end
	end

	return metadata, count
end

---@param metadata table<string, any>
---@param item OxServerItem
---@param name string
---@param ostime number
---Validate (and in some cases convert) item metadata when an inventory is being loaded.
function Items.CheckMetadata(metadata, item, name, ostime)
	if metadata.bag then
		metadata.container = metadata.bag
		metadata.size = Items.containers[name]?.size or {5, 1000}
		metadata.bag = nil
	end

	local durability = metadata.durability

	if durability then
		if durability < 0 or durability > 100 and ostime >= durability then
			metadata.durability = 0
		end
	else
		metadata = setItemDurability(item, metadata)
	end

	if item.weapon then
		if metadata.components then
			if table.type(metadata.components) == 'array' then
				for i = #metadata.components, 1, -1 do
					if not ItemList[metadata.components[i]] then
						table.remove(metadata.components, i)
					end
				end
			else
				local components = {}
				local size = 0

				for _, component in pairs(metadata.components) do
					if component and ItemList[component] then
						size += 1
						components[size] = component
					end
				end

				metadata.components = components
			end
		end

		if metadata.serial and item.throwable then
			metadata.serial = nil
		end

		if metadata.specialAmmo and type(metadata.specialAmmo) ~= 'string' then
			metadata.specialAmmo = nil
		end
	end

	return metadata
end

---Update item durability, and call `Inventory.RemoveItem` if it was removed from decay.
---@param inv OxInventory
---@param slot SlotWithItem
---@param item OxServerItem
---@param value? number
---@param ostime? number
---@return boolean? removed
function Items.UpdateDurability(inv, slot, item, value, ostime)
    local durability = slot.metadata.durability or value

    if not durability then return end

    if value then
        durability = value
    elseif ostime and durability > 100 and ostime >= durability then
        durability = 0
    end

    if item.decay and durability == 0 then
        return Inventory.RemoveItem(inv, slot.name, slot.count, nil, slot.slot)
    end

    if slot.metadata.durability == durability then return end

    inv.changed = true
    slot.metadata.durability = durability

    inv:syncSlotsWithClients({
        {
            item = slot,
            inventory = inv.id
        }
    }, true)
end

local function Item(name, cb)
	local item = ItemList[name]

	if item and not item.cb then
		item.cb = cb
	end
end


RegisterServerEvent('ox_inventory:refillItem', function(_slot)
    local src = source
    local item = exports.ox_inventory:GetSlot(src, _slot)

    if not item or not item.metadata or not item.metadata.durability then
        TriggerClientEvent('ox_lib:notify', src, {
            description = 'Cannot refill this item',
            type = 'error'
        })
        return
    end

    local currentDurability = item.metadata.durability
    local maxDurability = 100 -- Adjust based on your item logic
    if currentDurability >= maxDurability then
        TriggerClientEvent('ox_lib:notify', src, {
            description = 'Item is already at maximum durability',
            type = 'error'
        })
        return
    end

    local needed = maxDurability - currentDurability
    local itemName = item.name
    local inventory = exports.ox_inventory:GetInventory(src)
    local donorSlot

    local items = inventory.items
    if not items then return end

    for k, v in pairs(items) do
        if type(v) == 'table' and v.name == itemName and v.slot ~= _slot and v.metadata and v.metadata.durability and v.metadata.durability > 0 then
            donorSlot = v.slot
            break
        end
    end

    if not donorSlot then
        TriggerClientEvent('ox_lib:notify', src, {
            description = 'No compatible items found to refill with',
            type = 'error'
        })
        return
    end

    local donorItem = exports.ox_inventory:GetSlot(src, donorSlot)
    local donorDurability = donorItem.metadata.durability

    if donorDurability <= needed then
        -- Use up entire donor
        item.metadata.durability = currentDurability + donorDurability
        exports.ox_inventory:RemoveItem(src, donorItem.name, 1, donorItem.metadata, donorSlot)
        TriggerClientEvent('ox_lib:notify', src, {
            description = 'Successfully refilled item using entire donor',
            type = 'success'
        })
    else
        -- Partially use donor
        item.metadata.durability = maxDurability
        donorItem.metadata.durability = donorDurability - needed
        exports.ox_inventory:SetMetadata(src, donorItem.slot, donorItem.metadata)
        TriggerClientEvent('ox_lib:notify', src, {
            description = 'Successfully refilled item',
            type = 'success'
        })
    end

    exports.ox_inventory:SetMetadata(src, item.slot, item.metadata)
end)


local agraphicNovelConsume = function(source, slotid)
	local slot = Inventory.GetSlot(source, slotid)
	local inv = Inventory(source)
	if not slot or not inv then return false end
	-- print(json.encode(slot))
    local durability = slot.metadata.durability

    if not durability then durability = 100 end
	-- print(durability)

    durability = durability - 50
	-- print(durability)

    if durability <= 0 then
		-- print('delete item')
        return Inventory.RemoveItem(source, slot.name, slot.count, nil, slot.slot)
    end

    if slot.metadata.durability == durability then return end

    inv.changed = true
    slot.metadata.durability = durability

    inv:syncSlotsWithClients({
        {
            item = slot,
            inventory = inv.id
        }
    }, true)
	return true
end
RegisterNetEvent('ox_inventory:agraphicNovelConsume', agraphicNovelConsume)

RegisterNetEvent('ox_inventory:agraphicNovelTeleport', function(source, slot, coords)
	if agraphicNovelConsume(source, slot) then
		TriggerClientEvent('ox_inventory:ClientagraphicNovelTeleport', source, coords)
	end
end)

RegisterServerEvent('ox_inventory:renameItem')
AddEventHandler('ox_inventory:renameItem', function(_slot,_itemname)
    local src = source
	local item = exports.ox_inventory:GetSlot(src, _slot)
	item.metadata.label = _itemname
	exports.ox_inventory:SetMetadata(src, item.slot, item.metadata)
end)

-----------------------------------------------------------------------------------------------
-- Serverside item functions
-----------------------------------------------------------------------------------------------

-- Item('testburger', function(event, item, inventory, slot, data)
-- 	if event == 'usingItem' then
-- 		if Inventory.GetItem(inventory, item, inventory.items[slot].metadata, true) > 0 then
-- 			-- if we return false here, we can cancel item use
-- 			return {
-- 				inventory.label, event, 'external item use poggies'
-- 			}
-- 		end

-- 	elseif event == 'usedItem' then
-- 		print(('%s just ate a %s from slot %s'):format(inventory.label, item.label, slot))

-- 	elseif event == 'buying' then
-- 		print(data.id, data.coords, json.encode(data.items[slot], {indent=true}))
-- 	end
-- end)


local function getJointName(itemName)
    local jointName = 'regweed_joint'

    if itemName == 'regweed_bud' then
        jointName = 'regweed_joint'
    elseif itemName == 'bananakush_bud' then
        jointName = 'bananakush_joint'
    elseif itemName == 'purplehaze_bud' then
        jointName = 'purplehaze_joint'
    elseif itemName == 'bluedream_bud' then
        jointName = 'bluedream_joint'
    else
        print('Unknown item name: ' .. itemName)
    end

    return jointName
end

local function getBagName(itemName)
    local bagName = 'regweed_bag'

    if itemName == 'regweed_bud' then
        bagName = 'regweed_bag'
    elseif itemName == 'bananakush_bud' then
        bagName = 'bananakush_bag'
    elseif itemName == 'purplehaze_bud' then
        bagName = 'purplehaze_bag'
    elseif itemName == 'bluedream_bud' then
        bagName = 'bluedream_bag'
    else
        print('Unknown item name: ' .. itemName)
    end

    return bagName
end


-- Used for Lation Weed
RegisterServerEvent('weedRoll:server')
AddEventHandler('weedRoll:server', function(itemName,count)
    local src = source
	local jointName = getJointName(itemName)
	local hasRollingPaper = exports.ox_inventory:Search(src, 'count', 'rolling_paper') >= count
	if hasRollingPaper then 
		local success = exports.ox_inventory:RemoveItem(src, itemName, count) and exports.ox_inventory:RemoveItem(src, 'rolling_paper', count)
		if success then 
			exports.ox_inventory:AddItem(src, jointName, count)
			TriggerClientEvent('ox_lib:notify', src, { description = 'Rolled a joint', type = 'info', duration = 3500 })
		else
			TriggerClientEvent('ox_lib:notify', src, { description = 'You do not have the required items to do this.', type = 'error', duration = 3500 })
		end
	else
		TriggerClientEvent('ox_lib:notify', src, { description = 'You do not have the required items. Make sure you have some rolling paper!', type = 'error', duration = 3500 })
	end
end)

RegisterServerEvent('weedBag:server')
AddEventHandler('weedBag:server', function(itemName,count)
    local src = source
	local bagName = getBagName(itemName)
	local hasEmptyBag = exports.ox_inventory:Search(src, 'count', 'empty_weed_bag') >= count
	if hasEmptyBag then 
		local success = exports.ox_inventory:RemoveItem(src, itemName, count) and exports.ox_inventory:RemoveItem(src, 'empty_weed_bag', count)
		if success then 
			exports.ox_inventory:AddItem(src, bagName, count)
			TriggerClientEvent('ox_lib:notify', src, { description = 'Bagged up Weed', type = 'info', duration = 3500 })
		else
			TriggerClientEvent('ox_lib:notify', src, { description = 'You do not have the required items to do this.', type = 'error', duration = 3500 })
		end
	else
		TriggerClientEvent('ox_lib:notify', src, { description = 'You do not have the required items. Make sure you have some empty weed bags!', type = 'error', duration = 3500 })
	end
end)

--End of being used for Lation Weed
-----------------------------------------------------------------------------------------------

-- Reuseable combine part function.--
RegisterServerEvent('combineParts:server')
AddEventHandler('combineParts:server', function(partNameRequired,amountRequired,itemToGive,amountTogive)
	local partsNeeded = amountRequired
	local amountToGive = amountTogive
	local partNameRequired = partNameRequired
	local itemToGive = itemToGive
    local src = source
	local hasParts = exports.ox_inventory:Search(src, 'count', partNameRequired) >= partsNeeded
	if hasParts then 
		local success = exports.ox_inventory:RemoveItem(src, partNameRequired, partsNeeded)
		if success then 
			exports.ox_inventory:AddItem(src, itemToGive, amountToGive)
			TriggerClientEvent('ox_lib:notify', src, { description = 'Combined your '..partNameRequired' into '..itemToGive ' ', type = 'info', duration = 3500 })
		else
			TriggerClientEvent('ox_lib:notify', src, { description = 'You do not have the required items to do this.', type = 'error', duration = 3500 })
		end
	else
		TriggerClientEvent('ox_lib:notify', src, { description = 'You do not have the required items.', type = 'error', duration = 3500 })
	end
end)


RegisterServerEvent('combineTwoParts:server')
AddEventHandler('combineTwoParts:server', function(part1NameRequired, amount1Required, part2NameRequired, amount2Required, itemToGive, amountToGive)
	local src = source
	local part1Needed = amount1Required
	local part2Needed = amount2Required
	local part1Name = part1NameRequired
	local part2Name = part2NameRequired
	local itemToGive = itemToGive
	local amountToGive = amountToGive

	-- Check if parts are weapons and verify their components
	local function checkWeaponComponents(itemName)
		if string.sub(itemName, 1, 7) == "WEAPON_" then
			local items = exports.ox_inventory:Search(src, 'slots', itemName)
			if items then
				for _, item in pairs(items) do
					if item.metadata and item.metadata.components and #item.metadata.components > 0 then
						-- Weapon has components attached, don't allow combining
						return false
					end
				end
			end
		end
		return true
	end

	-- Check both items for weapon components
	if not checkWeaponComponents(part1Name) then
		TriggerClientEvent('ox_lib:notify', src, { description = 'Remove all attachments from '..part1Name..' first', type = 'error', duration = 3500 })
		return
	end

	if not checkWeaponComponents(part2Name) then
		TriggerClientEvent('ox_lib:notify', src, { description = 'Remove all attachments from '..part2Name..' first', type = 'error', duration = 3500 })
		return
	end

	local hasPart1 = exports.ox_inventory:Search(src, 'count', part1Name) >= part1Needed
	local hasPart2 = exports.ox_inventory:Search(src, 'count', part2Name) >= part2Needed

	if hasPart1 and hasPart2 then 
		local success1 = exports.ox_inventory:RemoveItem(src, part1Name, part1Needed)
		local success2 = exports.ox_inventory:RemoveItem(src, part2Name, part2Needed)

		if success1 and success2 then
			exports.ox_inventory:AddItem(src, itemToGive, amountToGive)
			TriggerClientEvent('ox_lib:notify', src, { description = 'Combined your '..part1Name..' and '..part2Name..' into '..itemToGive, type = 'info', duration = 3500 })
		else
			TriggerClientEvent('ox_lib:notify', src, { description = 'You do not have the required items to do this.', type = 'error', duration = 3500 })
		end
	else
		TriggerClientEvent('ox_lib:notify', src, { description = 'You do not have the required items.', type = 'error', duration = 3500 })
	end
end)


RegisterServerEvent('combineThreeParts:server')
AddEventHandler('combineThreeParts:server', function(part1NameRequired, amount1Required, part2NameRequired, amount2Required, part3NameRequired, amount3Required, itemToGive, amountToGive)
	local src = source
	local part1Needed = amount1Required
	local part2Needed = amount2Required
	local part3Needed = amount3Required
	local part1Name = part1NameRequired
	local part2Name = part2NameRequired
	local part3Name = part3NameRequired

	local function checkWeaponComponents(itemName)
		if string.sub(itemName, 1, 7) == "WEAPON_" then
			local items = exports.ox_inventory:Search(src, 'slots', itemName)
			if items then
				for _, item in pairs(items) do
					if item.metadata and item.metadata.components and #item.metadata.components > 0 then
						return false
					end
				end
			end
		end
		return true
	end

	if not checkWeaponComponents(part1Name) then
		TriggerClientEvent('ox_lib:notify', src, { description = 'Remove all attachments from '..part1Name..' first', type = 'error', duration = 3500 })
		return
	end

	if not checkWeaponComponents(part2Name) then
		TriggerClientEvent('ox_lib:notify', src, { description = 'Remove all attachments from '..part2Name..' first', type = 'error', duration = 3500 })
		return
	end

	if not checkWeaponComponents(part3Name) then
		TriggerClientEvent('ox_lib:notify', src, { description = 'Remove all attachments from '..part3Name..' first', type = 'error', duration = 3500 })
		return
	end

	local hasPart1 = exports.ox_inventory:Search(src, 'count', part1Name) >= part1Needed
	local hasPart2 = exports.ox_inventory:Search(src, 'count', part2Name) >= part2Needed
	local hasPart3 = exports.ox_inventory:Search(src, 'count', part3Name) >= part3Needed

	if hasPart1 and hasPart2 and hasPart3 then 
		local success1 = exports.ox_inventory:RemoveItem(src, part1Name, part1Needed)
		local success2 = exports.ox_inventory:RemoveItem(src, part2Name, part2Needed)
		local success3 = exports.ox_inventory:RemoveItem(src, part3Name, part3Needed)

		if success1 and success2 and success3 then
			exports.ox_inventory:AddItem(src, itemToGive, amountToGive)
			TriggerClientEvent('ox_lib:notify', src, { description = 'Combined your '..part1Name..', '..part2Name..' and '..part3Name..' into '..itemToGive, type = 'info', duration = 3500 })
		else
			TriggerClientEvent('ox_lib:notify', src, { description = 'You do not have the required items to do this.', type = 'error', duration = 3500 })
		end
	else
		TriggerClientEvent('ox_lib:notify', src, { description = 'You do not have the required items.', type = 'error', duration = 3500 })
	end
end)




RegisterServerEvent('uncombineTwoParts:server')
AddEventHandler('uncombineTwoParts:server', function(itemToRemove, part1Name, part2Name, amountToGive)
    local src = source

    local items = exports.ox_inventory:Search(src, 'slots', itemToRemove) or {}

    local fullDurabilityItem = nil
    for _, item in ipairs(items) do
        local durability = item.metadata and item.metadata.durability or 100
        if durability >= 100 then
            fullDurabilityItem = item
            break -- Found a valid item, exit loop
        end
    end

    if not fullDurabilityItem then
        TriggerClientEvent('ox_lib:notify', src, { 
            description = 'You cannot uncombine a damaged item. Repair it first.', 
            type = 'error', 
            duration = 3500 
        })
        return
    end

    local function checkWeaponComponents(metadata)
        if string.sub(itemToRemove, 1, 7) == "WEAPON_" then
            if metadata and metadata.components and #metadata.components > 0 then
                return false -- Has attachments
            end
        end
        return true -- No attachments or not a weapon
    end

    if not checkWeaponComponents(fullDurabilityItem.metadata) then
        TriggerClientEvent('ox_lib:notify', src, { 
            description = 'Remove all attachments from '..part1Name..' first', 
            type = 'error', 
            duration = 3500 
        })
        return
    end

    exports.ox_inventory:RemoveItem(src, itemToRemove, 1, fullDurabilityItem.metadata)
    exports.ox_inventory:AddItem(src, part1Name, amountToGive)
    exports.ox_inventory:AddItem(src, part2Name, amountToGive)

    TriggerClientEvent('ox_lib:notify', src, { 
        description = 'Uncombined your ' .. itemToRemove .. ' back into ' .. part1Name .. ' and ' .. part2Name, 
        type = 'info', 
        duration = 3500 
    })
end)
-----------------------------------------------------------------------------------------------
---[[Clothing Bag]]---
RegisterNetEvent('clothing_bag:updateMetadata')
AddEventHandler('clothing_bag:updateMetadata', function(slot, metadata)
    local source = source
    local inventory = exports.ox_inventory:GetInventory(source)
    
    if inventory then
        exports.ox_inventory:SetMetadata(source, slot, metadata)
    end
end)
---[[End Clothing Bag]]---

---[[Weapon Parts Crate]]---
RegisterNetEvent('ox_inventory:giveWeaponParts', function(weaponParts)
    local source = source
    
    -- Get 2 unique random indices first
    local index1 = math.random(1, #weaponParts)
    local index2 = index1
    while index2 == index1 do
        index2 = math.random(1, #weaponParts)
    end
    
    -- Check chance and give items
    local selectedParts = {weaponParts[index1], weaponParts[index2]}
    for _, part in ipairs(selectedParts) do
        if math.random(1, 100) <= part.chance then
            exports.ox_inventory:AddItem(source, part.item, part.amount)
        end
    end
    
    -- Remove the crate
    exports.ox_inventory:RemoveItem(source, 'crate_weapon', 1)
end)
---[[End Weapon Parts Crate]]---


lib.callback.register('ox_inventory:updateWeaponDurability', function(source, slot, durability)
    local inventory = Inventory(source)
    if not inventory then return false end

    local success = Inventory.SetDurability(inventory, slot, durability)
    return success
end)





-----------------------------------------------------------------------------------------------
-----------------------------------------------------------------------------------------------
return Items
