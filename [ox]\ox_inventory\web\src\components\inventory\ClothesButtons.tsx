import React, { useEffect, useMemo, useRef, useState } from 'react';
import { Inventory, Slot } from '../../typings';
import InventorySlot from './InventorySlot';
import { getTotalWeight } from '../../helpers';
import { useAppSelector } from '../../store';
import { useIntersection } from '../../hooks/useIntersection';
import BodyIcon from '/inventory-images/body.png';
import { inventoryimagepath } from '../../store/imagepath';
import { selectPlayerInventory } from '../../store/inventory';
import InventoryGrid from './InventoryGrid';
import { fetchNui } from '../../utils/fetchNui';


const ClothesButtons: React.FC<{}> = ({ }) => {
  const [page, setPage] = useState(0);
  const [filter, setFilter] = useState('');
  const containerRef = useRef(null);
  const { ref, entry } = useIntersection({ threshold: 0.5 });
  const isBusy = useAppSelector((state) => state.inventory.isBusy);

  const clothesMenu = ['hat', 'visor', 'hair', 'mask', 'glasses', 'ear', 'shirt', 'pants', 'vest', 'gloves', 'watch', 'bracelet', 'shoes', 'necklace', 'bag']

  useEffect(() => {
    if (entry && entry.isIntersecting) {
      setPage((prev) => ++prev);
    }
  }, [entry]);

  const getIcon = (img: string) => {
    return inventoryimagepath + '/' + img + '.png'
  }

  const handleClick = (button: string) => {
    //console.log(button);
    fetchNui('clothesToggle', {toggle: button})
  }

  const handleContext = (button: string) => {
    event.preventDefault();
  }
  

  return (
    <>
      <div className='inventory-grid-wrapper' style={{ pointerEvents: isBusy ? 'none' : 'auto' }}>
        <div className={'inventory-grid-header'}>
          {<img className="inventory-grid-header-icon" src={inventoryimagepath + '/player.svg'} alt="player-inventory" />}
            <span className="title-weight">
              <span className="title">Body</span>
            </span>
        </div>
        <div className='inventory-grid-hotbar-inventory'>
          <div className='inventory-grid-container-left' ref={containerRef}>
            {clothesMenu.map((item, index) => (
                <div
                  onContextMenu={() => handleContext(item)}
                  onClick={() => handleClick(item)}
                  className="inventory-slot"
                  key={item}
                >
                  <div className='item-slot-wrapper'>
                    <img className="image" src={getIcon(item)} />
                  </div>
                </div>
            ))}
          </div>
        </div>
      </div>
    </>
  );
};

export default ClothesButtons;
