import { fetchNui } from "../utils/fetchNui";

import { createSlice, PayloadAction } from '@reduxjs/toolkit';

interface PreferencesState {
  PrimaryColor: string,
  StreamerMode: boolean,
  BodyColor: boolean,
}

const initialState: PreferencesState = {
  PrimaryColor: '#9500ff',
  StreamerMode: false,
  BodyColor: false,
};

export const preferencesSlice = createSlice({
  name: 'preferences',
  initialState,
  reducers: {
    setPreferences(state, action: PayloadAction<{[key: string]: any}>) {
      for (const name in action.payload) {
        state[name] = action.payload[name];
        if (name == 'PrimaryColor') {
          var primaryColor = hexToHsl(state.PrimaryColor)
          document.body.style.setProperty('--sectionTitleColor', state[name])
          document.getElementById('root').style.setProperty('--sectionTitleColor', state[name])
          document.body.style.setProperty('--sectionTitleHue', hexToHsl(state.PrimaryColor)[0]+'deg')
          document.getElementById('root').style.setProperty('--sectionTitleHue', hexToHsl(state.PrimaryColor)[0]+'deg')
          document.body.style.setProperty('--sectionTitleSat', hexToHsl(state.PrimaryColor)[1]+'%')
          document.getElementById('root').style.setProperty('--sectionTitleSat', hexToHsl(state.PrimaryColor)[1]+'%')
          document.body.style.setProperty('--sectionTitleLum', hexToHsl(state.PrimaryColor)[2]+'%')
          document.getElementById('root').style.setProperty('--sectionTitleLum', hexToHsl(state.PrimaryColor)[2]+'%')
        }
      }
    }
  }
});

export const { setPreferences } = preferencesSlice.actions;

export default preferencesSlice.reducer;

function hexToHsl(_in: String){
  var r = parseInt(_in.substr(1,2), 16);
  var g = parseInt(_in.substr(3,2), 16);
  var b = parseInt(_in.substr(5,2), 16);
  r /= 255, g /= 255, b /= 255;
  var max = Math.max(r, g, b), min = Math.min(r, g, b);
  var h, s, l = (max + min) / 2;

  if(max == min){
      h = s = 0; // achromatic
  }else{
      var d = max - min;
      s = l > 0.5 ? d / (2 - max - min) : d / (max + min);
      switch(max){
          case r: h = (g - b) / d + (g < b ? 6 : 0); break;
          case g: h = (b - r) / d + 2; break;
          case b: h = (r - g) / d + 4; break;
      }
      h /= 6;
  }

  return [h*360, s*100, l*100];
}

export function updateClientPreferences(preferences) {
  fetchNui('setPreferences', {PrimaryColor: preferences.PrimaryColor, StreamerMode: preferences.StreamerMode, BodyColor: preferences.BodyColor})
}
