import { createSlice, PayloadAction } from '@reduxjs/toolkit';
import { Inventory, SlotWithItem } from '../typings';

interface ContextMenuState {
  coords: {
    x: number;
    y: number;
  } | null;
  item: SlotWithItem | null;
  inventoryid: Inventory['id'];
  inventoryType: Inventory['type'];
}

const initialState: ContextMenuState = {
  coords: null,
  item: null,
  inventoryid: null,
  inventoryType: null,
};

export const contextMenuSlice = createSlice({
  name: 'contextMenu',
  initialState,
  reducers: {
    openContextMenu(state, action: PayloadAction<{ item: SlotWithItem; coords: { x: number; y: number }; inventoryid: Inventory['id']; inventoryType: Inventory['type'] }>) {
      state.coords = action.payload.coords;
      state.item = action.payload.item;
      state.inventoryid = action.payload.inventoryid;
      state.inventoryType = action.payload.inventoryType
    },
    closeContextMenu(state) {
      state.coords = null;
    },
  },
});

export const { openContextMenu, closeContextMenu } = contextMenuSlice.actions;

export default contextMenuSlice.reducer;
