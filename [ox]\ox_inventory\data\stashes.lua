return {
	{
		coords = false,
		target = {
			loc = vec3(480.8, -1006.59, 30.69),
			length = 1.2,
			width = 1.2,
			heading = 0,
			minZ=27.89,
  			maxZ=31.89,
			label = 'Open personal locker'
		},
		name = 'policelocker',
		label = 'Personal locker',
		owner = true,
		slots = 70,
		weight = 100000,
		groups = shared.police
	},

	{
		coords = false,
		target = {
			loc = vec3(475.25, -989.98, 30.69),
			length = 1.2,
			width = 1.2,
			heading = 0,
			minZ=28.29,
  			maxZ=32.29,
			label = 'Open personal locker'
		},
		name = 'policelocker',
		label = 'Personal locker',
		owner = true,
		slots = 70,
		weight = 100000,
		groups = shared.police
	},
	{
		coords = false,
		target = {
			loc = vec3(460.80, -999.08, 30.69),
			length = 1.2,
			width = 1.2,
			heading = 0,
			minZ=28.29,
  			maxZ=30.29,
			label = 'Shared Locker'
		},
		name = 'policelockerShared',
		label = 'Shared PD locker',
		owner = false,
		slots = 100,
		weight = 350000,
		groups = shared.police
	},

	-- {
	-- 	coords = vec3(301.3, -600.23, 43.28),
	-- 	target = {
	-- 		loc = vec3(301.82, -600.99, 43.29),
	-- 		length = 0.6,
	-- 		width = 1.8,
	-- 		heading = 340,
	-- 		minZ = 43.34,
	-- 		maxZ = 44.74,
	-- 		label = 'Open personal locker'
	-- 	},
	-- 	name = 'emslocker',
	-- 	label = 'Personal Locker',
	-- 	owner = true,
	-- 	slots = 70,
	-- 	weight = 70000,
	-- 	groups = {['ambulance'] = 0}
	-- },
}
