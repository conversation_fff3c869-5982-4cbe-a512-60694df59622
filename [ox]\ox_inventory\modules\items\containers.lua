local containers = {}

local weps = require 'data.weapons'

---@class ItemContainerProperties
---@field slots number
---@field maxWeight number
---@field groups? table<string, number>
---@field whitelist? table<string, true> | string[]
---@field blacklist? table<string, true> | string[]

local function arrayToSet(tbl)
	local size = #tbl
	local set = table.create(0, size)

	for i = 1, size do
		set[tbl[i]] = true
	end

	return set
end

---Registers items with itemName as containers (i.e. backpacks, wallets).
---@param itemName string
---@param properties ItemContainerProperties
---@todo Rework containers for flexibility, improved data structure; then export this method.
local function setContainerProperties(itemName, properties)
	local blacklist, whitelist = properties.blacklist, properties.whitelist

	if blacklist then
		local tableType = table.type(blacklist)

		if tableType == 'array' then
			blacklist = arrayToSet(blacklist)
		elseif tableType ~= 'hash' then
			TypeError('blacklist', 'table', type(blacklist))
		end
	end

	if whitelist then
		local tableType = table.type(whitelist)

		if tableType == 'array' then 
			whitelist = arrayToSet(whitelist)
		elseif tableType ~= 'hash' then
			TypeError('whitelist', 'table', type(whitelist))
		end
	end

	containers[itemName] = {
		size = { properties.slots, properties.maxWeight },
		blacklist = blacklist,
		whitelist = whitelist,
		groups = properties.groups
	}
end

setContainerProperties('paperbag', {
	slots = 5,
	maxWeight = 1000,
	blacklist = { 'testburger' }
})

setContainerProperties('pizzabox', {
	slots = 5,
	maxWeight = 1000,
	whitelist = { 'pizza' }
})

setContainerProperties('smallbackpack', {
	slots = 15,
	maxWeight = 20000,
	blacklist = { 'testburger', 'money_bag', 'money_crate', 'ls_oxygen_tank' }
})

setContainerProperties('backpack', {
	slots = 25,
	maxWeight = 40000,
	blacklist = { 'testburger', 'money_bag', 'money_crate', 'ls_oxygen_tank' }
})

setContainerProperties('emsbag', {
	slots = 35,
	maxWeight = 60000,
	groups = { ['ambulance'] = 0, ['police'] = 0 },
	blacklist = { 'testburger', 'money_bag', 'money_crate' }
})

setContainerProperties('pdbag', {
	slots = 35,
	maxWeight = 60000,
	groups = { ['police'] = 0 },
	blacklist = { 'testburger', 'money_bag', 'money_crate' }
})

setContainerProperties('private', {
	slots = 0,
	maxWeight = 0,
	blacklist = { 
		'WEAPON_BATTLERIFLE', 'WEAPON_SNOWLAUNCHER', 'WEAPON_TECPISTOL', 'WEAPON_ADVANCEDRIFLE', 'WEAPON_APPISTOL',
		'WEAPON_ASSAULTRIFLE', 'WEAPON_ASSAULTRIFLE_MK2', 'WEAPON_ASSAULTSHOTGUN', 'WEAPON_ASSAULTSMG', 'WEAPON_BALL',
		'WEAPON_BAT', 'WEAPON_BATTLEAXE', 'WEAPON_BOTTLE', 'WEAPON_BULLPUPRIFLE', 'WEAPON_BULLPUPRIFLE_MK2', 'WEAPON_BULLPUPSHOTGUN',
		'WEAPON_BZGAS', 'WEAPON_CARBINERIFLE', 'WEAPON_CARBINERIFLE_MK2', 'WEAPON_CERAMICPISTOL', 'WEAPON_PISTOLXM3', 'WEAPON_COMBATMG',
		'WEAPON_COMBATMG_MK2', 'WEAPON_COMBATPDW', 'WEAPON_COMBATPISTOL', 'WEAPON_COMBATSHOTGUN', 'WEAPON_COMPACTLAUNCHER',
		'WEAPON_COMPACTRIFLE', 'WEAPON_CROWBAR', 'WEAPON_DAGGER', 'WEAPON_DBSHOTGUN', 'WEAPON_DOUBLEACTION', 'WEAPON_EMPLAUNCHER',
		'WEAPON_FIREEXTINGUISHER', 'WEAPON_FIREWORK', 'WEAPON_FLARE', 'WEAPON_FLAREGUN', 'WEAPON_FLASHLIGHT', 'WEAPON_GOLFCLUB',
		'WEAPON_GRENADE', 'WEAPON_GRENADELAUNCHER', 'WEAPON_GUSENBERG', 'WEAPON_HAMMER', 'WEAPON_HATCHET', 'WEAPON_HEAVYRIFLE',
		'WEAPON_HAZARDCAN', 'WEAPON_METALDETECTOR', 'WEAPON_HOMINGLAUNCHER', 'WEAPON_FERTILIZERCAN', 'WEAPON_HEAVYPISTOL',
		'WEAPON_HEAVYSHOTGUN', 'WEAPON_HEAVYSNIPER', 'WEAPON_HEAVYSNIPER_MK2', 'WEAPON_KNIFE', 'WEAPON_KNUCKLE', 'WEAPON_MACHETE',
		'WEAPON_MACHINEPISTOL', 'WEAPON_MARKSMANPISTOL', 'WEAPON_MARKSMANRIFLE', 'WEAPON_MARKSMANRIFLE_MK2', 'WEAPON_MG', 'WEAPON_MINIGUN',
		'WEAPON_MICROSMG', 'WEAPON_MILITARYRIFLE', 'WEAPON_MINISMG', 'WEAPON_MOLOTOV', 'WEAPON_MUSKET', 'WEAPON_NAVYREVOLVER',
		'WEAPON_NIGHTSTICK', 'WEAPON_PETROLCAN', 'WEAPON_GADGETPISTOL', 'WEAPON_PIPEBOMB', 'WEAPON_PISTOL', 'WEAPON_PISTOL50',
		'WEAPON_PISTOL_MK2', 'WEAPON_POOLCUE', 'WEAPON_CANDYCANE', 'WEAPON_PROXMINE', 'WEAPON_PUMPSHOTGUN', 'WEAPON_PUMPSHOTGUN_MK2',
		'WEAPON_RAILGUN', 'WEAPON_RAILGUNXM3', 'WEAPON_RAYCARBINE', 'WEAPON_RAYPISTOL', 'WEAPON_FLAMETHROWER', 'WEAPON_REVOLVER',
		'WEAPON_REVOLVER_MK2', 'WEAPON_RPG', 'WEAPON_SAWNOFFSHOTGUN', 'WEAPON_SMG', 'WEAPON_SMG_MK2', 'WEAPON_SMOKEGRENADE',
		'WEAPON_SNIPERRIFLE', 'WEAPON_SNOWBALL', 'WEAPON_SNSPISTOL', 'WEAPON_SNSPISTOL_MK2', 'WEAPON_SPECIALCARBINE',
		'WEAPON_SPECIALCARBINE_MK2', 'WEAPON_STICKYBOMB', 'WEAPON_STONE_HATCHET', 'WEAPON_STUNGUN', 'WEAPON_AUTOSHOTGUN',
		'WEAPON_SWITCHBLADE', 'WEAPON_VINTAGEPISTOL', 'WEAPON_RAYMINIGUN', 'WEAPON_WRENCH', 'WEAPON_PRECISIONRIFLE',
		'WEAPON_TACTICALRIFLE', 'WEAPON_TEARGAS', 'WEAPON_FN57', 'WEAPON_T1911', 'WEAPON_GLOCK21', 'WEAPON_TARP',
		'WEAPON_LBTARP', 'WEAPON_WOARP', 'WEAPON_SR40', 'WEAPON_ILLGLOCK17', 'WEAPON_DMK18', 'WEAPON_BLACKARP', 'WEAPON_REDM4A1',
		'WEAPON_BLUEGLOCKS', 'WEAPON_GLOCKBEAMS', 'WEAPON_MGGLOCK', 'WEAPON_TGLOCK19', 'WEAPON_MIDASGLOCK', 'WEAPON_TEC9S',
		'WEAPON_CHAIR', 'WEAPON_REDARP', 'WEAPON_P30L', 'WEAPON_GLOCK41', 'WEAPON_THOMPSON', 'WEAPON_RAM7', 'WEAPON_M500',
		'WEAPON_R590', 'WEAPON_BAR15', 'WEAPON_BSCAR', 'WEAPON_AXE', 'WEAPON_P210', 'WEAPON_KRISSVECTOR', 'WEAPON_FM1_GLOCK19',
		'WEAPON_FM1_M9A3', 'WEAPON_FM1_CZ75', 'WEAPON_FM1_P226', 'WEAPON_FM1_P320', 'money_bag', 'money_crate', 'ls_oxygen_tank' }
})

setContainerProperties('halloween_bag', {
	slots = 20,
	maxWeight = 30000,
	blacklist = { 'testburger', 'money_bag', 'money_crate','ls_oxygen_tank' }
})

setContainerProperties('hunting_bag', { 
	slots = 35,
	maxWeight = 150000,
	blacklist = { 'testburger', 'money_bag', 'money_crate' },
	whitelist = {'gg_hunting_animaltracker', 'gg_hunting_animaltrap', 'gg_hunting_campfire', 'gg_hunting_meat', 
		'gg_hunting_cookedmeat', 'gg_hunting_knife_01', 'gg_hunting_knife_02', 'gg_hunting_knife_03', 
		'gg_deer_hide_01', 'gg_deer_hide_02', 'gg_deer_hide_03', 'gg_boar_tusk_01', 'gg_boar_tusk_02', 
		'gg_boar_tusk_03', 'gg_rabbit_pelt_01', 'gg_rabbit_pelt_02', 'gg_rabbit_pelt_03', 'gg_cougar_claw_01', 
		'gg_cougar_claw_02', 'gg_cougar_claw_03', 'gg_coyote_fangs_01', 'gg_coyote_fangs_02', 'gg_coyote_fangs_03', 
		'gg_salt_block_01', 'gg_salt_block_02', 'gg_salt_block_03', 'gg_pug_bait_01', 'gg_pug_bait_02', 'gg_pug_bait_03', 
		'gg_captured_rabbit', 'gg_captured_hen', 'gg_captured_chickenhawk' }
})

setContainerProperties('fishing_bag', {
	slots = 50,
	maxWeight = 150000,
	blacklist = { 'testburger', 'money_bag', 'money_crate' },
	whitelist = { 'anchor', 'antique_locket', 'arapaima', 'atlantean_coin', 'bait_shovel',
		'bass', 'bluegill', 'bream', 'captain_compass', 'captain_spyglass', 'carp', 'catfish',
		'cod', 'devilray', 'eel', 'emperors_jade', 			'excellent_bait', 'fishingbait',
		'fishinghat', 'fishingnet', 'fishingrod', 'fishingrod2', 'fishingrod3', 'giantcatfish', 
		'giantcoelacanth', 'giantsnakehead', 'gold_coin', 'goldenfish', 'goliathtigerfish', 'good_bait', 
		'herring', 	'illegal_hook', 'kraken', 'legendary_bait', 'logbook', 'megalodon', 'megalodon_tooth', 
		'muskellunge', 'net_repair_kit', 'perch', 'pharaohs_scarab', 'pike', 'salmon', 'shark', 'sturgeon', 
		'swordfish', 'tarpon', 'treasure_bait', 'treasure_map', 'trout', 'tuna', 'vintage_watch', 'walleye', 'whale', 'zander' }
})

setContainerProperties('mining_bag', {
	slots = 40,
	maxWeight = 150000,
	blacklist = { 'testburger', 'money_bag', 'money_crate' },
	whitelist = {
		'bauxiteore_bar', 'bauxiteore_ore', 'carbon', 'chain_mold', 'coal_brick', 'coal_ore',
		'copper', 'copper_ore', 'crystal_blue', 'crystal_green', 'crystal_ore', 'crystal_red',
		'diamond', 'diamond_earring', 'diamond_earring_silver', 'diamond_necklace_silver',
		'diamond_necklace2', 'diamond_ore', 'diamond_ring_silver', 'diamond_ring2',
		'earring_mold', 'emerald', 'emerald_earring', 'emerald_earring_silver',
		'emerald_necklace', 'emerald_necklace_silver', 'emerald_ore', 'emerald_ring',
		'emerald_ring_silver', 'garnet', 'garnet_earrings', 'garnet_earrings_silver',
		'garnet_necklace', 'garnet_necklace_silver', 'garnet_ore', 'garnet_ring',
		'garnet_ring_silver', 'gold_bar', 'gold_chain', 'gold_earring', 'gold_ore',
		'gold_ring2', 'goldore', 'hammer', 'iron', 'iron_ore', 'ironore', 'laser_drill',
		'nephrite', 'nephrite_earrings', 'nephrite_earrings_silver', 'nephrite_necklace',
		'nephrite_necklace_silver', 'nephrite_ore', 'nephrite_ring', 'nephrite_ring_silver',
		'pickaxe', 'polymer', 'pure_gold_bar', 'refinedbrass', 'refinedcopper',
		'refinedplastic', 'refinedrubber', 'refinedscrap', 'ring_mold', 'rock', 'ruby',
		'ruby_earring', 'ruby_earring_silver', 'ruby_necklace', 'ruby_necklace_silver',
		'ruby_ore', 'ruby_ring', 'ruby_ring_silver', 'saphire', 'sapphire',
		'sapphire_earring', 'sapphire_earring_silver', 'sapphire_necklace',
		'sapphire_necklace_silver', 'sapphire_ore', 'sapphire_ring', 'sapphire_ring_silver',
		'silver_bar', 'silver_chain', 'silver_earring', 'silver_ore', 'silver_ring',
		'sulfur', 'tin_bar', 'tin_ore', 'titanium_ingot', 'titanium_ore'}
})

setContainerProperties('material_bag', {
	slots = 40,
	maxWeight = 150000,
	blacklist = { 'testburger', 'money_bag', 'money_crate' },
	whitelist = {
		'bauxiteore_bar', 'bauxiteore_ore', 'carbon','coal_brick', 'coal_ore',
		'copper', 'copper_ore', 'crystal_blue', 'crystal_green', 'crystal_ore', 'crystal_red',
		'diamond', 'diamond_ore', 'emerald', 'emerald_ore', 'garnet', 'garnet_ore', 'gold_bar',
		'gold_ore','goldore', 'iron', 'iron_ore', 'ironore', 'nephrite', 'nephrite_ore',
		'polymer', 'pure_gold_bar', 'refinedbrass', 'refinedcopper', 'refinedplastic',
		'refinedrubber', 'refinedscrap', 'rock', 'ruby', 'ruby_ore','saphire',
		'sapphire', 'sapphire_ore', 'silver_bar', 'silver_ore', 'silver_ring',
		'sulfur', 'tin_bar', 'tin_ore', 'titanium_ingot', 'titanium_ore','metalscrap',
		'plastic','iron','aluminum','rubber','tobacco','log','cloth','screws',
		'glue','refinediron','sulfur','lithium','woodhandle', 'glass', 'lockpickpart', 'empty_weed_bag', 'lighter'}
})


return containers
