import { CaseReducer, PayloadAction } from '@reduxjs/toolkit';
import { getTargetInventory } from '../helpers';
import { Inventory, InventoryType, SlotWithItem, State } from '../typings';

export const stackSlotsReducer: CaseReducer<
  State,
  PayloadAction<{
    fromSlot: SlotWithItem;
    fromType: Inventory['type'];
    fromId: Inventory['id'];
    toSlot: SlotWithItem;
    toType: Inventory['type'];
    toId: Inventory['id'];
    count: number;
  }>
> = (state, action) => {
  const { fromSlot, fromType, fromId, toSlot, toType, toId, count } = action.payload;

  const { sourceInventory, targetInventory } = getTargetInventory(state, fromId, toId);

  const pieceWeight = fromSlot.weight / fromSlot.count;


  targetInventory.items[toSlot.slot - 1] = {
    ...targetInventory.items[toSlot.slot - 1],
    count: +toSlot.count + +count,
    weight: pieceWeight * (+toSlot.count + +count),
  };

  if (fromType === InventoryType.SHOP || fromType === InventoryType.CRAFTING) return;

  sourceInventory.items[fromSlot.slot - 1] =
    fromSlot.count - count > 0
      ? {
          ...sourceInventory.items[fromSlot.slot - 1],
          count: +fromSlot.count - +count,
          weight: +pieceWeight * (+fromSlot.count - +count),
        }
      : {
          slot: fromSlot.slot,
        };
};
