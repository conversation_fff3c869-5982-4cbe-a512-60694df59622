if not lib then return end
require '@qbx_core.modules.lib'


function GetBodyDamage(source)
    local PlayerSkeleton = {
        head = {        label = 'head',         limp = false, level = 0, bleed = 0, type = "" },
        neck = {        label = 'neck',         limp = false, level = 0, bleed = 0, type = "" },
        spine = {       label = 'spine',        limp = false, level = 0, bleed = 0, type = "" },
        upper_body = {  label = 'upper_body',   limp = false, level = 0, bleed = 0, type = "" },
        lower_body = {  label = 'lower_body',   limp = false, level = 0, bleed = 0, type = "" },
        left_arm = {    label = 'left_arm',     limp = false, level = 0, bleed = 0, type = "" },
        left_leg = {    label = 'left_leg',     limp = false, level = 0, bleed = 0, type = "" },
        right_arm = {   label = 'right_arm',    limp = false, level = 0, bleed = 0, type = "" },
        right_leg = {   label = 'right_leg',    limp = false, level = 0, bleed = 0, type = "" },
    }
	local bodyDmg = exports['wasabi_ambulance']:getInjuries()
	if bodyDmg ~= false then
		if bodyDmg then
			local ptr = next(bodyDmg, nil)
			while ptr do
                -- print(json.encode(bodyDmg[ptr]))
				if PlayerSkeleton[ptr] then
					PlayerSkeleton[ptr] = bodyDmg[ptr].data
                    PlayerSkeleton[ptr].type = bodyDmg[ptr].type
				end
				ptr = next(bodyDmg, ptr)
			end
		end
    end
    SendNUIMessage({
        action = 'DamageCall',
        data = PlayerSkeleton
    })
	-- print(json.encode(PlayerSkeleton))
    return
end

AddEventHandler('getBodyDamage', GetBodyDamage)