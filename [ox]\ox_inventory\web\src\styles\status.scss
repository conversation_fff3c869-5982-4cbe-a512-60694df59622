@import "animations.scss";
@import "colors.scss";

$columnSize: 49.778vh;
$columnGap: 3.556vh;
$gridSize: calc($columnSize * 18.5 / 100);
$gridGap: calc(($columnSize * 0.99 - ($gridCols * $gridSize)) / ($gridCols));

// Status Section
section.status {
  position: absolute;
  display: grid;
  flex-direction: column;
  // justify-content: center; 
  width: $columnSize;
  gap: auto;
  padding-top:6.55vh;
  height: calc(100% - 6.55vh);
  // height:100%;
  top: 0%;
  outline: Yellow;
  // background-color: #00ff0033;
  pointer-events: none;

  .header {
    display: block;
    // margin-top: 0.45vh;
    height: 4.2vh;
    //background-color: #0000ff33;
    img {
      height: 100%;
      margin-left: 1.067vh;
      // display: none;
    }
    span {
      display: inline-block;
      position: relative;
      top: -35%;
    }
  }

  .body-wrapper {
    display: inline-block;
    // align-items: flex-start;
    font-weight: bold;
    position: relative;
    // left: 12.3vh;
    // top: calc(-1 * ($gridSize * 5 + $gridGap * 7.5));
    // margin-top: 5.3%;
    margin-left: 24%;
    // margin-right: auto;
    // padding-left: 0.633vh;
    width: 52.6%;
    aspect-ratio: 543 / 1022;
    pointer-events: all;
  }

  .body {
    display: inline-flex;
    height: calc(5*$gridSize + 4*$gridGap);
    // outline: yellow solid 1px;
    // height:100%;
    width: 100%;
    justify-content: center;
    // div {
    //   outline: yellow solid 1px;
    // }
    
    .bodyimage {
      // width: 100%;
      height: calc(5*$gridSize + 4*$gridGap);
      object-fit: cover;
      position: absolute;
      // filter: sepia(100%) saturate(300%) brightness(100%) hue-rotate(299deg);
    }
  }

  .censor {
    pointer-events: all;
    color: rgb(26,26,26);
    background-color: rgb(26,26,26);;
    
    &:hover {
      color: #FFF;
      background-color: #00000000;
    }
  }

  .stats {
    display: grid;
    position: relative;
    justify-content: space-between;
    // margin-top: auto;
    max-width: $columnSize;
    bottom: 0;
    grid-template-columns: auto auto;
    row-gap: 2.2vh;
    // background-color: rgba(64, 64, 64, 0.5);
    // padding: .5rem 1rem;
    border-radius: .5rem;
    // box-shadow: -5px 2px 3px rgba(0, 0, 0, 0.2);
    div {
      width: auto;
    }

    .info-bubble {
      display: inline-block;
      width: 0.6em;
      height: 0.6em;
      background-color: var(--secondaryColor);
      border-radius: 100%;
      margin-top: .2em;
      margin-right: .5rem;
      transform: scale(1);
      animation: pulse 2s infinite;
    }
  }

  .info,
  .person {
    display: flex;
    align-items: right;
    font-size: 1.778vh;
    font-weight: bold;
    text-transform: uppercase;
    text-wrap: nowrap;
  }

  .person {
    .name {
      margin-left: auto;
      text-align: right;
    }
  }

  .stat {
    border-left: 4px solid;
    padding-left: .75rem;
    padding-right: .75rem;
    font-size: 1.418vh;

    &.right {
      border-left: none;
      border-right: 4px solid;
      text-align: right;
      font-size: 1.418vh;
    }
  }

div {
  padding-bottom: .1rem;
  align-content: end;
  overflow: hidden;

  .label {
    display: block;
    font-size: 1.185vh;
    color: $panelLabelColor;
  }

  .value {
    font-size: 1.418vh;
    max-lines: 1;
    text-overflow: ellipsis;
    white-space: nowrap;
    text-wrap: nowrap;
    overflow: hidden;
  }
}

  

  .stat.dob {
    border-color: yellow;
  }
  
  .stat.money {
    border-color: blue;
  }
  
  .stat.phone {
    border-color: green;
  }
  
  .stat.home {
    border-color: orange;
  }
}