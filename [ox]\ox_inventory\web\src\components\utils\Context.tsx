import { autoUpdate, flip, FloatingPortal, offset, shift, useFloating, useTransitionStyles } from '@floating-ui/react';
import React, { useEffect } from 'react';
import { useAppDispatch, useAppSelector } from '../../store';
import HelpTooltip from '../inventory/HelpTooltip';

const Context: React.FC = () => {
  const hoverData = useAppSelector((state) => state.contextMenu);
  const open = hoverData.coords != null ? true : false;
  const dispatch = useAppDispatch();
  var x = 0;
  var y = 0;

  const { refs, context, floatingStyles } = useFloating({
    whileElementsMounted: autoUpdate,
    middleware: [flip(), shift(), offset({ mainAxis: 10, crossAxis: 10 })],
    open: open,
    placement: 'right-end',
  });

  const { isMounted, styles } = useTransitionStyles(context, {
    duration: 200,
  });

  const handleMouseMove = ({ clientX, clientY }: MouseEvent | React.MouseEvent<unknown, MouseEvent>) => {
    refs.setPositionReference({
      getBoundingClientRect() {
        // console.log(clientX, clientY)
        if (x == 0) {
          x = clientX;
          y = clientY;
        }
        return {
          width: 0,
          height: 0,
          x: hoverData.coords.x,
          y: hoverData.coords.y,
          left: hoverData.coords.x,
          top: hoverData.coords.y,
          right: hoverData.coords.x,
          bottom: hoverData.coords.y,
        };
      },
    });
  };

  useEffect(() => {
    var help = document.getElementsByClassName('inventory-slot');
    for (var i = 0; i < help.length; i++) {
      // help[i].addEventListener('contextmenu', handleMouseMove,);
    }
    document.addEventListener("click", function(event) {
      // console.log(event.target)
    })

    return () => {
      window.removeEventListener('contextmenu', handleMouseMove);
    };
  }, []);

  return (
    <>
      {isMounted && open   && (
        <FloatingPortal>
          <HelpTooltip
            ref={refs.setFloating}
            style={{ ...floatingStyles, ...styles }}
          />
        </FloatingPortal>
      )}
    </>
  );
};

export default Context;
