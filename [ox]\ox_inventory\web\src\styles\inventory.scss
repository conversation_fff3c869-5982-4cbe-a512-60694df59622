$mainFont: Roboto;

$gridCols: 5;
$gridRows: 5;

$columnSize: 49.778vh;
$columnGap: 3.556vh;
$gridSize: calc($columnSize * 18.5 / 100);
$gridGap: calc(($columnSize * 0.99 - ($gridCols * $gridSize)) / ($gridCols));

$borderRadius: 0.7vh;

$containerSize: calc(#{$gridRows} * #{$gridSize} + (#{$gridRows} - 1) * #{$gridGap});
$inventory-margin-space: 3rem;

// Hotbar
.hotbar-container {
  display: flex;
  align-items: center;
  gap: 2vh;
  justify-content: center;
  width: 100%;
  position: absolute;
  bottom: 2vh;
}

.hotbar-item-slot {
  @extend .inventory-slot;
  width: $gridSize;
  aspect-ratio: 1;
}

.hotbar-slot-header-wrapper {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
}


// Inventory
.inventory-overlay {
  position: relative;
  display: block;
  align-items: center;
  background: #030005e0 ;
  height: 100%;
}
.inventory-wrapper {
  display: flex;
  flex-direction: row;
  justify-content: center;
  position: relative;
  gap: $columnGap;
  width: 100%;
  // width: calc(3 * $columnSize + 2 * $columnGap);
  overflow: hidden;
  top: 12.2vh;
  margin-left: auto;
  margin-right: auto;
  height: 74vh;
  // background-color: #b7357faa;
}

.inventories {
  display: flex;
  position: absolute;
  height: 100%;
  width: calc(3 * $columnSize + 2 * $columnGap);
  gap: calc($columnGap - 2vh);
  justify-content: right;

  .inventory-column {
    position: relative;
    overflow-y: hidden;
    width: calc($columnSize + 4vh);
    // padding-right: 1vh;
    display: flex;
    flex-direction: column;
    gap: 2.8vh;

    &:hover {
      overflow-y: auto;
    }
  }

  .inventory-column-left {
    position: relative;
    width: calc($columnSize + 2vh);
    margin-right: 2vh;
  }
}

.inventory-control {
  display: flex;

  .inventory-control-wrapper {
    display: flex;
    flex-direction: column;
    gap: 3.556vh;
    justify-content: center;
    align-items: center;
    max-width: 6rem;
  }

  .inventory-control-input {
    transition: 200ms;
    padding: 16px 8px;
    border-radius: 2.5%;
    font-size: 16px;
    text-align: center;
    outline: none;
    border: none;
    color: #fff;
    background-color: $secondaryColor;
    width: 100%;

    &:focus-within {
      background-color: $secondaryColorDark;
    }
  }

  .inventory-control-button {
    font-size: 14px;
    color: #fff;
    background-color: $secondaryColor;
    transition: 200ms;
    padding: 12px 8px;
    border-radius: 2.5%;
    border: none;
    text-transform: uppercase;
    width: 100%;
    font-weight: 500;
    &:hover {
      background-color: $secondaryColorDark;
    }
  }
}


// Inventory Grids
.inventory-grid-wrapper {
  display: inline-flex;
  flex-direction: column;
  gap: 2.35vh;


  &.lower {
  }
}

.inventory-grid-hotbar-inventory {
  display: flex;
  overflow-y: hidden;
  margin-right: 0.3vh;
  // background-color: #00a0ffaa;
  &:hover {
    overflow-y: auto;
  }
}
::-webkit-scrollbar:vertical {
  display: inline;
  width: 5px;
  background-color: rgb(56,56,56);
  border-radius: 2.5px;
}
::-webkit-scrollbar-thumb {
  // display: inline;
  width: 4px;
  background-color: #c5c5c5;
  border-radius: 2px;
}

.inventory-grid-header {
  display: flex;
  flex-direction: row;
  width: calc($columnSize - 1vh);
  grid-template-columns: auto auto auto auto;
  column-gap: auto;
  position: relative;
  height: 4.2vh;
  justify-content: left;
  overflow-y:unset;
  // background-color: #0000ffaa;
  &.is-player {
    grid-template-columns: auto auto auto auto auto;
  }
  .title-weight {
    min-width: fit-content;
  }
}
.inventory-grid-header-icon {
  display: inline-block;
  height: 90%;
  margin-top: auto;
  margin-bottom: auto;
  aspect-ratio: 1;
  margin-left: 1.067vh;
  filter: saturate(100%) brightness(100%) hue-rotate(calc(var(--sectionTitleHue) - 275deg));
}
.blank {
  width:100%;
}
.search {
  display: inline-block;
  // position: relative;
  bottom: 0%;
  // height: 3.9vh;
  margin-top: auto;
  // margin-left: auto;
  left: calc(2 * $gridSize);
  input {
    background-color: #00000000;
    border-color: rgb(62, 62, 62);
    padding: 0.222vh;
    border-style: solid;
    border-radius: 0.7vh;
    color: rgb(117, 117, 117);
    font-size: 1.481vh;
    width: calc($gridSize * 1.89);
  }
  input:focus {
    border-color: rgb(117, 117, 117);
    border-style: solid;
    outline: none;
    padding: 0.222vh;
    width: calc($gridSize * 1.89);
  }
}

.collapse {
  // outline: yellow 1px solid;
  display: flex;
  margin-top: 10px;
  height: calc(100% - 10px);
  aspect-ratio: 3/2;
  justify-content: center;
  align-content: center;
  background-color: hsla(0, 0%, 20%, 0.0);
  border-radius: $borderRadius;
  margin-left: 1vh;
  &.collapsed {
    .collapse-arrow {
      transform: rotate(180deg);
    }
  }
  &:hover {
    .collapse-arrow {
      svg {
          stroke: whitesmoke;
      }
    }
  }

  .collapse-arrow {
    aspect-ratio: 1;
    height: 100%;
    transform-origin: center;
    transition: transform 300ms ease-in-out;
    margin: auto;
    
    svg {
      stroke: gray;
      stroke-width: 3;
      fill: $sectionTitleColor;
    }
  }
}

// .inventory-grid-header-wrapper {
//   display: inline-block;
//   // justify-content: spaces-between;
//   width: 100%;
//   position: relative;
//   top: -35%;

  .title-weight {
    display: inline-block;
    // position: relative;
    // top: 5%;
    // justify-content: space-between;
    margin-top: auto;
    margin-bottom: auto;
    font-size: 1.926vh;

    .weight {
      display: block;
      margin-left: 1rem;
      font-size: 1.481vh;
    }
  }
// }

.inventory-grid-container {
  display: grid;
  width: $columnSize;
  grid-template-columns: repeat($gridCols, $gridSize);
  grid-auto-rows: $gridSize;
  gap: $gridGap;
  overflow-y: hide;
  max-height: calc($gridSize * 3 + $gridGap * 2);
  padding-bottom: calc($gridGap*0.2);
  border-radius: $borderRadius;
  // background-color: #0000ffaa;
  transition: max-height 300ms ease-in-out;

  &.vertical {
    display: inline-flex;
    flex-direction: column;
    position: absolute;
    top: 6.2vh;
    right: -4vh;
    margin-right: $columnGap;
    grid-template-rows: $gridSize;
    grid-auto-columns: repeat(1, $gridSize);
    gap: $gridGap;
    width: $gridSize;
    max-height: calc($gridSize * 5 + $gridGap * 4.5);
  }
  &.solo {
    transition: max-height 300ms ease-in-out;
    max-height: calc($gridSize * 73 + $gridGap * 6);
  }
  &.collapsed {
    max-height: 0;
    padding: 0;
  }
}

.inventory-grid-container-left {
  display: inline-grid;
  overflow-y: hidden;
  overflow-x: hidden;
  grid-template-columns: repeat(3, $gridSize);
  grid-auto-rows: $gridSize;
  row-gap: $gridGap;
  column-gap: $gridGap;
  // column-gap: calc($gridSize * 3 + $gridGap * 5);
  // width: $columnSize;
  width: calc($gridSize + ($gridGap * 0.5));
  height: calc($gridSize * 5 + $gridGap * 4.5);
  border-radius: $borderRadius;
  z-index: 2;
  pointer-events: none;
  transition: 300ms ease-in-out;
  // transition-delay: 0ms;

  &:hover {
    width: calc($gridSize * 3 + $gridGap * 2.5);
    pointer-events: all;
    background: rgba(26, 26, 26, 0.8);

    .inventory-slot {
      background: radial-gradient(circle, hsl(0, 0%, 22%) 0%, hsl(0, 0%, 15%) 35%, hsl(0, 0%, 11%) 100%) no-repeat;
      // transition-delay: 0ms;

      &:hover {
        background: radial-gradient(circle, hsla(275, 10%, 30%, 1) 0%, hsla(275, 10%, 23%, 1) 35%, hsla(275, 10%, 18%, 1) 100%) no-repeat;
      }
    }
  }
}


// Inventory Slots
.inventory-slot {
  background-repeat: no-repeat;
  background-position: center;
  background: rgb(56,56,56, 0.85);
  background: radial-gradient(circle, hsla(0, 0%, 22%, 0.85) 0%, hsla(0, 0%, 15%, 0.85) 55%, hsla(0, 0%, 11%, 0.85) 100%) no-repeat;
  box-shadow: -5px 2px 3px rgba(0, 0, 0, 0.2);
  image-rendering: -webkit-optimize-contrast;
  position: relative;
  color: $textColor;
  border-color: rgba(0, 0, 0, 0.0);
  border-style: inset;
  border-radius: $borderRadius;
  border-width: 0px;
  width: $gridSize;
  aspect-ratio: 1;
  pointer-events: all;;
  overflow: hidden;
}
.inventory-slot:hover {
  background: rgba(76,76,76, 0.85);
  background: radial-gradient(circle, hsla(0, 0%, 22%, 0.85) 0%, hsla(0, 0%, 18%, 0.85) 55%, hsla(0, 0%, 15%, 0.85) 100%) no-repeat;
  box-shadow: -0px -0px 4px 0px $sectionTitleColor inset;
  .inventory-slot-label-box {
    // margin-bottom: 2px;
    // margin-left: 1px;
    // margin-right: 1px;
    box-shadow: 0 -4px 4px -4px  $sectionTitleColor inset, -4px 0px 4px -4px  $sectionTitleColor inset, 4px 0px 4px -4px  $sectionTitleColor inset;
  }
}

.inventory-slot-label-wrapper {
  z-index: 1;
  // width: calc($gridSize - 4px);
  // container-type:inline-size;
  // container-name: ''
}

.inventory-slot-label-box {
  // background: radial-gradient(circle, hsla(0, 0%, 22%, 0) 0%, hsla(0, 0%, 18%, 0) 55%, hsla(0, 0%, 15%, 0) 100%) no-repeat;
  background-color: hsla(var(--sectionTitleHue) var(--sectionTitleSat) calc(var(--sectionTitleLum) * 7 / 50) / 1);
  color: $textColor;
  text-align: center;
  border-bottom-left-radius: $borderRadius;
  border-bottom-right-radius: $borderRadius;
  // border-top-color: hsla(0, 0%, 0%, 0.2);
  // border-top-style: inset;
  // border-top-width: 2px;
  width: $gridSize;
  overflow-x: hidden;
}

.inventory-slot-label-text {
  text-transform: uppercase;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  padding: 2px 0px;
  font-weight: 400;
  font-size: 1.111vh;
    
  @keyframes scroll-text {
    0% {
      transform: translateX(0%);
    }
    90% {
      transform: translateX(-50%);
    }
    95% {
      transform: translateX(0%);
    }
    100% {
      transform: translateX(0%);
    }
  }
}

.inventory-slot-number {
  display: block;
  position: relative;
  top:-11.5%;
  // background-color: white;
  color: hsla(0, 0%, 28%, 0.85);
  height: $gridSize;
  width: $gridSize;
  // border-top-left-radius: $borderRadius;
  // border-bottom-right-radius: $borderRadius;
  text-align: center;
  font-size: calc($gridSize);
  font-weight: 700;
  text-shadow: 0px 0px 10px hsla(0, 0%, 8%, 0.85);
}

.item-slot-wrapper {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  height: $gridSize;
  position: relative;

  .image {
    position: absolute;
    left: 50%;
    top: 45%;
    max-width: 65%;
    max-height: 65%;
    transform: translate(-50%, -50%);
    z-index: 3;
    // outline: yellow 1px solid;
  }

  p {
    font-size: 1.111vh;
  }
}

.item-slot-header-wrapper {
  display: flex;
  flex-direction: row;
  justify-content: flex-end;
  z-index: 1;
}

.item-hotslot-header-wrapper {
  @extend .item-slot-header-wrapper;
  justify-content: space-between !important;
}

.item-slot-info-wrapper {
  display: grid;
  flex-direction: row;
  grid-template-columns: auto 1fr auto;
  align-self: flex-end;
  padding: 4px;
  gap: 3px;
  width: 100%;
  font-size: 1.222vh;
  font-weight: 600;
  div {
    text-shadow: 0px 0px 0.2vh hsl(0, 0%, 6%);
  }
}

.item-slot-currency-wrapper {
  display: flex;
  flex-direction: row;
  justify-content: flex-end;
  align-items: center;
  padding-right: 3px;
  p {
    font-size: 1.111vh;
    text-shadow: 0.1vh 0.1vh 0 rgba(0, 0, 0, 0.7);
  }
}

.item-slot-price-wrapper {
  display: flex;
  flex-direction: row;
  justify-content: flex-end;
  padding-right: 3px;
  display: flex;
  flex-direction: row;
  align-items: center;
  svg {
    padding-right: 1px;
    margin-bottom: 2px
  }
  p {
    font-size: 1.111vh;
    text-shadow: 0.1vh 0.1vh 0 rgba(0, 0, 0, 0.7);
  }
}


// Tooltip
.tooltip-wrapper {
  position: relative;
  pointer-events: none;
  display: flex;
  background-color: $mainColor;
  background-color: rgba(26, 26, 26, 0.9);
  min-width: 200px;
  width: fit-content;
  padding: 8px;
  flex-direction: column;
  max-width: 20vh;
  color: $textColor;
  border-radius: $borderRadius;
  border-color: hsla(0, 0%, 100%, 0.2);
  border-style: inset;
  border-width: 1px;
  p {
    font-size: 1.111vh;
    font-weight: 400;
  }
}

.tooltip-description {
  text-align: center;
}

.tooltip-markdown > p {
  margin: 0;
}

.tooltip-image {
  max-width: 100%;
  margin: 1vh auto 0.4vh auto ;
}

.tooltip-durability-wrapper {
  position: relative;
  width: 100%;
  p {
    position: absolute;
    transform: translateX(-50%) translateY(-50%);
    width: min-content;
    top: 50%;
    left: 50%;
    font-size: 1.555vh;
    font-weight: 600;
  }
}

.tooltip-durability {
  width: 100%;
  height: 3vh;
  overflow: visible;
  position: relative;
  margin: .11vh 0;
  &::-webkit-meter-bar {
    background: #00000000;
    border-radius: 10vh;
    border-width: 1px;
  }
  &::-webkit-meter-suboptimum-value {
    background-color: rgba(116, 87, 1, 0.5);

  }
  &::-webkit-meter-optimum-value {
    background-color: rgba(1, 92, 1, 0.5);
  }
  &::-webkit-meter-even-less-good-value {
    background-color: rgba(121, 0, 0, 0.5);
  }
}

.tooltip-header-wrapper {
  //display: flex;
  //flex-direction: row;
  //justify-content: space-between;
  align-self: center;
  //gap: 0.3vh;
  p {
    font-size: 1.389vh;
    font-weight: 800;
  }
}

.tooltip-body-wrapper {
  display: flex;
}

.tooltip-crafting-duration {
  position: absolute;
  top: 0;
  right: 0;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
  padding: 0.22vh 0.33vh 0.33vh 0.44vh;
  background-color: hsla(var(--sectionTitleHue) var(--sectionTitleSat) calc(var(--sectionTitleLum) / 4) / 0.7);
  border-radius: 0 $borderRadius 0 calc(1*$borderRadius);
  border: 1px solid #ffffff22;
  svg {
    padding-right: 3px;
  }
  p {
    font-weight: 400;
    font-size: 1.555vh;
  }
}

.tooltip-ingredients {
  padding-top: 5px;
}

.tooltip-ingredient {
  display: flex;
  flex-direction: row;
  align-items: center;
  img {
    width: 2.593vh;
    aspect-ratio: 1;
    padding-right: 5px;
  }
}

.status-tooltip-grid {
  max-width: 90%;
  display: grid;
  grid-template-columns: auto 1fr;
  column-gap: 0.6vh;
  row-gap: 0.6vh;
  padding: 0 0.6vh;
  div {
    display: flex;
    width: 100%;
    justify-content: center;
    align-items: center;
    font-size: 1.3vh;
    padding-top: 0.65vh;
    padding-bottom: 0.35vh;
    border-radius: $borderRadius;
    text-shadow: 0 0 4px black;
  }
  .left {
    justify-content: right;
    text-align: right;
    img {
      width: 2.593vh;
      aspect-ratio: 1;
    }
  }
  .right {
    justify-content: left ;
    padding-left: 0.6vh;
    background-color: hsla(var(--sectionTitleHue) var(--sectionTitleSat) calc(var(--sectionTitleLum) / 2) / 0.4);
    border: 1px inset rgba(29, 29, 29, 0.2);
    text-align: left;
  }
}


// Item Notifications
.item-notification-container {
  display: flex;
  overflow-x: scroll;
  flex-wrap: nowrap;
  gap: 2px;
  position: absolute;
  bottom: 20vh;
  // left: 50%;
  width: 100%;
  //margin-left: calc(50% - calc($gridSize/2));
  //transform: translate(-50%);
  justify-content: center;
}

.item-notification-action-box {
  width: 100%;
  color: $textColor;
  background-color: $secondaryColor;
  text-transform: uppercase;
  text-align: center;
  border-top-left-radius: 0.25vh;
  border-top-right-radius: 0.25vh;
  p {
    font-size: 1.019vh;
    padding: 2px;
    font-weight: 600;
  }
}

.item-notification-item-box {
  @extend .inventory-slot;
  height: $gridSize;
  width: $gridSize;
}

.durability-bar {
  background: rgba(0, 0, 0, 0.5);
  height: 3px;
  overflow: hidden;
}

.weight-bar {
  background: rgba(0, 0, 0, 0.4);
  border: 1px inset rgba(0, 0, 0, 0.1);
  height: 0.8vh;
  border-radius: 5%;
  overflow: hidden;
}

.split-popup {
  position: absolute;
  background-color: hsla(var(--sectionTitleHue) calc(var(--sectionTitleSat) / 10) calc(var(--sectionTitleLum) * 0.4) / 0.8);
  width: calc($columnSize * 1.25);
  aspect-ratio: 16/4;
  align-items: center;
  z-index: 2;
  display: flex;
  flex-direction: column;
  align-self: center;
  justify-content: center;
  margin: auto;
  gap:0.7vh;
  
  border-radius: 0.7vh;
}

.split {
  display: grid;
  gap: 0.6vh;
  grid-template-columns: auto auto;
  flex-direction: row;
  align-content: center;
  width: 95%;
  height: 32px; 
  margin: 0 2.5%;
}

.split-input {
  text-align: center;
  background-color: #00000000;
  border-color: rgb(116, 116, 116);
  padding: auto;
  border-style: solid;
  border-radius: 0.7vh;
  margin-left: auto;
  color: rgb(255, 255, 255);
  font-size: 1.25vh;
  min-width: 7ex;
  // width: 1em;
  height: 70%;
  align-self: center;
  z-index: 10;
}

.split-input:focus {
  border-color: #9d9d9d;
  border-style: solid;
  outline: none;
  padding: 0.222vh;
}

.split-slider {
  width: auto;
  -webkit-appearance: none;
  background: hsla(var(--sectionTitleHue) var(--sectionTitleSat) calc(var(--sectionTitleLum) * 11 / 50) / 0);
  --track-color: hsla(var(--sectionTitleHue) var(--sectionTitleSat) calc(var(--sectionTitleLum) * 11 / 50)  / 0.667);
  outline: none;
  overflow-x: hidden;
	overflow-y: visible;
	color: $sectionTitleColor;
  height: 100%;
  --track-height: 25%;
	--thumb-height: 100%;
	--thumb-width: 10px;
	--clip-edges: 6px;
}

.split-slider:active {
	cursor: grabbing;
}

.split-slider,
.split-slider::-webkit-slider-runnable-track,
.split-slider::-webkit-slider-thumb {
	-webkit-appearance: none;
	transition: all ease 100ms;
	height: var(--thumb-height);
}

.split-slider::-webkit-slider-runnable-track,
.split-slider::-webkit-slider-thumb {
	position: relative;
}

.split-slider::-webkit-slider-thumb {
	--thumb-radius: calc((var(--thumb-height) * 0.5) - 1px);
	--clip-top: calc((var(--thumb-height) - var(--track-height)) * 0.5 - 0.5px);
	--clip-bottom: calc(var(--thumb-height) - var(--clip-top));
	--clip-further: calc(100% + 1px);
	--box-fill: calc(-100vmax - var(--thumb-width, var(--thumb-height))) 0 0
		100vmax currentColor;

	width: var(--thumb-width, var(--thumb-height));
	background: linear-gradient(currentColor 0 0) scroll no-repeat left center /
		50% calc(var(--track-height) + 1px);
	background-color: currentColor;
	box-shadow: var(--box-fill);
	border-radius: var(--thumb-width, var(--thumb-height));

	filter: brightness(100%);
	clip-path: polygon(
		100% -1px,
		0 -1px,
		0 var(--clip-top),
		-20vh var(--clip-top),
		-20vh var(--clip-bottom),
		0 var(--clip-bottom),
		0 100%,
		var(--clip-further) var(--clip-further)
	);
}

.split-slider:hover::-webkit-slider-thumb {
	filter: brightness(var(--brightness-hover));
	cursor: grab;
}

.split-slider:active::-webkit-slider-thumb {
	filter: brightness(var(--brightness-down));
	cursor: grabbing;
}

.split-slider::-webkit-slider-runnable-track {
	background: linear-gradient(var(--track-color) 0 0) scroll no-repeat center /
		100% calc(var(--track-height) + 1px);
    height: 2vh;
}

.split-slider:disabled::-webkit-slider-thumb {
	cursor: not-allowed;
}

.split-popup-close {
  background-color: #6602aebb;
  display: block;
  width: fit-content;
  height: fit-content;
  padding: 0.7vh 1.777vh;
  // position: absolute;
  // margin: 0 auto;
  // bottom:15%;
  // left: 0;
  // right: 0;
  font-size: 1.926vh;
  border-style: none;
  border-radius: 0.7vh;
}
.split-popup-close:hover {
  background-color: $sectionTitleColor;
}
