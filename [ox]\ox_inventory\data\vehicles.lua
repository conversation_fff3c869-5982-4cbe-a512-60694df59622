return {
	-- 0	vehicle has no storage
	-- 1	vehicle has no trunk storage
	-- 2	vehicle has no glovebox storage
	-- 3	vehicle has trunk in the hood
	Storage = {
		[`jester`] = 3,
		[`adder`] = 3,
		[`osiris`] = 1,
		[`pfister811`] = 1,
		[`penetrator`] = 1,
		[`autarch`] = 1,
		[`bullet`] = 1,
		[`cheetah`] = 1,
		[`cyclone`] = 1,
		[`voltic`] = 1,
		[`reaper`] = 3,
		[`entityxf`] = 1,
		[`t20`] = 1,
		[`taipan`] = 1,
		[`tezeract`] = 1,
		[`torero`] = 3,
		[`turismor`] = 1,
		[`fmj`] = 1,
		[`infernus`] = 1,
		[`italigtb`] = 3,
		[`italigtb2`] = 3,
		[`nero2`] = 1,
		[`vacca`] = 3,
		[`vagner`] = 1,
		[`visione`] = 1,
		[`prototipo`] = 1,
		[`zentorno`] = 1,
		[`trophytruck`] = 0,
		[`trophytruck2`] = 0,
	},

	-- slots, maxWeight; default weight is 8000 per slot
	glovebox = {
		[0] = { 5, 8000 }, -- Compact
		[1] = { 5, 8000 }, -- Sedan
		[2] = { 5, 8000 }, -- SUV
		[3] = { 5, 8000 }, -- Coupe
		[4] = { 5, 8000 }, -- Muscle
		[5] = { 5, 8000 }, -- Sports Classic
		[6] = { 5, 8000 }, -- Sports
		[7] = { 5, 8000 }, -- Super
		[8] = { 5, 4000 }, -- Motorcycle
		[9] = { 5, 8000 }, -- Offroad
		[10] = { 5, 8000 }, -- Industrial
		[11] = { 5, 8000 }, -- Utility
		[12] = { 5, 8000 }, -- Van
		[14] = { 5, 8000 }, -- Boat
		[15] = { 5, 2000 }, -- Helicopter
		[16] = { 5, 2000 }, -- Plane
		[17] = { 5, 8000 }, -- Service
		[18] = { 5, 8000 }, -- Emergency
		[19] = { 5, 8000 }, -- Military
		[20] = { 5, 8000 }, -- Commercial (trucks)
		models = {
			[`xa21`] = { 11, 8000 }
		}
	},

	trunk = {
		[0] = { 20, 12000 }, -- Compact
		[1] = { 20, 12000 }, -- Sedan
		[2] = { 20, 19000 }, -- SUV
		[3] = { 20, 12000 }, -- Coupe
		[4] = { 20, 12000 }, -- Muscle
		[5] = { 20, 12000 }, -- Sports Classic
		[6] = { 20, 12000 }, -- Sports
		[7] = { 20, 5000 }, -- Super
		[8] = { 5, 4000 }, -- Motorcycle
		[9] = { 20, 22000 }, -- Offroad
		[10] = { 20, 22000 }, -- Industrial
		[11] = { 20, 32800 }, -- Utility
		[12] = { 20, 32000 }, -- Van
		[14] = { 5, 4000 }, -- Boat
		[15] = { 5, 4000 }, -- Helicopter
		[16] = { 5, 4000 }, -- Plane
		[17] = { 20, 32800 }, -- Service
		[18] = { 20, 32800 }, -- Emergency
		[19] = { 20, 32800 }, -- Military
		[20] = { 20, 22000 }, -- Commercial
		models = {
			[`xa21`] = { 20, 10000 }
		},
	}
}
