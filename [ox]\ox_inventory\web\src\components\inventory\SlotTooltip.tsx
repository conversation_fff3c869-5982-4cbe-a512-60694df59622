import { Inventory, SlotWithItem } from '../../typings';
import React, { Fragment, useMemo } from 'react';
import { Items } from '../../store/items';
import { Locale } from '../../store/locale';
import ReactMarkdown from 'react-markdown';
import { useAppSelector } from '../../store';
import ClockIcon from '../utils/icons/ClockIcon';
import { getItemUrl } from '../../helpers';
import Divider from '../utils/Divider';

const SlotTooltip: React.ForwardRefRenderFunction<
  HTMLDivElement,
  { item: SlotWithItem; inventoryType: Inventory['type']; maxWeight: number; style: React.CSSProperties }
> = ({ item, inventoryType, maxWeight, style }, ref) => {
  const additionalMetadata = useAppSelector((state) => state.inventory.additionalMetadata);
  const itemData = useMemo(() => Items[item.name], [item]);
  const ingredients = useMemo(() => {
    if (!item.ingredients) return null;
    return Object.entries(item.ingredients).sort((a, b) => a[1] - b[1]);
  }, [item]);
  const description = item.metadata?.description || itemData?.description;
  const ammoName = itemData?.ammoName && Items[itemData?.ammoName]?.label;

  return (
    <>
        <div style={{ ...style }} className="tooltip-wrapper popup" ref={ref}>
          <img className="tooltip-image" src={item?.name ? getItemUrl(item as SlotWithItem) : ""} />
          <div className="tooltip-header-wrapper">
            <p>{item.metadata?.label || itemData?.label || item.name}</p>
          </div>
            {description && (
              <>
                {/* <Divider /> */}
                <div className="tooltip-description">
                  <ReactMarkdown className="tooltip-markdown">{description}</ReactMarkdown>
                </div>
              </>
            )}
            {item.durability !== undefined ? (
                  <div className='tooltip-durability-wrapper'>
                    <meter className="tooltip-durability" min={0} max={100} low={33} high={66} optimum={100} value={item.durability} />
                    <p>{Math.trunc(item.durability)}</p>
                  </div>
            ) : (<Divider />) }
          <div className='tooltip-crafting-duration'>
            {inventoryType === 'crafting' ? (
                <>
                  <ClockIcon />
                  <p>{(item.duration !== undefined ? item.duration : 0) / 1000}s</p>
                </>
              ) : (
                <>
                  <p>{(item.weight ? item.weight / 1000 : 0.0)}</p>
                </>
              )}
          </div>
          
          <div className='status-tooltip-grid'>
            {inventoryType !== 'crafting' ? (
              <>
                {item.metadata?.type ? (
                  <>
                    <div className='left'>
                      Type
                    </div>
                    <div className="right">
                      <p>{item.metadata?.type}</p>
                    </div>
                  </>
                ) : '' }
                {item.metadata?.ammo !== undefined && (
                  <>
                    <div className='left'>
                      {Locale.ui_ammo}
                    </div>
                    <div className="right">
                      <p>{item.metadata.ammo}</p>
                    </div>
                  </>
                )}
                {ammoName && (
                  <>
                    <div className='left'>
                      {Locale.ammo_type}
                    </div>
                    <div className="right">
                      <p>{ammoName}</p>
                    </div>
                  </>
                )}
                {item.metadata?.serial && (
                  <>
                    <div className='left'>
                      {Locale.ui_serial}
                    </div>
                    <div className="right">
                      <p>{item.metadata.serial}</p>
                    </div>
                  </>
                )}
                {item.metadata?.components && item.metadata?.components[0] && (
                  <>
                    {Locale.ui_components}:{' '}
                    {(item.metadata?.components).map((component: string, index: number, array: []) =>
                    {index + 1 === array.length ? (
                      <>
                        <div className='left'>
                          Items[component]?.label
                        </div>
                        <div className="right">
                          <p>Items[component]?.label</p>
                        </div>
                      </>
                    ) : ''
                    }
                    )}
                  </>
                )}
                {item.metadata?.weapontint && (
                  <>
                    <div className='left'>
                    {Locale.ui_tint}
                    </div>
                    <div className="right">
                      <p>{item.metadata.weapontint}</p>
                    </div>
                  </>
                )}
                {additionalMetadata.map((data: { metadata: string; value: string }, index: number) => (
                  <Fragment key={`metadata-${index}`}>
                    {item.metadata && item.metadata[data.metadata] && typeof item.metadata[data.metadata] != 'object' && (
                      <>
                        <div className='left'>
                        {data.value}
                        </div>
                        <div className="right">
                          <p>{item.metadata[data.metadata]}</p>
                        </div>
                      </>
                    )}
                  </Fragment>
                ))}
              </>
            ) : (
              <>
                {ingredients &&
                  ingredients.map((ingredient) => {
                    const [item, count] = [ingredient[0], ingredient[1]];
                    return (
                      <>
                        <div className="left" key={`ingredient-${item}`}>
                          <img src={item ? getItemUrl(item) : 'none'} alt="item-image" />
                        </div>
                        <div className='right'>
                          <p>
                            {count >= 1
                              ? `${count}x ${Items[item]?.label || item}`
                              : count === 0
                              ? `${Items[item]?.label || item}`
                              : count < 1 && `${count * 100}% ${Items[item]?.label || item}`}
                          </p>
                        </div>
                      </>
                    );
                  })}
              </>
            )}
            </div>
        </div>
    </>
  );
};

export default React.forwardRef(SlotTooltip);
