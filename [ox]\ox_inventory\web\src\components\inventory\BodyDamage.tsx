import illeg from '../../../inventory-images/LLEG.png';
import irleg from '../../../inventory-images/RLEG.png';
import iupperbody from '../../../inventory-images/upperbody.png';
import ilowerbody from '../../../inventory-images/lowerbody.png';
import irarm from '../../../inventory-images/rarm.png';
import ilarm from '../../../inventory-images/larm.png';
import ilpalm from '../../../inventory-images/lpalm.png';
import ineck from '../../../inventory-images/neck.png';
import ihead from '../../../inventory-images/head.png';
import irpalm from '../../../inventory-images/rpalm.png';
import irfoot from '../../../inventory-images/rfoot.png';
import ilfoot from '../../../inventory-images/lfoot.png';
import Arrowright from '../utils/Arrowright';
import Arrowleft from '../utils/Arrowleft';
import { flip, FloatingPortal, offset, shift, useFloating, useTransitionStyles } from '@floating-ui/react';
import React from 'react';
import { debugData } from '../../utils/debugData';
import { useAppDispatch, useAppSelector } from '../../store';
import useNuiEvent from '../../hooks/useNuiEvent';
import { bodyPart } from '../../typings/damage';
import { setDamage } from '../../store/damage';
import Divider from '../utils/Divider';







const BodyDamage = () => {
    const damagevar = useAppSelector((state) => state.damage);
    const preferences = useAppSelector((state) => state.preferences);
    const [hoverData, setHoverData] = React.useState<boolean>(false);
    const [bodypart, setBodypart] = React.useState<string>("");
    const dispatch = useAppDispatch();

    const { refs, context, floatingStyles } = useFloating({
        middleware: [flip(), shift(), offset({ mainAxis: 15, crossAxis: -20 })],
        open: hoverData,
        placement: 'right-start',
    });
    const { isMounted, styles } = useTransitionStyles(context, {
        duration: 200,
    });
    const handleMouseMove = ({ clientX, clientY }: MouseEvent | React.MouseEvent<unknown, MouseEvent>) => {
        refs.setPositionReference({
            getBoundingClientRect() {
                return {
                    width: 0,
                    height: 0,
                    x: clientX,
                    y: clientY,
                    left: clientX,
                    top: clientY,
                    right: clientX,
                    bottom: clientY,
                };
            },
        });
    };

    React.useEffect(() => {
        window.addEventListener('mousemove', handleMouseMove);

        return () => {
            window.removeEventListener('mousemove', handleMouseMove);
        };
    }, []);
    return (
        <div className="body">
            {isMounted && (
                <FloatingPortal>
                    <div
                        ref={refs.setFloating}
                        style={{ ...floatingStyles, ...styles, color: 'white' }}
                        className="tooltip-wrapper popup"
                    >
                        <div className="tooltip-header-wrapper">
                            <p>{bodypart == "head" ?            "HEAD"
                                : bodypart == "neck" ?          "NECK"
                                : bodypart == "spine" ?         "SPINE"
                                : bodypart == "upper_body" ?    "UPPER BODY"
                                : bodypart == "lower_body" ?    "LOWER BODY"
                                : bodypart == "left_arm" ?      "LEFT ARM"
                                : bodypart == "left_leg" ?      "LEFT LEG"
                                : bodypart == "right_arm" ?     "RIGHT ARM"
                                : bodypart == "right_leg" ?     "RIGHT LEG"
                                : ""
                            }</p>
                        </div>
                        <Divider />
                        <div>
                            <div className='status-tooltip-grid'>
                                    <div className='left' >
                                        Injury
                                    </div>
                                    <div className='right'>
                                        { damagevar[bodypart]?.type == "shot"    ? 'Gunshot Wound' 
                                        : damagevar[bodypart]?.type == "stabbed" ? 'Stab Wound' 
                                        : damagevar[bodypart]?.type == "beat"    ? 'Bruising' 
                                        : damagevar[bodypart]?.type == "burned"  ? 'Burned' 
                                        :                                          'None'}
                                    </div>
                                    <div className='left' >
                                        Bone
                                    </div>
                                    <div className='right' >
                                        {damagevar[bodypart]?.limp ? 'Broken' : 'Normal'}
                                    </div>
                                    <div className='left' >
                                        Pain
                                    </div>
                                    <div className='right' >
                                        { damagevar[bodypart]?.level >= 4 ? 'Deadly' 
                                        : damagevar[bodypart]?.level == 3 ? 'Severe' 
                                        : damagevar[bodypart]?.level == 2 ? 'Moderate' 
                                        : damagevar[bodypart]?.level == 1 ? 'Minor' 
                                        :                                   'None'}
                                    </div>
                                    <div className='left' >
                                        Bleeding
                                    </div>
                                    <div className='right'>
                                        { damagevar[bodypart]?.bleed >= 4 ? 'Deadly' 
                                        : damagevar[bodypart]?.bleed == 3 ? 'Severe' 
                                        : damagevar[bodypart]?.bleed == 2 ? 'Moderate' 
                                        : damagevar[bodypart]?.bleed == 1 ? 'Minor' 
                                        :                                   'None'}
                                    </div>
                            </div>
                        </div>
                    </div>
                </FloatingPortal>
            )}
            <div onMouseEnter={() => { setHoverData(true); setBodypart('head');         }} onMouseLeave={() => { setHoverData(false) }} className='head'      style={{ width: '19%', height: '13%', position: 'absolute', left: '41%', top: '0%', zIndex: 1, borderRadius: '1.778vh', rotate: '0deg', backgroundColor:'#00FF0000' }} />
            <div onMouseEnter={() => { setHoverData(true); setBodypart('neck')          }} onMouseLeave={() => { setHoverData(false) }} className='neck'      style={{ width: '30%', height: '5.6%', position: 'absolute', left: '36%', top: '13%', zIndex: 1, borderRadius: '1.778vh', rotate: '0deg', backgroundColor:'#FF000000' }} />
            <div onMouseEnter={() => { setHoverData(true); setBodypart('upper_body')    }} onMouseLeave={() => { setHoverData(false) }} className='upbody'    style={{ width: '35%', height: '16%', position: 'absolute', left: '33%', top: '18.5%', zIndex: 1, borderRadius: '1.778vh', backgroundColor:'#0000FF00' }} />
            <div onMouseEnter={() => { setHoverData(true); setBodypart('lower_body')    }} onMouseLeave={() => { setHoverData(false) }} className='lowerbody' style={{ width: '35%', height: '17%', position: 'absolute', left: '33%', top: '32.5%', zIndex: 1, borderRadius: '1.778vh', rotate: '0 deg', backgroundColor:'#FFFF0000' }} />
            <div onMouseEnter={() => { setHoverData(true); setBodypart('left_arm')      }} onMouseLeave={() => { setHoverData(false) }} className='larm'      style={{ width: '15%', height: '35%', position: 'absolute', right: '16%', top: '15%', zIndex: 1, borderRadius: '1.778vh', rotate: '-15deg', backgroundColor:'#FF00FF00' }} />
            <div onMouseEnter={() => { setHoverData(true); setBodypart('left_arm')      }} onMouseLeave={() => { setHoverData(false) }} className='lpalm'     style={{ width: '17%', height: '11%', position: 'absolute', right: '1%', top: '48%', zIndex: 1, borderRadius: '1.778vh', rotate: '-15deg', backgroundColor:'#00FFFF00' }} />
            <div onMouseEnter={() => { setHoverData(true); setBodypart('right_arm')     }} onMouseLeave={() => { setHoverData(false) }} className='rarm'      style={{ width: '15%', height: '35%', position: 'absolute', left: '16%', top: '15%', zIndex: 1, borderRadius: '1.778vh', rotate: '15deg', backgroundColor:'#FFFFFF00' }} />
            <div onMouseEnter={() => { setHoverData(true); setBodypart('right_arm')     }} onMouseLeave={() => { setHoverData(false) }} className='rpalm'     style={{ width: '17%', height: '11%', position: 'absolute', left: '1%', top: '48%', zIndex: 1, borderRadius: '1.778vh', rotate: '15deg', backgroundColor:'#FF000000' }} />
            <div onMouseEnter={() => { setHoverData(true); setBodypart('right_leg')     }} onMouseLeave={() => { setHoverData(false) }} className='rleg' style={{ width: '23%', height: '47%', position: 'absolute', left: '27%', top: '46%', zIndex: 1, borderRadius: '1.778vh', rotate: '0deg', backgroundColor:'#00FF0000' }} />
            <div onMouseEnter={() => { setHoverData(true); setBodypart('right_leg')     }} onMouseLeave={() => { setHoverData(false) }} className='rfoot' style={{ width: '31%', height: '8%', position: 'absolute', left: '19%', top: '91%', zIndex: 1, borderRadius: '1.778vh', rotate: '0deg', backgroundColor:'#0000FF00' }} />
            <div onMouseEnter={() => { setHoverData(true); setBodypart('left_leg')      }} onMouseLeave={() => { setHoverData(false) }} className='lleg' style={{ width: '23%', height: '47%', position: 'absolute', right: '27%', top: '46%', zIndex: 1, borderRadius: '1.778vh', rotate: '0deg', backgroundColor:'#FFFF0000' }} />
            <div onMouseEnter={() => { setHoverData(true); setBodypart('left_leg')      }} onMouseLeave={() => { setHoverData(false) }} className='lfoot' style={{ width: '31%', height: '8%', position: 'absolute', right: '19%', top: '91%', zIndex: 1, borderRadius: '1.778vh', rotate: '0deg', backgroundColor:'#FF00FF00' }} />
            
            <img className="bodyimage" style={preferences.BodyColor ? { filter: `sepia(100%) saturate(${500/4*damagevar["head"]?.level}%) brightness(100%) hue-rotate(299deg)` } : { filter: ` saturate(300%) brightness(100%) hue-rotate(${75/4*damagevar["head"]?.level}deg)` }} alt="Head" src={ihead} />
            <img className="bodyimage" style={preferences.BodyColor ? { filter: `sepia(100%) saturate(${500/4*damagevar["neck"]?.level}%) brightness(100%) hue-rotate(299deg)` } : { filter: ` saturate(300%) brightness(100%) hue-rotate(${75/4*damagevar["neck"]?.level}deg)` }} alt="Neck" src={ineck} />
            <img className="bodyimage" style={preferences.BodyColor ? { filter: `sepia(100%) saturate(${500/4*damagevar["upper_body"]?.level}%) brightness(100%) hue-rotate(299deg)` } : { filter: ` saturate(300%) brightness(100%) hue-rotate(${75/4*damagevar["upper_body"]?.level}deg)` }} alt="Upperbody" src={iupperbody} />
            <img className="bodyimage" style={preferences.BodyColor ? { filter: `sepia(100%) saturate(${500/4*damagevar["lower_body"]?.level}%) brightness(100%) hue-rotate(299deg)` } : { filter: ` saturate(300%) brightness(100%) hue-rotate(${75/4*damagevar["lower_body"]?.level}deg)` }} alt="Lowerbody" src={ilowerbody} />
            <img className="bodyimage" style={preferences.BodyColor ? { filter: `sepia(100%) saturate(${500/4*damagevar["left_arm"]?.level}%) brightness(100%) hue-rotate(299deg)` } : { filter: ` saturate(300%) brightness(100%) hue-rotate(${75/4*damagevar["left_arm"]?.level}deg)` }} alt="Larm" src={irarm} />
            <img className="bodyimage" style={preferences.BodyColor ? { filter: `sepia(100%) saturate(${500/4*damagevar["left_arm"]?.level}%) brightness(100%) hue-rotate(299deg)` } : { filter: ` saturate(300%) brightness(100%) hue-rotate(${75/4*damagevar["left_arm"]?.level}deg)` }} alt="Lhand" src={irpalm} />
            <img className="bodyimage" style={preferences.BodyColor ? { filter: `sepia(100%) saturate(${500/4*damagevar["right_arm"]?.level}%) brightness(100%) hue-rotate(299deg)` } : { filter: ` saturate(300%) brightness(100%) hue-rotate(${75/4*damagevar["right_arm"]?.level}deg)` }} alt="Rarm" src={ilarm} />
            <img className="bodyimage" style={preferences.BodyColor ? { filter: `sepia(100%) saturate(${500/4*damagevar["right_arm"]?.level}%) brightness(100%) hue-rotate(299deg)` } : { filter: ` saturate(300%) brightness(100%) hue-rotate(${75/4*damagevar["right_arm"]?.level}deg)` }} alt="Rhand" src={ilpalm} />
            <img className="bodyimage" style={preferences.BodyColor ? { filter: `sepia(100%) saturate(${500/4*damagevar["left_leg"]?.level}%) brightness(100%) hue-rotate(299deg)` } : { filter: ` saturate(300%) brightness(100%) hue-rotate(${75/4*damagevar["left_leg"]?.level}deg)` }} alt="Rleg" src={irleg} />
            <img className="bodyimage" style={preferences.BodyColor ? { filter: `sepia(100%) saturate(${500/4*damagevar["left_leg"]?.level}%) brightness(100%) hue-rotate(299deg)` } : { filter: ` saturate(300%) brightness(100%) hue-rotate(${75/4*damagevar["left_leg"]?.level}deg)` }} alt="LFoot" src={irfoot} />
            <img className="bodyimage" style={preferences.BodyColor ? { filter: `sepia(100%) saturate(${500/4*damagevar["right_leg"]?.level}%) brightness(100%) hue-rotate(299deg)` } : { filter: ` saturate(300%) brightness(100%) hue-rotate(${75/4*damagevar["right_leg"]?.level}deg)` }} alt="Lleg" src={illeg} />
            <img className="bodyimage" style={preferences.BodyColor ? { filter: `sepia(100%) saturate(${500/4*damagevar["right_leg"]?.level}%) brightness(100%) hue-rotate(299deg)` } : { filter: ` saturate(300%) brightness(100%) hue-rotate(${75/4*damagevar["right_leg"]?.level}deg)` }} alt='RFoot' src={ilfoot} />

            {/* <Arrowright styles={{
                height: '5%',
                position: 'absolute',
                left: '58%',
                top: '2.8%',
                fontSize: '1.235vh',
                width: '7.467vh',
                textAlign: 'left',
                whiteSpace: 'nowrap',
            }} durablitystyles={{ width: '80%', aspectRatio: '40', left: '20%', top: '-5%', position: 'absolute' }} label={'Head'} percent={(4 - damagevar["head"]?.level)*100/4} />
            <Arrowright styles={{
                height: '5%',
                position: 'absolute',
                left: '74.5%',
                top: '15%',
                fontSize: '1.235vh',
                width: '7.467vh',
                textAlign: 'left',
                whiteSpace: 'nowrap',
            }} durablitystyles={{ width: '80%', aspectRatio: '40', left: '20%', top: '-5%', position: 'absolute' }} label={'Left Arm'} percent={(4 - damagevar["left_arm"]?.level)*100/4} />
            <Arrowright styles={{
                height: '5%',
                position: 'absolute',
                left: '62%',
                top: '79.8%',
                fontSize: '1.235vh',
                width: '7.467vh',
                textAlign: 'left',
                whiteSpace: 'nowrap',
            }} durablitystyles={{ width: '80%', aspectRatio: '40', left: '20%', top: '-5%', position: 'absolute' }} label={'Left Leg'} percent={(4 - damagevar["left_leg"]?.level)*100/4} />
            <Arrowleft styles={{
                height: '5%',
                position: 'absolute',
                right: '73%',
                top: '56.8%',
                fontSize: '1.235vh',
                width: '7.467vh',
                textAlign: 'right',
                whiteSpace: 'nowrap',
            }} durablitystyles={{ width: '80%', aspectRatio: '40', right: '25%', top: '-5%', position: 'absolute' }} label={'Right Leg'} percent={(4 - damagevar["right_leg"]?.level)*100/4} />
            <Arrowright styles={{
                height: '5%',
                position: 'absolute',
                left: '43%',
                top: '28%',
                fontSize: '1.235vh',
                width: '7.467vh',
                textAlign: 'left',
                whiteSpace: 'nowrap',
            }} durablitystyles={{ width: '80%', aspectRatio: '40', left: '20%', top: '-5%', position: 'absolute' }} label={'Body'} percent={(4 - damagevar["upper_body"]?.level)*100/4} />
            <Arrowleft styles={{
                height: '5%',
                width: '7.467vh',
                position: 'absolute',
                right: '71.7%',
                top: '19%',
                fontSize: '1.235vh',
                textAlign: 'right',
                whiteSpace: 'nowrap',
            }} durablitystyles={{ width: '80%', aspectRatio: '40', right: '25%', top: '-5%', position: 'absolute' }} label={'Right Arm'} percent={(4 - damagevar["right_arm"]?.level)*100/4} /> */}
        </div>
    );
};

export default BodyDamage;
