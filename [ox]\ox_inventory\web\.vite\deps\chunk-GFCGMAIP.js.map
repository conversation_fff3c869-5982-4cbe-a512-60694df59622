{"version": 3, "sources": ["../../node_modules/@react-dnd/invariant/src/index.ts"], "sourcesContent": ["/**\n * Use invariant() to assert state which your program assumes to be true.\n *\n * Provide sprintf-style format (only %s is supported) and arguments\n * to provide information about what broke and what you were\n * expecting.\n *\n * The invariant message will be stripped in production, but the invariant\n * will remain to ensure logic does not differ in production.\n */\n\nexport function invariant(condition: any, format: string, ...args: any[]) {\n\tif (isProduction()) {\n\t\tif (format === undefined) {\n\t\t\tthrow new Error('invariant requires an error message argument')\n\t\t}\n\t}\n\n\tif (!condition) {\n\t\tlet error\n\t\tif (format === undefined) {\n\t\t\terror = new Error(\n\t\t\t\t'Minified exception occurred; use the non-minified dev environment ' +\n\t\t\t\t\t'for the full error message and additional helpful warnings.',\n\t\t\t)\n\t\t} else {\n\t\t\tlet argIndex = 0\n\t\t\terror = new Error(\n\t\t\t\tformat.replace(/%s/g, function () {\n\t\t\t\t\treturn args[argIndex++]\n\t\t\t\t}),\n\t\t\t)\n\t\t\terror.name = 'Invariant Violation'\n\t\t}\n\n\t\t;(error as any).framesToPop = 1 // we don't care about invariant's own frame\n\t\tthrow error\n\t}\n}\n\nfunction isProduction() {\n\treturn (\n\t\ttypeof process !== 'undefined' && process.env['NODE_ENV'] === 'production'\n\t)\n}\n"], "mappings": ";AAWO,SAASA,UAAUC,WAAgBC,WAAmBC,MAAa;AACzE,MAAIC,aAAY,GAAI;AACnB,QAAIF,WAAWG,QAAW;AACzB,YAAM,IAAIC,MAAM,8CAA8C;;;AAIhE,MAAI,CAACL,WAAW;AACf,QAAIM;AACJ,QAAIL,WAAWG,QAAW;AACzBE,cAAQ,IAAID,MACX,+HAC8D;WAEzD;AACN,UAAIE,WAAW;AACfD,cAAQ,IAAID,MACXJ,OAAOO,QAAO,OAAQ,WAAY;AACjC,eAAON,KAAKK,UAAU;OACtB,CAAC;AAEHD,YAAMG,OAAO;;AAGZH,UAAcI,cAAc;AAC9B,UAAMJ;;;AAIR,SAASH,eAAe;AACvB,SACC,OAAOQ,YAAY,eAAeA;;", "names": ["invariant", "condition", "format", "args", "isProduction", "undefined", "Error", "error", "argIndex", "replace", "name", "framesToPop", "process"]}