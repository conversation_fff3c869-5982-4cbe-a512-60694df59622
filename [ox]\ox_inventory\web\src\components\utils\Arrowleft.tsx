import Durability from './Durability';

const Arrowleft = ({ styles, durablitystyles, percent, label }: { styles?: React.CSSProperties, durablitystyles?: React.CSSProperties, percent: number, label: string }) => (
    <div style={styles}>
        <Durability percent={percent} label={label} styles={durablitystyles} />
        <svg style={{position: 'relative', top:'90%', left: '0', width: '70%', scale: '-1 1'}} fill="none" viewBox='0 0 50 18'>
            <path d="M2 16 L46 16 L46 2 L45 2 L45 15 L1 15 Z" fill="white" />
            <circle cx="45.5" cy="2.5" r="2" fill="white" />
            <circle cx="2.4" cy="15" r="2" fill="white" />
        </svg>
    </div>
);

export default Arrowleft;