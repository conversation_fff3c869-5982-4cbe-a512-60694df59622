@import "colors.scss";
@import "animations.scss";

// Sections
@import "inventory.scss";
@import "status.scss";

$mainFont: Roboto;

body {
  margin: 0;
  // font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', '<PERSON><PERSON>', 'Oxygen', 'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif;
  font-family: $mainFont;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  height: 100vh;
  background: none !important;
  overflow: hidden !important;
  user-select: none;
  --sectionTitleColor: #9500ff;
  --sectionTitleHue: 275deg;
  --sectionTitleSat: 100%;
  --sectionTitleLum: 7%;
  --secondaryColor: hsl(calc(var(--sectionTitleHue) - 1.2deg) var(--sectionTitleSat) var(--sectionTitleLum));
}

#root {
  height: 100%;
}

code {
  // font-family: source-code-pro, Menlo, Monaco, Consolas, 'Courier New', monospace;
}

::-webkit-scrollbar {
  display: none;
}

p {
  margin: 0;
  padding: 0;
  // font-family: $mainFont;
}

input[type='number']::-webkit-inner-spin-button,
input[type='number']::-webkit-outer-spin-button {
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
}

.app-wrapper {
  height: 100%;
  width: 100%;
  color: white;
}

.popup {
  z-index: 10;
}

.divider {
  width: 100%;
  height: 1px;
  background-color: rgba(255, 255, 255, 0.12);
  margin: .5rem 0;
}

.context-menu-list {
  width: 20vh;
  background-color: $mainColor;
  background-color: rgba(26, 26, 26, 0.9);
  color: $textColor;
  font-family: $mainFont;
  border-color: rgba(0, 0, 0, 0.2);
  border-style: inset;
  border-width: 1px;
  border-radius: $borderRadius;
  outline: none;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  padding-bottom: 0.3vh;
}

.context-menu-item  {
  padding: 0.8vh;
  // border-radius: 4px;
  background-color: transparent;
  outline: none;
  border: none;
  color: $textColor;
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 1.3vh;
  font-family: $mainFont;
  &:active {
    transform: none;
  }
  &:hover {
    background-color: $secondaryColorHighlight;
    cursor: pointer;
  }
}

button:active {
  transform: translateY(3px);
}

.item-drag-preview {
  width: calc($gridSize * 0.6);
  aspect-ratio: 1;
  z-index: 1;
  position: fixed;
  pointer-events: none;
  top: 0;
  left: 0;
  background-repeat: no-repeat;
  background-position: center;
  background-size: 100%;
  image-rendering: -webkit-optimize-contrast;
}

.title {
  margin-left: 1rem;
  font-size: 1.3rem;
  font-weight: 500;
  color: $sectionTitleColor;
}

.useful-controls-dialog {
  background-color: $mainColor;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  color: $textColor;
  width: 450px;
  display: flex;
  flex-direction: column;
  padding: 16px;
  border-radius: 4px;
  gap: 16px;
}

.useful-controls-dialog-overlay {
  background-color: rgba(0, 0, 0, 0.5);
}

.useful-controls-dialog-title {
  display: flex;
  width: 100%;
  justify-content: space-between;
  align-items: center;
  font-size: 18px;
}

.useful-controls-dialog-close {
  width: 25px;
  height: 25px;
  padding: 6px;
  display: flex;
  justify-content: center;
  align-items: center;
  border-radius: 4px;
  fill: $textColor;
  &:hover {
    background-color: $secondaryColorHighlight;
    cursor: pointer;
  }
}

.useful-controls-content-wrapper {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

// Dialog is used for useful controls window
.useful-controls-button {
  position: absolute !important;
  bottom: 25px;
  right: 25px;
  transition: 200ms !important;
  border: none;
  color: white;
  width: 52px;
  height: 52px;
  display: flex;
  justify-content: center;
  align-items: center;
  fill: white;
  border-radius: 5% !important;
  background-color: $secondaryColor !important;
  &:hover {
    background-color: $secondaryColorDark !important;
    cursor: pointer;
  }
}

.useful-controls-exit-button {
  position: absolute !important;
  right: 8px;
  top: 8px;
  border-radius: 2.5% !important;
  color: grey !important;
}

.help-buttons {
  justify-content: left;
  justify-self: center;
  position: relative;
  display: flex;
  flex-direction: row;
  gap: $gridGap;
  bottom: -15vh;
  left: -2vh;
  width: calc(3 * $columnSize + 2 * $columnGap);
  // margin-top: 15vh;
  margin-left: auto;
  margin-right: auto;
}

.help-filler {
  width: 100%;
}

.help-button {
  font-size: 1.481vh;
  width: $gridSize;
  text-align: center;
  padding: 0.3em 0;
  background: rgba(76, 76, 76, 80);
  border-radius: 0.5vh;
  &:hover {
    background: rgba(50, 50, 50, 80);
  }
  &.gold {
    color: #222;
    background: rgba(213, 165, 0, 80);
    &:hover {
      background: rgba(187, 139, 0, 80);
    }
  }
}

// help
.help-wrapper {
  pointer-events: none;
  display: flex;
  background-color: rgba(26, 26, 26, 0.9);
  width: max-content;
  padding: 1vh;
  flex-direction: column;
  min-width: 200px;
  color: $textColor;
  border-radius: $borderRadius;
  border-color: rgba(0, 0, 0, 0.2);
  border-style: inset;
  border-width: 1px;
  p {
    font-size: 1.111vh;
    font-weight: 400;
  }
}

.help-header-wrapper {
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;
  color: $sectionTitleColor;
  p {
    font-size: 1.9vh;
    font-weight: 400;
  }
}
.help-grid {
  display: grid;
  // flex-direction: row;
  grid-template-columns: auto auto;
  gap: 3px 1em;
  font-size: 1.5vh;
}
.help-grid-item {
  align-content: center;
  justify-content: center;
  text-wrap: nowrap;
  justify-self: right;

  &.r {
    justify-self: left;
  }
}
