import React, { useEffect, useMemo, useRef, useState } from 'react';
import { Inventory, Slot } from '../../typings';
import InventorySlot from './InventorySlot';
import { getTotalWeight } from '../../helpers';
import { useAppDispatch, useAppSelector } from '../../store';
import { Items } from '../../store/items';
import { useIntersection } from '../../hooks/useIntersection';
import CollapseArrow from '../utils/CollapseArrow';
import { setItemAmount } from '../../store/inventory';
import { inventoryimagepath } from '../../store/imagepath';
import { fetchNui } from '../../utils/fetchNui';

const PAGE_SIZE = 30;

const InventoryGrid: React.FC<{ inventory: Inventory; icon?: string; showHotbar?: boolean; lower?: boolean; left?: boolean; center?: boolean; solo?: boolean }> = ({ inventory, icon, showHotbar = false, lower = false, left = false, center = false, solo = false }) => {
  const weight = useMemo(
    () => (inventory.maxWeight !== undefined ? Math.floor(getTotalWeight(inventory.items) * 1000) / 1000 : 0),
    [inventory.maxWeight, inventory.items]
  );
  const [page, setPage] = useState(0);
  const [filter, setFilter] = useState('');
  const containerRef = useRef(null);
  const { ref, entry } = useIntersection({ threshold: 0.5 });
  const isBusy = useAppSelector((state) => state.inventory.isBusy);
  const itemAmount = useAppSelector((state) => state.inventory.itemAmount)
  const dispatch = useAppDispatch();
  const getItemName = (item) => item.metadata?.label ? item.metadata.label : Items[item.name]?.label || item.name;
  const isFiltered = (item: Slot) => {
    const itemName = getItemName(item);
    if (!itemName || itemName.toLowerCase().includes(filter.toLowerCase().trim())) {
      return false;
    }

    return true;
  }
  const changeSplit = (data) => {
    data =
      isNaN(data) || data < 0 ? 0 : Math.floor(data);
    dispatch(setItemAmount(data));
    // amt = data;
  }

  useEffect(() => {
    if (entry && entry.isIntersecting) {
      setPage((prev) => ++prev);
    }
  }, [entry]);
  const getIcon = (img: string) => {
    return inventoryimagepath + '/' + img + '.svg'
  }

  return (
    <>
      <div className={`inventory-grid-wrapper${left ? '-hb' : ''}`} style={{ pointerEvents: isBusy ? 'none' : 'auto' }}>
        {!left ?
        <div className={`inventory-grid-header${inventory.type === 'player' ? ' is-player' : ''}`}>
          {icon && <img className="inventory-grid-header-icon" src={getIcon(inventory.type)} />}
          {/* <div className="inventory-grid-header-wrapper"> */}
            <span className="title-weight">
              <span className="title">{inventory.type === 'player' ? 'Player' : inventory.label}</span>
                <span className="weight">
                  {inventory.maxWeight ? (<>{weight / 1000}/{inventory.maxWeight / 1000}</>) : (<>&nbsp;</>)}
                </span>
            </span>
            <div className='blank' />
            {inventory.type === 'player' && (
              <div className="search">
                <input placeholder="Filter by name" onChange={(event) => setFilter(event.target.value)} onFocusCapture={(e)=>fetchNui('focusKeyboard')} onBlur={(e)=>fetchNui('releaseKeyboard')} />
              </div>
            )}
            {inventory.type === 'shop'&& (
              <div className="search">
                <input className='split-input' type='number' min='0' onChange={(i) => changeSplit(i.target.valueAsNumber)} value={itemAmount} onFocusCapture={(e)=>fetchNui('focusKeyboard')} onBlur={(e)=>fetchNui('releaseKeyboard')}
                />
              </div>
            )}
            <div className='collapse' onClick={(e) => {
              if(e.currentTarget.getAttribute('class').includes('collapsed')) {
                e.currentTarget.setAttribute('class', e.currentTarget.getAttribute('class').replace(' collapsed', ''));
              } else {
                e.currentTarget.setAttribute('class', e.currentTarget.getAttribute('class')+' collapsed')
              }
              var z = document.getElementById(inventory.id);
              if(z.getAttribute('class').includes('collapsed')) {
                z.setAttribute('class', z.getAttribute('class').replace(' collapsed', ''));
              } else {
                z.setAttribute('class', z.getAttribute('class')+' collapsed')
              }
            }}>
              <CollapseArrow />
            </div>
          {/* </div> */}
        </div>
        : ''}
        <div className="inventory-grid-hotbar-inventory">
          {left && (
            <div className="inventory-grid-container vertical" ref={containerRef}>
              {inventory.items
                .slice(0, 5)
                .map((item, index) => (
                  <InventorySlot
                    key={`${inventory.type}-${inventory.id}-${item.slot}`}
                    item={item}
                    ref={index === (page + 1) * PAGE_SIZE - 1 ? ref : null}
                    inventoryType={inventory.type}
                    inventoryGroups={inventory.groups}
                    inventoryId={inventory.id}
                    isFiltered={isFiltered(item)}
                    maxWeight={inventory.maxWeight}
                  />
              ))}
            </div>
          )}
          {!left && (
          <div className={`inventory-grid-container${left ? '-left' : ''}${inventory.type === 'player' ? ' is-player' : ''}${solo ? ' solo' : ''}`} id={`${inventory.id}${left ? '-hb' : ''}`} ref={containerRef}>
            {inventory.items.map((item, index) => (
                (inventory.type == 'player' && center && index > 4) || inventory.type != 'player' ?
                <InventorySlot
                  key={`${inventory.type}-${inventory.id}-${item.slot}`}
                  item={item}
                  ref={index === (page + 1) * PAGE_SIZE - 1 ? ref : null}
                  inventoryType={inventory.type}
                  inventoryGroups={inventory.groups}
                  inventoryId={inventory.id}
                  isFiltered={isFiltered(item)}
                  maxWeight={inventory.maxWeight}
                />
                : ''
              ))}
          </div>)}
        </div>
      </div>
    </>
  );
};

export default InventoryGrid;
