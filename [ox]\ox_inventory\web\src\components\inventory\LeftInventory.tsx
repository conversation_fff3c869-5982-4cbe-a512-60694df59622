import PlayerStats from './PlayerStats';
import ClothesButtons from './ClothesButtons';
import { useAppSelector } from '../../store';
import { selectPlayerInventory } from '../../store/inventory';
import InventoryGrid from './InventoryGrid';

const LeftInventory: React.FC = () => {
  const hotBarInventory = useAppSelector(selectPlayerInventory);

  return (
  <div className='inventory-column-left'>
    <ClothesButtons />
    {hotBarInventory != null && (<InventoryGrid key={'hb'+hotBarInventory.id} inventory={hotBarInventory} left={true} />)}
    <PlayerStats/>
  </div>
  );
};

export default LeftInventory;
