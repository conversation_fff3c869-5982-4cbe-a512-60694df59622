import { Inventory } from './inventory';
import { Slot } from './slot';

export type State = {
  playerInventories: Array<Inventory>;
  targetInventories: Array<Inventory>;
  itemAmount: number;
  shiftPressed: boolean;
  isBusy: boolean;
  additionalMetadata: Array<{ metadata: string; value: string }>;
  history?: {
    playerInventories: Array<Inventory>;
    targetInventories: Array<Inventory>;
  };
};
