import { store, useAppSelector } from '../store';
import { DragSource, DropTarget, InventoryType } from '../typings';
import { getTargetInventory, isSlotWithItem } from '../helpers';
import { Items } from '../store/items';
import { craftItem } from '../thunks/craftItem';
import { selectInventoryByID, selectInventoryByType, selectPlayerInventory } from '../store/inventory';

export const onCraft = (source: DragSource, target: DropTarget) => {
  // const state  = store.getState();
  // const sourceInventory = selectInventoryByID(source.inventoryid, state);
  // const targetInventory = selectPlayerInventory(state);
  
  const { inventory: state } = store.getState();

  const { sourceInventory, targetInventory } = getTargetInventory(state, source.inventoryid, target?.inventoryid);

  const sourceSlot = sourceInventory.items[source.item.slot - 1];

  if (!isSlotWithItem(sourceSlot)) throw new Error(`Item ${sourceSlot.slot} name === undefined`);

  if (sourceSlot.count === 0) return;

  const sourceData = Items[sourceSlot.name];

  if (sourceData === undefined) return console.error(`Item ${sourceSlot.name} data undefined!`);

  const targetSlot = targetInventory.items[target.item.slot - 1];

  if (targetSlot === undefined) return console.error(`Target slot undefined`);

  const count = state.itemAmount === 0 ? 1 : state.itemAmount;

  const data = {
    fromSlot: sourceSlot,
    toSlot: targetSlot,
    fromType: sourceInventory.type,
    toType: targetInventory.type,
    fromId: sourceInventory.id,
    toId: targetInventory.id,
    count,
  };

  store.dispatch(
    craftItem({
      ...data,
      fromSlot: sourceSlot.slot,
      toSlot: targetSlot.slot,
    })
  );
};
