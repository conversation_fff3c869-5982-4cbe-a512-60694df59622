import { Inventory, SlotWithItem } from '../../typings';
import React, { Fragment, useMemo } from 'react';
import { Items } from '../../store/items';
import { Locale } from '../../store/locale';
import ReactMarkdown from 'react-markdown';
import { useAppSelector } from '../../store';
import ClockIcon from '../utils/icons/ClockIcon';
import { getItemUrl } from '../../helpers';
import Divider from '../utils/Divider';

const HelpTooltip: React.ForwardRefRenderFunction<
  HTMLDivElement,
  { style: React.CSSProperties }
> = ({ style }, ref) => {

  return (
    <>
      {
        <div className="help-wrapper popup" ref={ref} style={{ ...style}}>
          <div className="help-header-wrapper">
            <p>Useful Controls</p>
          </div>
          <Divider />
          <div className='help-grid'>
            <div className='help-grid-item'>RMB</div>
            <div className='help-grid-item r'>{Locale.ui_rmb}</div>
            <div className='help-grid-item'>ALT + LMB</div>
            <div className='help-grid-item r'>{Locale.ui_alt_lmb}</div>
            <div className='help-grid-item'>CTRL + LMB</div>
            <div className='help-grid-item r'>{Locale.ui_ctrl_lmb}</div>
            <div className='help-grid-item'>SHIFT + Drag</div>
            <div className='help-grid-item r'>{Locale.ui_shift_drag}</div>
            <div className='help-grid-item'>CTRL + SHIFT + LMB</div>
            <div className='help-grid-item r'>{Locale.ui_ctrl_shift_lmb}</div>
          </div>
        </div>
      }
    </>
  );
};

export default React.forwardRef(HelpTooltip);
