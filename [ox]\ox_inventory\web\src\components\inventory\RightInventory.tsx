import InventoryGrid from './InventoryGrid';
import { useAppSelector } from '../../store';
import { selectTargetInventories } from '../../store/inventory';
import Drop from '../../../inventory-images/drop.png';

const RightInventory: React.FC = () => {
  const targetInventories = useAppSelector(selectTargetInventories);

  return (
    <div className='inventory-column'>
      {targetInventories.map((inv, index) => (
        <InventoryGrid key={inv.id} inventory={inv} icon={Drop} solo={targetInventories.length == 1 ? true : false} />
      ))}
    </div>
  );
};

export default RightInventory;
