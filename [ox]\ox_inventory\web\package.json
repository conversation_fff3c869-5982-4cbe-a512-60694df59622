{"name": "ox_inventory", "version": "1.0.0", "homepage": "web/build", "private": true, "dependencies": {"@floating-ui/react": "^0.25.4", "@types/lodash": "^4.14.191", "@types/node": "^18.11.12", "@types/react": "^18.0.26", "@types/react-dom": "^18.0.9", "@types/react-transition-group": "^4.4.5", "@vitejs/plugin-react": "^3.0.0", "lodash": "^4.17.21", "react": "^18.2.0", "react-dnd": "^16.0.1", "react-dnd-touch-backend": "^16.0.1", "react-dom": "^18.2.0", "react-markdown": "^8.0.4", "react-redux": "^8.0.5", "react-transition-group": "^4.4.5", "redux": "^4.2.0", "sass": "^1.56.2"}, "scripts": {"start": "vite", "watch": "vite build --watch", "build": "tsc && vite build", "preview": "vite preview", "format": "prettier --write \"./src/**/*.{ts,tsx,css}\""}, "eslintConfig": {"extends": ["react-app"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"@babel/core": "^7.20.5", "@redux-devtools/core": "^3.13.1", "@redux-devtools/instrument": "^2.1.0", "@reduxjs/toolkit": "^1.9.1", "@types/react-redux": "^7.1.24", "cross-env": "^7.0.3", "csstype": "^2.6.21", "prettier": "^2.8.1", "typescript": "^4.9.4", "vite": "^4.5.2"}}