import React, { useState } from 'react';
import useNuiEvent from '../../hooks/useNuiEvent';
import InventoryHotbar from './InventoryHotbar';
import { store, useAppDispatch, useAppSelector } from '../../store';
import { refreshSlots, setAdditionalMetadata, setItemAmount, setupInventory } from '../../store/inventory';
import { useExitListener } from '../../hooks/useExitListener';
import type { Inventory as InventoryProps } from '../../typings';
import RightInventory from './RightInventory';
import CenterInventory from './CenterInventory';
import LeftInventory from './LeftInventory';
import Tooltip from '../utils/Tooltip';
import { closeTooltip } from '../../store/tooltip';
import InventoryContext from './InventoryContext';
import { closeContextMenu } from '../../store/contextMenu';
import Fade from '../utils/transitions/Fade';
import { toggleHelp, toggleSettings } from '../../store/menus';
import Help from '../utils/Help';
import Settings from '../utils/Settings';
import { fetchNui } from '../../utils/fetchNui';
import { updateClientPreferences } from '../../store/preferences';

const Inventory: React.FC = () => {
  const [inventoryVisible, setInventoryVisible] = useState(false);
  const dispatch = useAppDispatch();
  const help = store.getState().menu.help;
  const preferences = useAppSelector((state) => state.preferences)

  useNuiEvent<boolean>('setInventoryVisible', setInventoryVisible);
  useNuiEvent<false>('closeInventory', () => {
    setInventoryVisible(false);
    dispatch(closeContextMenu());
    dispatch(closeTooltip());
  });
  useExitListener(setInventoryVisible);

  useNuiEvent<{
    // leftInventory?: InventoryProps;
    // bodyInventory?: InventoryProps;
    // playerInventory?: InventoryProps;
    // bagInventory?: InventoryProps;
    // rightInventory?: InventoryProps;
    playerInventories: Array<InventoryProps>;
    targetInventories: Array<InventoryProps>;
  }>('setupInventory', (data) => {
    dispatch(setItemAmount(0))
    dispatch(setupInventory(data));
    !inventoryVisible && setInventoryVisible(true);
  });

  useNuiEvent('refreshSlots', (data) => {
    dispatch(refreshSlots(data))
  });

  useNuiEvent('displayMetadata', (data: Array<{ metadata: string; value: string }>) => {
    dispatch(setAdditionalMetadata(data));
  });

  return (
    <>
      <Fade in={inventoryVisible}>
        <div className="inventory-overlay">
          <div className="inventory-wrapper">
            <div className="inventories">
              <LeftInventory />
              <CenterInventory />
              <RightInventory />
            </div>
            
            <Tooltip />
            <InventoryContext />
            {/* <Context /> */}
            {/* <Split /> */}
          </div>
          <div className="help-buttons">
            <div className="help-button" id="help" onClick={() => {dispatch(toggleHelp())}}>Help</div>
            <div className="help-button" id="settings" onClick={() => {dispatch(toggleSettings());updateClientPreferences(preferences)}}>Settings</div>
            {/* <div className="help-filler" ></div> */}
            <div className="help-button gold" id="donate" onClick={() => {fetchNui('openDonoMenu', {open: true})}}>Be a Star</div>
          </div>
          <Help />
          <Settings />
          {/* <UsefulControls infoVisible={help} setInfoVisible={() => {}} /> */}
        </div>
      </Fade>
      <InventoryHotbar />
    </>
  );
};

export default Inventory;
