@keyframes starshower
{
    0%
    {
        transform: rotate(315deg) translateX(0);
        opacity: 1;
    }
    70%
    {
        opacity: 1;
    }
    100%
    {
        transform: rotate(315deg) translateX(-1000px);
        opacity: 0;
    }
}

section.starshower {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100vh;
  background-color: black;

  span {
    position: absolute;
    top:50%;
    left:50%;
    width: 4px;
    height: 4px;
    background: #fff;
    border-radius: 50%;
    box-shadow: 0 0 0 4px rgba(255,0,255,0.1),0 0 0 8px rgba(255,0,255,0.1),0 0 20px rgba(255,0,255,0.1);
    animation: animate 10s linear infinite;
    opacity: 50%;
    
    &::before {
      content:'';
      position: absolute;
      top: 50%;
      transform: translateY(-50%);
      width: 300px;
      height: 1px;
      background: linear-gradient(90deg,#f0f,transparent);
      opacity: 50%;
    }

    &:nth-child(1) {
        top: 0;
        right: 0;
        left: initial;
        animation-delay: 0s;
        animation-duration: 3s;
    }

    &:nth-child(2) {
        top: 0;
        right: 80px;
        left: initial;
        animation-delay: 0.2s;
        animation-duration: 4s;
    }

    &:nth-child(3) {
        top: 80;
        right: 0px;
        left: initial;
        animation-delay: 0.4s;
        animation-duration: 3s;
    }

    &:nth-child(4) {
        top: 0;
        right: 180px;
        left: initial;
        animation-delay: 0.6s;
        animation-duration: 2.5s;
    }

    &:nth-child(5) {
        top: 0;
        right: 400px;
        left: initial;
        animation-delay: 0.8s;
        animation-duration: 4.5s;
    }

    &:nth-child(6) {
        top: 0;
        right: 600px;
        left: initial;
        animation-delay: 1s;
        animation-duration: 4s;
    }

    &:nth-child(7) {
        top: 300px;
        right: 0px;
        left: initial;
        animation-delay: 1.2s;
        animation-duration: 1.75s;
    }

    &:nth-child(8) {
        top: 0px;
        right: 700px;
        left: initial;
        animation-delay: 1.4s;
        animation-duration: 7.25s;
    }

    &:nth-child(9) {
        top: 0px;
        right: 1000px;
        left: initial;
        animation-delay: 0.75s;
        animation-duration: 2.25s;
    }

    &:nth-child(9) {
        top: 0px;
        right: 450px;
        left: initial;
        animation-delay: 2.75s;
        animation-duration: 2.75s;
    }
  }
}