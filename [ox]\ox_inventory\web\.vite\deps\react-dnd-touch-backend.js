import {
  invariant
} from "./chunk-GFCGMAIP.js";
import "./chunk-5WWUZCGV.js";

// node_modules/react-dnd-touch-backend/dist/interfaces.js
var ListenerType;
(function(ListenerType2) {
  ListenerType2["mouse"] = "mouse";
  ListenerType2["touch"] = "touch";
  ListenerType2["keyboard"] = "keyboard";
})(ListenerType || (ListenerType = {}));

// node_modules/react-dnd-touch-backend/dist/OptionsReader.js
var OptionsReader = class {
  get delay() {
    var _delay;
    return (_delay = this.args.delay) !== null && _delay !== void 0 ? _delay : 0;
  }
  get scrollAngleRanges() {
    return this.args.scrollAngleRanges;
  }
  get getDropTargetElementsAtPoint() {
    return this.args.getDropTargetElementsAtPoint;
  }
  get ignoreContextMenu() {
    var _ignoreContextMenu;
    return (_ignoreContextMenu = this.args.ignoreContextMenu) !== null && _ignoreContextMenu !== void 0 ? _ignoreContextMenu : false;
  }
  get enableHoverOutsideTarget() {
    var _enableHoverOutsideTarget;
    return (_enableHoverOutsideTarget = this.args.enableHoverOutsideTarget) !== null && _enableHoverOutsideTarget !== void 0 ? _enableHoverOutsideTarget : false;
  }
  get enableKeyboardEvents() {
    var _enableKeyboardEvents;
    return (_enableKeyboardEvents = this.args.enableKeyboardEvents) !== null && _enableKeyboardEvents !== void 0 ? _enableKeyboardEvents : false;
  }
  get enableMouseEvents() {
    var _enableMouseEvents;
    return (_enableMouseEvents = this.args.enableMouseEvents) !== null && _enableMouseEvents !== void 0 ? _enableMouseEvents : false;
  }
  get enableTouchEvents() {
    var _enableTouchEvents;
    return (_enableTouchEvents = this.args.enableTouchEvents) !== null && _enableTouchEvents !== void 0 ? _enableTouchEvents : true;
  }
  get touchSlop() {
    return this.args.touchSlop || 0;
  }
  get delayTouchStart() {
    var ref, ref1;
    var ref2, ref3;
    return (ref3 = (ref2 = (ref = this.args) === null || ref === void 0 ? void 0 : ref.delayTouchStart) !== null && ref2 !== void 0 ? ref2 : (ref1 = this.args) === null || ref1 === void 0 ? void 0 : ref1.delay) !== null && ref3 !== void 0 ? ref3 : 0;
  }
  get delayMouseStart() {
    var ref, ref4;
    var ref5, ref6;
    return (ref6 = (ref5 = (ref = this.args) === null || ref === void 0 ? void 0 : ref.delayMouseStart) !== null && ref5 !== void 0 ? ref5 : (ref4 = this.args) === null || ref4 === void 0 ? void 0 : ref4.delay) !== null && ref6 !== void 0 ? ref6 : 0;
  }
  get window() {
    if (this.context && this.context.window) {
      return this.context.window;
    } else if (typeof window !== "undefined") {
      return window;
    }
    return void 0;
  }
  get document() {
    var ref;
    if ((ref = this.context) === null || ref === void 0 ? void 0 : ref.document) {
      return this.context.document;
    }
    if (this.window) {
      return this.window.document;
    }
    return void 0;
  }
  get rootElement() {
    var ref;
    return ((ref = this.args) === null || ref === void 0 ? void 0 : ref.rootElement) || this.document;
  }
  constructor(args, context) {
    this.args = args;
    this.context = context;
  }
};

// node_modules/react-dnd-touch-backend/dist/utils/math.js
function distance(x1, y1, x2, y2) {
  return Math.sqrt(Math.pow(Math.abs(x2 - x1), 2) + Math.pow(Math.abs(y2 - y1), 2));
}
function inAngleRanges(x1, y1, x2, y2, angleRanges) {
  if (!angleRanges) {
    return false;
  }
  const angle = Math.atan2(y2 - y1, x2 - x1) * 180 / Math.PI + 180;
  for (let i = 0; i < angleRanges.length; ++i) {
    const ar = angleRanges[i];
    if (ar && (ar.start == null || angle >= ar.start) && (ar.end == null || angle <= ar.end)) {
      return true;
    }
  }
  return false;
}

// node_modules/react-dnd-touch-backend/dist/utils/predicates.js
var MouseButtons = {
  Left: 1,
  Right: 2,
  Center: 4
};
var MouseButton = {
  Left: 0,
  Center: 1,
  Right: 2
};
function eventShouldStartDrag(e) {
  return e.button === void 0 || e.button === MouseButton.Left;
}
function eventShouldEndDrag(e) {
  return e.buttons === void 0 || (e.buttons & MouseButtons.Left) === 0;
}
function isTouchEvent(e) {
  return !!e.targetTouches;
}

// node_modules/react-dnd-touch-backend/dist/utils/offsets.js
var ELEMENT_NODE = 1;
function getNodeClientOffset(node) {
  const el = node.nodeType === ELEMENT_NODE ? node : node.parentElement;
  if (!el) {
    return void 0;
  }
  const { top, left } = el.getBoundingClientRect();
  return {
    x: left,
    y: top
  };
}
function getEventClientTouchOffset(e, lastTargetTouchFallback) {
  if (e.targetTouches.length === 1) {
    return getEventClientOffset(e.targetTouches[0]);
  } else if (lastTargetTouchFallback && e.touches.length === 1) {
    if (e.touches[0].target === lastTargetTouchFallback.target) {
      return getEventClientOffset(e.touches[0]);
    }
  }
  return;
}
function getEventClientOffset(e, lastTargetTouchFallback) {
  if (isTouchEvent(e)) {
    return getEventClientTouchOffset(e, lastTargetTouchFallback);
  } else {
    return {
      x: e.clientX,
      y: e.clientY
    };
  }
}

// node_modules/react-dnd-touch-backend/dist/utils/supportsPassive.js
var supportsPassive = (() => {
  let supported = false;
  try {
    addEventListener("test", () => {
    }, Object.defineProperty({}, "passive", {
      get() {
        supported = true;
        return true;
      }
    }));
  } catch (e) {
  }
  return supported;
})();

// node_modules/react-dnd-touch-backend/dist/TouchBackendImpl.js
var eventNames = {
  [ListenerType.mouse]: {
    start: "mousedown",
    move: "mousemove",
    end: "mouseup",
    contextmenu: "contextmenu"
  },
  [ListenerType.touch]: {
    start: "touchstart",
    move: "touchmove",
    end: "touchend"
  },
  [ListenerType.keyboard]: {
    keydown: "keydown"
  }
};
var TouchBackendImpl = class _TouchBackendImpl {
  /**
  * Generate profiling statistics for the HTML5Backend.
  */
  profile() {
    var ref;
    return {
      sourceNodes: this.sourceNodes.size,
      sourcePreviewNodes: this.sourcePreviewNodes.size,
      sourcePreviewNodeOptions: this.sourcePreviewNodeOptions.size,
      targetNodes: this.targetNodes.size,
      dragOverTargetIds: ((ref = this.dragOverTargetIds) === null || ref === void 0 ? void 0 : ref.length) || 0
    };
  }
  // public for test
  get document() {
    return this.options.document;
  }
  setup() {
    const root = this.options.rootElement;
    if (!root) {
      return;
    }
    invariant(!_TouchBackendImpl.isSetUp, "Cannot have two Touch backends at the same time.");
    _TouchBackendImpl.isSetUp = true;
    this.addEventListener(root, "start", this.getTopMoveStartHandler());
    this.addEventListener(root, "start", this.handleTopMoveStartCapture, true);
    this.addEventListener(root, "move", this.handleTopMove);
    this.addEventListener(root, "move", this.handleTopMoveCapture, true);
    this.addEventListener(root, "end", this.handleTopMoveEndCapture, true);
    if (this.options.enableMouseEvents && !this.options.ignoreContextMenu) {
      this.addEventListener(root, "contextmenu", this.handleTopMoveEndCapture);
    }
    if (this.options.enableKeyboardEvents) {
      this.addEventListener(root, "keydown", this.handleCancelOnEscape, true);
    }
  }
  teardown() {
    const root = this.options.rootElement;
    if (!root) {
      return;
    }
    _TouchBackendImpl.isSetUp = false;
    this._mouseClientOffset = {};
    this.removeEventListener(root, "start", this.handleTopMoveStartCapture, true);
    this.removeEventListener(root, "start", this.handleTopMoveStart);
    this.removeEventListener(root, "move", this.handleTopMoveCapture, true);
    this.removeEventListener(root, "move", this.handleTopMove);
    this.removeEventListener(root, "end", this.handleTopMoveEndCapture, true);
    if (this.options.enableMouseEvents && !this.options.ignoreContextMenu) {
      this.removeEventListener(root, "contextmenu", this.handleTopMoveEndCapture);
    }
    if (this.options.enableKeyboardEvents) {
      this.removeEventListener(root, "keydown", this.handleCancelOnEscape, true);
    }
    this.uninstallSourceNodeRemovalObserver();
  }
  addEventListener(subject, event, handler, capture = false) {
    const options = supportsPassive ? {
      capture,
      passive: false
    } : capture;
    this.listenerTypes.forEach(function(listenerType) {
      const evt = eventNames[listenerType][event];
      if (evt) {
        subject.addEventListener(evt, handler, options);
      }
    });
  }
  removeEventListener(subject, event, handler, capture = false) {
    const options = supportsPassive ? {
      capture,
      passive: false
    } : capture;
    this.listenerTypes.forEach(function(listenerType) {
      const evt = eventNames[listenerType][event];
      if (evt) {
        subject.removeEventListener(evt, handler, options);
      }
    });
  }
  connectDragSource(sourceId, node) {
    const handleMoveStart = this.handleMoveStart.bind(this, sourceId);
    this.sourceNodes.set(sourceId, node);
    this.addEventListener(node, "start", handleMoveStart);
    return () => {
      this.sourceNodes.delete(sourceId);
      this.removeEventListener(node, "start", handleMoveStart);
    };
  }
  connectDragPreview(sourceId, node, options) {
    this.sourcePreviewNodeOptions.set(sourceId, options);
    this.sourcePreviewNodes.set(sourceId, node);
    return () => {
      this.sourcePreviewNodes.delete(sourceId);
      this.sourcePreviewNodeOptions.delete(sourceId);
    };
  }
  connectDropTarget(targetId, node) {
    const root = this.options.rootElement;
    if (!this.document || !root) {
      return () => {
      };
    }
    const handleMove = (e) => {
      if (!this.document || !root || !this.monitor.isDragging()) {
        return;
      }
      let coords;
      switch (e.type) {
        case eventNames.mouse.move:
          coords = {
            x: e.clientX,
            y: e.clientY
          };
          break;
        case eventNames.touch.move:
          var ref, ref1;
          coords = {
            x: ((ref = e.touches[0]) === null || ref === void 0 ? void 0 : ref.clientX) || 0,
            y: ((ref1 = e.touches[0]) === null || ref1 === void 0 ? void 0 : ref1.clientY) || 0
          };
          break;
      }
      const droppedOn = coords != null ? this.document.elementFromPoint(coords.x, coords.y) : void 0;
      const childMatch = droppedOn && node.contains(droppedOn);
      if (droppedOn === node || childMatch) {
        return this.handleMove(e, targetId);
      }
    };
    this.addEventListener(this.document.body, "move", handleMove);
    this.targetNodes.set(targetId, node);
    return () => {
      if (this.document) {
        this.targetNodes.delete(targetId);
        this.removeEventListener(this.document.body, "move", handleMove);
      }
    };
  }
  getTopMoveStartHandler() {
    if (!this.options.delayTouchStart && !this.options.delayMouseStart) {
      return this.handleTopMoveStart;
    }
    return this.handleTopMoveStartDelay;
  }
  installSourceNodeRemovalObserver(node) {
    this.uninstallSourceNodeRemovalObserver();
    this.draggedSourceNode = node;
    this.draggedSourceNodeRemovalObserver = new MutationObserver(() => {
      if (node && !node.parentElement) {
        this.resurrectSourceNode();
        this.uninstallSourceNodeRemovalObserver();
      }
    });
    if (!node || !node.parentElement) {
      return;
    }
    this.draggedSourceNodeRemovalObserver.observe(node.parentElement, {
      childList: true
    });
  }
  resurrectSourceNode() {
    if (this.document && this.draggedSourceNode) {
      this.draggedSourceNode.style.display = "none";
      this.draggedSourceNode.removeAttribute("data-reactid");
      this.document.body.appendChild(this.draggedSourceNode);
    }
  }
  uninstallSourceNodeRemovalObserver() {
    if (this.draggedSourceNodeRemovalObserver) {
      this.draggedSourceNodeRemovalObserver.disconnect();
    }
    this.draggedSourceNodeRemovalObserver = void 0;
    this.draggedSourceNode = void 0;
  }
  constructor(manager, context, options) {
    this.getSourceClientOffset = (sourceId) => {
      const element = this.sourceNodes.get(sourceId);
      return element && getNodeClientOffset(element);
    };
    this.handleTopMoveStartCapture = (e) => {
      if (!eventShouldStartDrag(e)) {
        return;
      }
      this.moveStartSourceIds = [];
    };
    this.handleMoveStart = (sourceId) => {
      if (Array.isArray(this.moveStartSourceIds)) {
        this.moveStartSourceIds.unshift(sourceId);
      }
    };
    this.handleTopMoveStart = (e) => {
      if (!eventShouldStartDrag(e)) {
        return;
      }
      const clientOffset = getEventClientOffset(e);
      if (clientOffset) {
        if (isTouchEvent(e)) {
          this.lastTargetTouchFallback = e.targetTouches[0];
        }
        this._mouseClientOffset = clientOffset;
      }
      this.waitingForDelay = false;
    };
    this.handleTopMoveStartDelay = (e) => {
      if (!eventShouldStartDrag(e)) {
        return;
      }
      const delay = e.type === eventNames.touch.start ? this.options.delayTouchStart : this.options.delayMouseStart;
      this.timeout = setTimeout(this.handleTopMoveStart.bind(this, e), delay);
      this.waitingForDelay = true;
    };
    this.handleTopMoveCapture = () => {
      this.dragOverTargetIds = [];
    };
    this.handleMove = (_evt, targetId) => {
      if (this.dragOverTargetIds) {
        this.dragOverTargetIds.unshift(targetId);
      }
    };
    this.handleTopMove = (e1) => {
      if (this.timeout) {
        clearTimeout(this.timeout);
      }
      if (!this.document || this.waitingForDelay) {
        return;
      }
      const { moveStartSourceIds, dragOverTargetIds } = this;
      const enableHoverOutsideTarget = this.options.enableHoverOutsideTarget;
      const clientOffset = getEventClientOffset(e1, this.lastTargetTouchFallback);
      if (!clientOffset) {
        return;
      }
      if (this._isScrolling || !this.monitor.isDragging() && inAngleRanges(this._mouseClientOffset.x || 0, this._mouseClientOffset.y || 0, clientOffset.x, clientOffset.y, this.options.scrollAngleRanges)) {
        this._isScrolling = true;
        return;
      }
      if (!this.monitor.isDragging() && // eslint-disable-next-line no-prototype-builtins
      this._mouseClientOffset.hasOwnProperty("x") && moveStartSourceIds && distance(this._mouseClientOffset.x || 0, this._mouseClientOffset.y || 0, clientOffset.x, clientOffset.y) > (this.options.touchSlop ? this.options.touchSlop : 0)) {
        this.moveStartSourceIds = void 0;
        this.actions.beginDrag(moveStartSourceIds, {
          clientOffset: this._mouseClientOffset,
          getSourceClientOffset: this.getSourceClientOffset,
          publishSource: false
        });
      }
      if (!this.monitor.isDragging()) {
        return;
      }
      const sourceNode = this.sourceNodes.get(this.monitor.getSourceId());
      this.installSourceNodeRemovalObserver(sourceNode);
      this.actions.publishDragSource();
      if (e1.cancelable)
        e1.preventDefault();
      const dragOverTargetNodes = (dragOverTargetIds || []).map(
        (key) => this.targetNodes.get(key)
      ).filter(
        (e) => !!e
      );
      const elementsAtPoint = this.options.getDropTargetElementsAtPoint ? this.options.getDropTargetElementsAtPoint(clientOffset.x, clientOffset.y, dragOverTargetNodes) : this.document.elementsFromPoint(clientOffset.x, clientOffset.y);
      const elementsAtPointExtended = [];
      for (const nodeId in elementsAtPoint) {
        if (!elementsAtPoint.hasOwnProperty(nodeId)) {
          continue;
        }
        let currentNode = elementsAtPoint[nodeId];
        if (currentNode != null) {
          elementsAtPointExtended.push(currentNode);
        }
        while (currentNode) {
          currentNode = currentNode.parentElement;
          if (currentNode && elementsAtPointExtended.indexOf(currentNode) === -1) {
            elementsAtPointExtended.push(currentNode);
          }
        }
      }
      const orderedDragOverTargetIds = elementsAtPointExtended.filter(
        (node) => dragOverTargetNodes.indexOf(node) > -1
      ).map(
        (node) => this._getDropTargetId(node)
      ).filter(
        (node) => !!node
      ).filter(
        (id, index, ids) => ids.indexOf(id) === index
      );
      if (enableHoverOutsideTarget) {
        for (const targetId in this.targetNodes) {
          const targetNode = this.targetNodes.get(targetId);
          if (sourceNode && targetNode && targetNode.contains(sourceNode) && orderedDragOverTargetIds.indexOf(targetId) === -1) {
            orderedDragOverTargetIds.unshift(targetId);
            break;
          }
        }
      }
      orderedDragOverTargetIds.reverse();
      this.actions.hover(orderedDragOverTargetIds, {
        clientOffset
      });
    };
    this._getDropTargetId = (node) => {
      const keys = this.targetNodes.keys();
      let next = keys.next();
      while (next.done === false) {
        const targetId = next.value;
        if (node === this.targetNodes.get(targetId)) {
          return targetId;
        } else {
          next = keys.next();
        }
      }
      return void 0;
    };
    this.handleTopMoveEndCapture = (e) => {
      this._isScrolling = false;
      this.lastTargetTouchFallback = void 0;
      if (!eventShouldEndDrag(e)) {
        return;
      }
      if (!this.monitor.isDragging() || this.monitor.didDrop()) {
        this.moveStartSourceIds = void 0;
        return;
      }
      if (e.cancelable)
        e.preventDefault();
      this._mouseClientOffset = {};
      this.uninstallSourceNodeRemovalObserver();
      this.actions.drop();
      this.actions.endDrag();
    };
    this.handleCancelOnEscape = (e) => {
      if (e.key === "Escape" && this.monitor.isDragging()) {
        this._mouseClientOffset = {};
        this.uninstallSourceNodeRemovalObserver();
        this.actions.endDrag();
      }
    };
    this.options = new OptionsReader(options, context);
    this.actions = manager.getActions();
    this.monitor = manager.getMonitor();
    this.sourceNodes = /* @__PURE__ */ new Map();
    this.sourcePreviewNodes = /* @__PURE__ */ new Map();
    this.sourcePreviewNodeOptions = /* @__PURE__ */ new Map();
    this.targetNodes = /* @__PURE__ */ new Map();
    this.listenerTypes = [];
    this._mouseClientOffset = {};
    this._isScrolling = false;
    if (this.options.enableMouseEvents) {
      this.listenerTypes.push(ListenerType.mouse);
    }
    if (this.options.enableTouchEvents) {
      this.listenerTypes.push(ListenerType.touch);
    }
    if (this.options.enableKeyboardEvents) {
      this.listenerTypes.push(ListenerType.keyboard);
    }
  }
};

// node_modules/react-dnd-touch-backend/dist/index.js
var TouchBackend = function createBackend(manager, context = {}, options = {}) {
  return new TouchBackendImpl(manager, context, options);
};
export {
  ListenerType,
  TouchBackend,
  TouchBackendImpl
};
//# sourceMappingURL=react-dnd-touch-backend.js.map
