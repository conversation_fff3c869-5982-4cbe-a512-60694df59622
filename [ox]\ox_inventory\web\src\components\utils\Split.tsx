import { flip, FloatingPortal, offset, shift, useFloating, useTransitionStyles } from '@floating-ui/react';
import React, { useEffect, useState } from 'react';
import { store, useAppDispatch, useAppSelector } from '../../store';
import SlotTooltip from '../inventory/SlotTooltip';
import { closeSplit, setSplit } from '../../store/menus';
import { setItemAmount } from '../../store/inventory';

const Split: React.FC = () => {
  const split = useAppSelector((state) => state.menu);
  const dispatch = useAppDispatch();

  const handleClick = ( data ) => {
    if(data.action = 'close') 
      dispatch(closeSplit())
  }

  const changeSplit = (data) => {
    if(data) {
      dispatch(setSplit({count:data}));
      dispatch(setItemAmount(data));
    }
  }

  // const { refs, context, floatingStyles } = useFloating({
  //   middleware: [flip(), shift(), offset({ mainAxis: 10, crossAxis: 10 })],
  //   open: hoverData.open,
  //   placement: 'right-start',
  // });

  // const { isMounted, styles } = useTransitionStyles(context, {
  //   duration: 200,
  // });

  // const handleMouseMove = ({ clientX, clientY }: MouseEvent | React.MouseEvent<unknown, MouseEvent>) => {
  //   refs.setPositionReference({
  //     getBoundingClientRect() {
  //       return {
  //         width: 0,
  //         height: 0,
  //         x: clientX,
  //         y: clientY,
  //         left: clientX,
  //         top: clientY,
  //         right: clientX,
  //         bottom: clientY,
  //       };
  //     },
  //   });
  // };

  // useEffect(() => {
  //   window.addEventListener('mousemove', handleMouseMove);

  //   return () => {
  //     window.removeEventListener('mousemove', handleMouseMove);
  //   };
  // }, []);

  return (
    <>
      {split.open && (
          <div className='split-popup'>
              {/* <input className='split-input' type='number' max={split.maxCount} value={split.count} onChange={(i) => changeSplit(i.target.value)} />
              <input className='split-slider' type='range' min='1' max={split.maxCount} step='1' value={split.count} onChange={(i) => changeSplit(i.target.value)} onFocusCapture={(e)=>fetchNui('focusKeyboard')} onBlur={(e)=>fetchNui('releaseKeyboard')} /> */}
              <div className='split-popup-close' onClick={() => handleClick({ action : 'close'})}>Done</div>
          </div>
      )}
    </>
  );
};

export default Split;
