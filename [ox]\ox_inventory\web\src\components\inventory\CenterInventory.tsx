import InventoryGrid from './InventoryGrid';
import { useAppSelector } from '../../store';
import { selectPlayerInventories } from '../../store/inventory';
import Backpack from '../../../inventory-images/backpack.png';
import BodyIcon from '../../../inventory-images/body.png';
import { InventoryType } from '../../typings';

const CenterInventory: React.FC = () => {
  // const playerInventory = useAppSelector(selectPlayerInventory);
  const playerInventories = useAppSelector((state) => selectPlayerInventories(state));
  // // const bagInventory = useAppSelector(selectBagInventory);
  // const bagInventory = useAppSelector((state) => selectInventoryByType(InventoryType.BAG, state));
  // const bodyInventory = useAppSelector((state) => selectInventoryByType(InventoryType.BODY, state));
  // const chooseBodyIcon = (inventoryType: InventoryType) => {
    
  // }


  return (
    <div className='inventory-column'>
      {playerInventories.map((inv, index) => (
        <InventoryGrid key={inv.id} inventory={inv} icon={BodyIcon} center />
      ))}
      {/* {playerInventory != null ? <InventoryGrid inventory={playerInventory} icon={BodyIcon} /> : ''}
      {bagInventory != null ? <InventoryGrid inventory={bagInventory} icon={Backpack} /> : ''}
      {bodyInventory != null ? <InventoryGrid inventory={bodyInventory} icon={BodyIcon} /> : ''} */}
      {/* <InventoryGrid inventory={bagInventory} icon={Backpack} lower/> */}
    </div>
  );
};

export default CenterInventory;
