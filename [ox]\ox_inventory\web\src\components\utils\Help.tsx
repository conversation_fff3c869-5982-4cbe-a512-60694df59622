import { autoUpdate, flip, FloatingPortal, offset, shift, useFloating, useTransitionStyles } from '@floating-ui/react';
import React, { useEffect } from 'react';
import { useAppDispatch, useAppSelector } from '../../store';
import HelpTooltip from '../inventory/HelpTooltip';

const Help: React.FC = () => {
  const hoverData = useAppSelector((state) => state.menu);
  const dispatch = useAppDispatch();
  var x = 0;
  var y = 0;

  const { refs, context, floatingStyles } = useFloating({
    whileElementsMounted: autoUpdate,
    middleware: [flip(), shift(), offset({ mainAxis: 10, crossAxis: 10 })],
    open: hoverData.help,
    placement: 'right-end',
  });

  const { isMounted, styles } = useTransitionStyles(context, {
    duration: 200,
  });

  const handleMouseMove = ({ clientX, clientY }: MouseEvent | React.MouseEvent<unknown, MouseEvent>) => {
    refs.setPositionReference({
      getBoundingClientRect() {
        // console.log(clientX, clientY)
        if (x == 0) {
          x = clientX;
          y = clientY;
        }
        return {
          width: 0,
          height: 0,
          x: clientX,
          y: clientY,
          left: clientX,
          top: clientY,
          right: clientX,
          bottom: clientY,
        };
      },
    });
  };

  useEffect(() => {
    var elem = document.getElementById('help');
    elem.addEventListener('click', handleMouseMove,);
    

    return () => {
      elem.removeEventListener('click', handleMouseMove,);
    };
  }, []);

  return (
    <>
      {isMounted && hoverData.help && (
        <FloatingPortal>
          <HelpTooltip
            ref={refs.setFloating}
            style={{ ...floatingStyles, ...styles }}
          />
        </FloatingPortal>
      )}
    </>
  );
};

export default Help;
