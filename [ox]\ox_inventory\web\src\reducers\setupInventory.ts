import { CaseReducer, PayloadAction } from '@reduxjs/toolkit';
import { getItemData, itemDurability } from '../helpers';
import { Items } from '../store/items';
import { Inventory, State } from '../typings';

export const setupInventoryReducer: CaseReducer<
  State,
  PayloadAction<{
    playerInventories: Array<Inventory>;
    targetInventories: Array<Inventory>;
    // inventoryList?: Array<Inventory>;
    // leftInventory?: Inventory;
    // rightInventory?: Inventory;
    // bodyInventory?: Inventory;
    // playerInventory?: Inventory;
    // bagInventory?: Inventory;
  }>
> = (state, action) => {
  const { playerInventories, targetInventories } = action.payload;
  const curTime = Math.floor(Date.now() / 1000);
    
  if(playerInventories) {
    // console.log('playerInventories ',playerInventories.length,' ',playerInventories);
    state.playerInventories = [];
    playerInventories.forEach(element => {
      state.playerInventories.push({
        ...element,
        items: Array.from(Array(element.slots), (_, index) => {
          const item = Object.values(element.items).find((item) => item?.slot === index + 1) || {
            slot: index + 1,
          };

          if (!item.name) return item;

          if (typeof Items[item.name] === 'undefined') {
            getItemData(item.name);
          }

          item.durability = itemDurability(item.metadata, curTime);
          return item;
        }),
      })
    });
  }
  
  if(targetInventories) {
    // console.log('targetInventories ',targetInventories.length,' ',targetInventories);
    state.targetInventories = []
    targetInventories.forEach(element => {
      state.targetInventories.push({
        ...element,
        items: Array.from(Array(element.slots), (_, index) => {
          const item = Object.values(element.items).find((item) => item?.slot === index + 1) || {
            slot: index + 1,
          };

          if (!item.name) return item;

          if (typeof Items[item.name] === 'undefined') {
            getItemData(item.name);
          }

          item.durability = itemDurability(item.metadata, curTime);
          return item;
        }),
      })
    });
  }
    
  

  // if (leftInventory)
  //   state.leftInventory = {
  //     ...leftInventory,
  //     items: Array.from(Array(leftInventory.slots), (_, index) => {
  //       const item = Object.values(leftInventory.items).find((item) => item?.slot === index + 1) || {
  //         slot: index + 1,
  //       };

  //       if (!item.name) return item;

  //       if (typeof Items[item.name] === 'undefined') {
  //         getItemData(item.name);
  //       }

  //       item.durability = itemDurability(item.metadata, curTime);
  //       return item;
  //     }),
  //   };

  //   if (rightInventory)
  //     state.rightInventory = {
  //       ...rightInventory,
  //       items: Array.from(Array(rightInventory.slots), (_, index) => {
  //         const item = Object.values(rightInventory.items).find((item) => item?.slot === index + 1) || {
  //           slot: index + 1,
  //         };
  
  //         if (!item.name) return item;
  
  //         if (typeof Items[item.name] === 'undefined') {
  //           getItemData(item.name);
  //         }
  
  //         item.durability = itemDurability(item.metadata, curTime);
  //         return item;
  //       }),
  //     };

  //     if (bodyInventory)
  //       state.bodyInventory = {
  //         ...bodyInventory,
  //         items: Array.from(Array(bodyInventory.slots), (_, index) => {
  //           const item = Object.values(bodyInventory.items).find((item) => item?.slot === index + 1) || {
  //             slot: index + 1,
  //           };
    
  //           if (!item.name) return item;
    
  //           if (typeof Items[item.name] === 'undefined') {
  //             getItemData(item.name);
  //           }
    
  //           item.durability = itemDurability(item.metadata, curTime);
  //           return item;
  //         }),
  //       };

  //       if (playerInventory)
  //         state.playerInventory = {
  //           ...playerInventory,
  //           items: Array.from(Array(playerInventory.slots), (_, index) => {
  //             const item = Object.values(playerInventory.items).find((item) => item?.slot === index + 1) || {
  //               slot: index + 1,
  //             };
      
  //             if (!item.name) return item;
      
  //             if (typeof Items[item.name] === 'undefined') {
  //               getItemData(item.name);
  //             }
      
  //             item.durability = itemDurability(item.metadata, curTime);
  //             return item;
  //           }),
  //         };

  //         if (bagInventory)
  //           state.bagInventory = {
  //             ...bagInventory,
  //             items: Array.from(Array(bagInventory.slots), (_, index) => {
  //               const item = Object.values(bagInventory.items).find((item) => item?.slot === index + 1) || {
  //                 slot: index + 1,
  //               };
        
  //               if (!item.name) return item;
        
  //               if (typeof Items[item.name] === 'undefined') {
  //                 getItemData(item.name);
  //               }
        
  //               item.durability = itemDurability(item.metadata, curTime);
  //               return item;
  //             }),
  //           };

  state.isBusy = false;
};
