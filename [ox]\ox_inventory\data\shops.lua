return {
	-- major stores
-- General = {
-- 	name = 'Conv. Store',
-- 	blip = {
-- 		id = 59, colour = 69, scale = 0.8
-- 	}, 
-- 	inventory = {

-- 	}, 
-- 	locations = {

-- 	}, 
-- 	targets = {

-- 	},
-- },

-- Liquor = {
-- 	name = 'Liquor Store',
-- 	blip = {
-- 		id = 93, colour = 27, scale = 0.8
-- 	}, inventory = {

		
-- 	}, locations = {

-- 	}, targets = {

-- 	},
-- },

-- YouTool = {
-- 	name = 'YouTool',
-- 	blip = {
-- 		id = 402, colour = 69, scale = 0.8
-- 	}, inventory = {

-- 	}, locations = {

-- 	}, targets = {
		
-- 	}
-- },

-- Ammunation = {
-- 	name = 'Ammunation',
-- 	blip = {
-- 		id = 110, colour = 69, scale = 0.8
-- 	}, inventory = {

		
-- 	}, locations = {

		
-- 	}, targets = {

-- 	}
-- },

-- PoliceArmoury = {
-- 	name = 'Police Armoury',
-- 	groups = shared.police,
-- 	blip = {
-- 		id = 110, colour = 84, scale = 0.8
-- 	}, inventory = {

-- 	}, locations = {

-- 	}, targets = {

-- 	}
-- },

-- Medicine = {
-- 	name = 'Bandage Shop',
-- 	groups = {
-- 		['ambulance'] = 0
-- 	},
-- 	inventory = {
-- 		{ name = 'bandage', price = 10, metadata = {label = "\"Bloody\" Bandage",	description = "A themed bandage, hope it can still stop the bleeding...",	image = "bandage_blood" } },
-- 		{ name = 'bandage', price = 10, metadata = {label = "Blue Cross Bandage",	description = "A themed bandage, hope it can still stop the bleeding...",	image = "bandage_bluecross" } },
-- 		{ name = 'bandage', price = 10, metadata = {label = "Broken Heart Bandage",	description = "A themed bandage, hope it can still stop the bleeding...",	image = "bandage_brokenheart" } },
-- 		{ name = 'bandage', price = 10, metadata = {label = "Calm Down Bandage",	description = "A themed bandage, hope it can still stop the bleeding...",	image = "bandage_calm" } },
-- 		{ name = 'bandage', price = 10, metadata = {label = "Cinnamaroll Bandage",	description = "A themed bandage, hope it can still stop the bleeding...",	image = "bandage_cinnamaroll" } },
-- 		{ name = 'bandage', price = 10, metadata = {label = "You're Enough Bandage",	description = "A themed bandage, hope it can still stop the bleeding...",	image = "bandage_enough" } },
-- 		{ name = 'bandage', price = 10, metadata = {label = "Hypno-Bandage",	description = "A themed bandage, hope it can still stop the bleeding...",	image = "bandage_eye" } },
-- 		{ name = 'bandage', price = 10, metadata = {label = "Pink Cross Bandage",	description = "A themed bandage, hope it can still stop the bleeding...",	image = "bandage_health" } },
-- 		{ name = 'bandage', price = 10, metadata = {label = "Heart Bandage",	description = "A themed bandage, hope it can still stop the bleeding...",	image = "bandage_heart" } },
-- 		{ name = 'bandage', price = 10, metadata = {label = "Hello Kitty Bandage",	description = "A themed bandage, hope it can still stop the bleeding...",	image = "bandage_hellokitty" } },
-- 		{ name = 'bandage', price = 10, metadata = {label = "Kuromi Bandage",	description = "A themed bandage, hope it can still stop the bleeding...",	image = "bandage_kuromi" } },
-- 		{ name = 'bandage', price = 10, metadata = {label = "Mended Heart Bandage",	description = "A themed bandage, hope it can still stop the bleeding...",	image = "bandage_mendedheart" } },
-- 		{ name = 'bandage', price = 10, metadata = {label = "My Melody Bandage",	description = "A themed bandage, hope it can still stop the bleeding...",	image = "bandage_mymelody" } },
-- 		{ name = 'bandage', price = 10, metadata = {label = "Peach Bandage",	description = "A themed bandage, hope it can still stop the bleeding...",	image = "bandage_peach" } },
-- 		{ name = 'bandage', price = 10, metadata = {label = "Peachy Bandage",	description = "A themed bandage, hope it can still stop the bleeding...",	image = "bandage_peachy" } },
-- 		{ name = 'bandage', price = 10, metadata = {label = "Peachy Keen Bandage",	description = "A themed bandage, hope it can still stop the bleeding...",	image = "bandage_peachykeen" } },
-- 		{ name = 'bandage', price = 10, metadata = {label = "Pentagram Bandage",	description = "A themed bandage, hope it can still stop the bleeding...",	image = "bandage_pentagram" } },
-- 		{ name = 'bandage', price = 10, metadata = {label = "Pill Bandage",	description = "A themed bandage, hope it can still stop the bleeding...",	image = "bandage_pill" } },
-- 		{ name = 'bandage', price = 10, metadata = {label = "Pill Spill Bandage",	description = "A themed bandage, hope it can still stop the bleeding...",	image = "bandage_pills" } },
-- 		{ name = 'bandage', price = 10, metadata = {label = "Pink Slime Bandage",	description = "A themed bandage, hope it can still stop the bleeding...",	image = "bandage_pinkslime" } },
-- 		{ name = 'bandage', price = 10, metadata = {label = "Poison Bandage",	description = "A themed bandage, hope it can still stop the bleeding...",	image = "bandage_poison" } },
-- 		{ name = 'bandage', price = 10, metadata = {label = "Pompompurin Bandage",	description = "A themed bandage, hope it can still stop the bleeding...",	image = "bandage_pompompurin" } },
-- 		{ name = 'bandage', price = 10, metadata = {label = "Purple Bandage",	description = "A themed bandage, hope it can still stop the bleeding...",	image = "bandage_purple" } },
-- 		{ name = 'bandage', price = 10, metadata = {label = "Pride Bandage",	description = "A themed bandage, hope it can still stop the bleeding...",	image = "bandage_rainbow" } },
-- 		{ name = 'bandage', price = 10, metadata = {label = "Smiley Bandage",	description = "A themed bandage, hope it can still stop the bleeding...",	image = "bandage_smiley" } },
-- 		{ name = 'bandage', price = 10, metadata = {label = "Upsidedown Cross Bandage",	description = "A themed bandage, hope it can still stop the bleeding...",	image = "bandage_ssorc" } },
-- 		{ name = 'bandage', price = 10, metadata = {label = "Well Done Bandage",	description = "A themed bandage, hope it can still stop the bleeding...",	image = "bandage_welldone" } },
-- 		{ name = 'bandage', price = 10, metadata = {label = "Winged Bandage",	description = "A themed bandage, hope it can still stop the bleeding...",	image = "bandage_wing" } },
-- 	},
-- 	locations = {
-- 		vector3(349.56, -572.23, 28.80),
-- 		vector3(320.88, -593.56, 43.40),
-- 	},
-- 	targets = {
-- 		{
-- 			ped = 'S_M_M_Paramedic_01',
-- 			scenario = 'WORLD_HUMAN_CLIPBOARD',
-- 			loc = vector3(349.56, -572.23, 27.80),
-- 			heading = 247,
-- 		},
-- 		{
-- 			ped = 'S_M_M_Paramedic_01',
-- 			scenario = 'WORLD_HUMAN_CLIPBOARD',
-- 			loc = vector3(320.88, -593.56, 42.40),
-- 			heading = 80,
-- 		},
-- 	},
-- },

--BlackMarketArms = {
--	name = 'Black Market (Arms)',
--	inventory = {
--		{ name = 'WEAPON_DAGGER', price = 5000, metadata = { registered = false	}, currency = 'black_money' },
--		{ name = 'WEAPON_CERAMICPISTOL', price = 50000, metadata = { registered = false }, currency = 'black_money' },
--		{ name = 'at_suppressor_light', price = 50000, currency = 'black_money' },
--		{ name = 'ammo-rifle', price = 1000, currency = 'black_money' },
--		{ name = 'ammo-rifle2', price = 1000, currency = 'black_money' }
	--}, locations = {
		--vec3(309.09, -913.75, 56.46)
	--}, targets = {
		--{
			--ped = 'u_m_y_gunvend_01',
			--scenario = 'WORLD_HUMAN_HANG_OUT_STREET_CLUBHOUSE',
			--loc = vec3(309.09, -913.75, 56.46),
			--heading = 125.022,
		--},
--	}
--},


--[[ VanillaUnicorn = {
	name = 'Vanilla Unicorn',
	blip = {
		id = 121, colour = 8, scale = 0.8
	}, inventory = {
		{ name = 'rancho_beer', price = 5 },
		{ name = 'dusche_beer', price = 7 },
		{ name = 'stronzo_beer', price = 6 },
		{ name = 'patriot_beer', price = 5 },
		
	}, locations = {
		vector3(129.74, -1284.59, 27.27),
		
	}, targets = {
		{
			ped = 's_f_y_bartender_01',
			scenario = 'WORLD_HUMAN_HANG_OUT_STREET_CLUBHOUSE',
			loc = vector3(129.74, -1284.59, 29.27),
			heading = 125.022,
		},
	}
}, ]]
	-- GymSupplements = {
	-- 	name = 'Gym Supplements',
	-- 	inventory = {
	-- 		{ name = 'creatine',     price = 325 },
	-- 		{ name = 'preworkout',   price = 350 },
	-- 		{ name = 'seamoss',      price = 360 },
	-- 	},
	-- 	locations = {
	-- 		vector3(263.43, -268.81, 53.96),
	-- 		vector3(1750.48, 2554.14, 43.59),
	-- 	},
	-- 	targets = {
	-- 		{
	-- 			ped = 'a_f_y_tennis_01',
	-- 			scenario = 'WORLD_HUMAN_CLIPBOARD',
	-- 			loc = vector3(263.43, -268.81, 52.96),
	-- 			heading = 75.022,
	-- 		},
	-- 		{
	-- 			ped = 'a_f_y_tennis_01',
	-- 			scenario = 'WORLD_HUMAN_CLIPBOARD',
	-- 			loc = vector3(1750.48, 2554.14, 42.59),
	-- 			heading = 162.022,
	-- 		},
	-- 	}
	-- },
--------------------------------------------------------------------------------------
--------------------------------------------------------------------------------------
-- vending
-- VendingMachineDrinks = {
-- 	name = 'Vending Machine',
-- 	inventory = {
-- 		{ name = 'water', price = 20 },
-- 		{ name = 'ecola', price = 23 },
-- 		{ name = 'sprunk', price = 23 },
-- 		{ name = 'milk', price = 24 },
-- 	},
-- 	model = {
-- 		`prop_vend_soda_02`, `prop_vend_water_01`, `prop_vend_soda_01`, 
-- 		`ch_chint10_vending_smallroom_01`, `sf_prop_sf_vend_drink_01a`
-- 	}
-- },

-- VendingMachineWater = {
-- 	name = 'Vending Machine',
-- 	inventory = {
-- 		{ name = 'water', price = 20 },
-- 	},
-- 	model = {
-- 		`prop_watercooler`,
-- 	}
-- },

-- VendingMachineSnacks = {
-- 	name = 'Vending Machine',
-- 	inventory = {
-- 		{ name = 'phatc_rib', price = 13 },
-- 		{ name = 'phatc_bch', price = 13 },
-- 		{ name = 'ps_qs', price = 22 },
-- 		{ name = 'apple', price = 12 },
-- 		{ name = 'banana', price = 12 },
-- 	},
-- 	model = {
-- 		`prop_vend_snak_01`, `prop_vend_snak_01_tu`
-- 	}
-- },

-- VendingMachineCoffee = {
-- 	name = 'Coffee Machine',
-- 	inventory = {
-- 		{ name = 'coffee_black', price = 14 },
-- 		{ name = 'coffee_mocha', price = 13 },
-- 		{ name = 'coffee_cpcno', price = 13 },
-- 		{ name = 'coffee_amrcno', price = 13 },
-- 	},
-- 	model = {
-- 		`prop_vend_coffe_01`
-- 	}
-- },

--racing stuff 

-- Raceshop = {
-- 	name = 'race shop',
-- 	inventory = {
-- 		{ name = 'stage2_component', price = 50, currency = 'race_coin' },
-- 		--{ name = 'race_money', price = 2, currency = 'race_coin' },
-- 	},
-- 	locations = {
-- 		vector3(153.67, -3215.59, 5.92),
-- 	},
-- 	targets = {
-- 		{
-- 			ped = 'mp_m_waremech_01',
-- 			scenario = 'WORLD_HUMAN_CLIPBOARD',
-- 			loc = vector3(153.67, -3215.59, 4.92),
-- 			heading = 60,
-- 		},
-- 	}
-- },

-- Halloweenshop = {
-- 	name = 'Halloween shop',
-- 	inventory = {
-- 		{ name = 'billysawdoll', price = 10, currency = 'halloween_token' },
-- 		{ name = 'pennywisedoll', price = 10, currency = 'halloween_token' },
-- 		{ name = 'chuckydoll', price = 10, currency = 'halloween_token' },
-- 		{ name = 'brightburndoll', price = 10, currency = 'halloween_token' },
-- 		{ name = 'halloween_bag', price = 30, currency = 'halloween_token' },
-- 		--{ name = 'race_money', price = 2, currency = 'race_coin' },
-- 	},
-- 	locations = {
-- 		vector3(-90.67, 7291.18, 15.53),
-- 	},
-- 	targets = {
-- 		{
-- 			ped = 'u_m_y_zombie_01',
-- 			loc = vector3(-90.67, 7291.18, 14.53),
-- 			heading = 204,
-- 		},
-- 	}
-- },
}
