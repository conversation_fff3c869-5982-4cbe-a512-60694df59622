import React, { Fragment, useMemo } from 'react';
import { Locale } from '../../store/locale';
import Divider from '../utils/Divider';
import { setPreferences } from '../../store/preferences';
import { useAppDispatch, useAppSelector } from '../../store';

const SettingsPopup: React.ForwardRefRenderFunction<
  HTMLDivElement,
  { style: React.CSSProperties }
> = ({ style }, ref) => {
  const preferences = useAppSelector((state) => state.preferences)
  const dispatch = useAppDispatch();

  return (
    <>
      {
        <div className="help-wrapper popup" id="settingsPopup" style={{ ...style}}>
          <div className="help-header-wrapper">
            <p>User Settings</p>
          </div>
          <Divider />
          <div className='help-grid'>
            <div className='help-grid-item'><label htmlFor="primaryColor">Select your favorite color:</label></div>
            <div className='help-grid-item r'><input type='color' id='primaryColor' value={preferences.PrimaryColor} onChange={(e) => {dispatch(setPreferences({PrimaryColor: e.currentTarget.value+''}))}} /></div>
            <div className='help-grid-item'><label htmlFor="streamerMode">Hides sensitive data:</label></div>
            <div className='help-grid-item r'><div className='help-button' id='streamerMode' onClick={() => {dispatch(setPreferences({StreamerMode: !(preferences.StreamerMode)}))}}>{preferences.StreamerMode? "Enabled" : "Disabled"}</div></div>
            <div className='help-grid-item'><label htmlFor="BodyColor">Monochrome Body:</label></div>
            <div className='help-grid-item r'><div className='help-button' id='BodyColor' onClick={() => {dispatch(setPreferences({BodyColor: !(preferences.BodyColor)}))}}>{preferences.BodyColor? "Enabled" : "Disabled"}</div></div>
          </div>
        </div>
      }
    </>
  );
};

export default React.forwardRef(SettingsPopup);
