const Durability = ({percent, styles, label}: {percent: number, styles?: React.CSSProperties, label?: string}) => (
    <div style={styles}>
        <div style={{display:'block', marginLeft: '1%', marginRight: '1%' }}>
            {label}
        </div>
        <div style={{display: 'block', marginTop: '0', height: '100%', border: '1px solid rgba(255,255,255,0.4)', borderRadius: '0.2vw' }}>
            <div
                style={{
                    height: '100%',
                    width: `${percent}%`,
                    borderRadius: '0.2v%',
                    backgroundColor: 'rgba(202,0,40,0.7)',
                    transition: `background ${0.3}s ease, width ${0.3}s ease`,
                }}
            >
            </div>
        </div>
    </div>
);

export default Durability;