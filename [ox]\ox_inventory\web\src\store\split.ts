import { createSlice, PayloadAction } from '@reduxjs/toolkit';
import { Inventory, SlotWithItem } from '../typings';

interface SplitState {
  open: boolean;
  count: number;
  maxCount: number;
}

const initialState: SplitState = {
  open: false,
  count: 0,
  maxCount: 0,
};

export const splitSlice = createSlice({
  name: 'split',
  initialState,
  reducers: {
    openSplit(state, action: PayloadAction<{ count: number, }>) {
      state.open = true;
      state.count = 1;
      state.maxCount = action.payload.count;
    },
    closeSplit(state) {
      state.open = false;
    },
    setSplit(state, action: PayloadAction<{ count: number}>) {
      state.count = action.payload.count;
    },
  },
});

export const { openSplit, closeSplit, setSplit } = splitSlice.actions;

export default splitSlice.reducer;
