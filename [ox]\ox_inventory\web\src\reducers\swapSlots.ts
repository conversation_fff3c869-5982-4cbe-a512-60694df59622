import { CaseReducer, PayloadAction } from '@reduxjs/toolkit';
import { getTargetInventory, itemDurability } from '../helpers';
import { Inventory, SlotWithItem, State } from '../typings';
import { store } from '../store';

export const swapSlotsReducer: CaseReducer<
  State,
  PayloadAction<{
    fromSlot: SlotWithItem;
    fromType: Inventory['type'];
    fromId: Inventory['id'];
    toSlot: SlotWithItem;
    toType: Inventory['type'];
    toId: Inventory['id'];
  }>
> = (state, action) => {
  const { fromSlot, fromType, fromId, toSlot, toType, toId } = action.payload;
  const { sourceInventory, targetInventory } = getTargetInventory(state, fromId, toId);
  const curTime = Math.floor(Date.now() / 1000);
  // console.log('swapslots', action, state);

  [sourceInventory.items[fromSlot.slot - 1], targetInventory.items[toSlot.slot - 1]] = [
    {
      ...targetInventory.items[toSlot.slot - 1],
      slot: fromSlot.slot,
      durability: itemDurability(toSlot.metadata, curTime),
    },
    {
      ...sourceInventory.items[fromSlot.slot - 1],
      slot: toSlot.slot,
      durability: itemDurability(fromSlot.metadata, curTime),
    },
  ];
};
