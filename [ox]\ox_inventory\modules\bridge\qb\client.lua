local QBCore = exports['qb-core']:GetCoreObject()
local Inventory = require 'modules.inventory.client'
local Weapon = require 'modules.weapon.client'

RegisterNetEvent('QBCore:Client:OnPlayerUnload', client.onLogout)

RegisterNetEvent('QBCore:Player:SetPlayerData', function(data)
    if source == '' or not PlayerData.loaded then return end

    if (data.metadata.isdead or data.metadata.inlaststand) ~= PlayerData.dead then
        PlayerData.dead = data.metadata.isdead or data.metadata.inlaststand
        OnPlayerData('dead', PlayerData.dead)
    end

    local groups = PlayerData.groups

    if not groups[data.job.name] or not groups[data.gang.name] or groups[data.job.name] ~= data.job.grade.level or groups[data.gang.name] ~= data.gang.grade.level then
        PlayerData.groups = {
            [data.job.name] = data.job.grade.level,
            [data.gang.name] = data.gang.grade.level,
        }

        OnPlayerData('groups', PlayerData.groups)
    end

    if data.metadata.ishandcuffed then
        PlayerData.cuffed = true
        LocalPlayer.state:set('invBusy', true, false)
        Weapon.Disarm()
    elseif PlayerData.cuffed then
        PlayerData.cuffed = false
        LocalPlayer.state:set('invBusy', false, false)
    end
end)
local function addUnderwaterTime()
    local playerid = PlayerId()
    SetPlayerUnderwaterTimeRemaining(playerid, 100)
end

local function addStamina(amount)
    local playerid = PlayerId()
    SetPlayerStamina(playerid, lib.math.clamp(GetPlayerStamina(playerid)+amount, 0, GetPlayerMaxStamina(playerid)))
end

local function superJump(time)
    local playerid = PlayerId()
    local ped = GetPlayerPed(playerid)
    local location = vector3(-1386.79, -604.10, 30.21)
    local maxDistance = 100.0  

    Citizen.CreateThread(function()
        local endTime = GetGameTimer() + (time * 1000)  

        while GetGameTimer() < endTime do
            local playerPos = GetEntityCoords(ped)

            local distance = #(playerPos - location)

            if distance <= maxDistance then
                SetSuperJumpThisFrame(playerid)
            end

            Citizen.Wait(0)  
        end
    end)
end

client.societyCheck = function(skill)
    -- print('societyCheck', skill)
    local hasSkill = lib.callback.await('srp-society:server:playerHasSkill', nil, skill)
    -- print('hasSkill result:', hasSkill)
    if not hasSkill then
        return false
    end
    return true
end

local function addArmor(amount, max)
    if not max then max = 100 end
    local playerid = PlayerId()
    local ped = GetPlayerPed(playerid)
    local armor = GetPedArmour(ped)
    if max > 100 then max = 100 end
	if armor < max then
        SetPlayerMaxArmour(playerid, 100)
        SetPedArmour(ped, lib.math.clamp(armor + amount, 0, max))
	end
end

local function addArmorOverTime(amount, max, duration)
    if not max then max = 100 end
    if max > 100 then max = 100 end
    
    local playerid = PlayerId()
    local ped = GetPlayerPed(playerid)
    local armor = GetPedArmour(ped)
    
    local totalAmount = amount
    local ticks = 20 
    local interval = duration / ticks  
    local increment = totalAmount / ticks  

    Citizen.CreateThread(function()
        for i = 1, ticks do
            armor = GetPedArmour(ped) 
            if armor < max then
                SetPlayerMaxArmour(playerid, 100) 
                SetPedArmour(ped, lib.math.clamp(armor + increment, 0, max))
            end
            Citizen.Wait(interval * 1000) 
        end
    end)
end


local function heal(amount, max)
    if not max then 
        max = 200
    else
        max = max + 100
    end
    local playerid = PlayerId()
    local ped = GetPlayerPed(playerid)
    local maxHealth = GetEntityMaxHealth(ped)
    if max < maxHealth then
        maxHealth = max
    end
    local health = GetEntityHealth(ped)
    if health < maxHealth then
        SetEntityHealth(ped, lib.math.clamp(health + amount, 0, maxHealth))
    end
    local overflow = health + (2 * amount) - maxHealth
    if overflow > 0 then
        return overflow / 2
    end
    return 0
end

local function healOverTime(amount, max, duration)
    if not max then 
        max = 200
    else
        max = max + 100
    end
    
    local playerid = PlayerId()
    local ped = GetPlayerPed(playerid)
    local maxHealth = GetEntityMaxHealth(ped)
    if max < maxHealth then
        maxHealth = max
    end
    
    local health = GetEntityHealth(ped)
    local totalAmount = amount
    local ticks = 20 
    local interval = duration / ticks  
    local increment = totalAmount / ticks 

    Citizen.CreateThread(function()
        for i = 1, ticks do
            health = GetEntityHealth(ped)
            if health < maxHealth then
                SetEntityHealth(ped, lib.math.clamp(health + increment, 0, maxHealth))
            end
            Citizen.Wait(interval * 1000)
            end
        end)

    local overflow = health + (2 * amount) - maxHealth
    if overflow > 0 then
        return overflow / 2
    end
    return 0
end


local function healthArmorOT(amount, maxHealth, maxArmor, duration)
    if not maxHealth then 
        maxHealth = 200
    else
        maxHealth = maxHealth + 100
    end

    if not maxArmor then
        maxArmor = 100
    end
    
    local playerid = PlayerId()
    local ped = GetPlayerPed(playerid)
    local entityMaxHealth = GetEntityMaxHealth(ped)
    if maxHealth < entityMaxHealth then
        entityMaxHealth = maxHealth
    end
    
    local health = GetEntityHealth(ped)
    local armor = GetPedArmour(ped)
    local totalAmount = amount
    local ticks = 20 
    local interval = duration / ticks  
    local increment = totalAmount / ticks 

    Citizen.CreateThread(function()
        for i = 1, ticks do
            health = GetEntityHealth(ped)
            armor = GetPedArmour(ped)
            
            if health < entityMaxHealth then
                SetEntityHealth(ped, lib.math.clamp(health + increment, 0, entityMaxHealth))
            elseif armor < maxArmor then
                SetPedArmour(ped, lib.math.clamp(armor + increment, 0, maxArmor))
            end
            
            Citizen.Wait(interval * 1000)
        end
    end)

    local overflow = (health + (2 * amount)) - entityMaxHealth
    if overflow > 0 then
        return overflow / 2
    end
    return 0
end

local function speed(speed, time)
    CreateThread(function ()
        SetRunSprintMultiplierForPlayer(PlayerId(), lib.math.clamp(speed, 1.0, 1.49))
        Wait(time * 1000)
        SetRunSprintMultiplierForPlayer(PlayerId(), 1.0)
    end)
end

local hotCooldown = false
local function jump(speed, time)
    CreateThread(function ()
        -- (PlayerId(), lib.math.clamp(speed, 1.0, 1.49))
        Wait(time * 1000)
        SetRunSprintMultiplierForPlayer(PlayerId(), 1.0)
    end)
end

---@diagnostic disable-next-line: duplicate-set-field
function client.setPlayerStatus(values)
    for name, value in pairs(values) do
        -- print('qb compatibility', tostring(value))
        -- print(name, json.encode(value))
        -- compatibility for ESX style values
        if type(value) == "number" and (value > 100 or value < -100) then
            -- print('esx range number detected')
            value = value * 0.0001
        end

        if name == "hunger" then
            local hungerValue = type(value) == "table" and math.random(value[1], value[2]) or value
            TriggerServerEvent('consumables:server:addHunger', hungerValue)
        elseif name == "thirst" then
            local thirstValue = type(value) == "table" and math.random(value[1], value[2]) or value
            TriggerServerEvent('consumables:server:addThirst', thirstValue)
        elseif name == "stress" then
            if type(value) == "table" then
                if not value[1] then return end
                if not value[2] then
                    if value[1] > 0 then
                        TriggerServerEvent('hud:server:GainStress', value[1])
                    else
                        value[1] = math.abs(value[1])
                        TriggerServerEvent('hud:server:RelieveStress', value[1])
                    end
                else
                    local val = math.random(value[1], value[2])
                    if val > 0 then
                        TriggerServerEvent('hud:server:GainStress', val)
                    else
                        val = math.abs(val)
                        TriggerServerEvent('hud:server:RelieveStress', val)
                    end
                end
            else
                if value > 0 then
                    TriggerServerEvent('hud:server:GainStress', value)
                else
                    value = math.abs(value)
                    TriggerServerEvent('hud:server:RelieveStress', value)
                end
            end
        elseif name == 'drunk' then
            if value > 0 then
                exports['rcore_drunk']:AddPlayerDrunkPercentage(value)
            else
                exports['rcore_drunk']:RemovePlayerDrunkPercentage(math.abs(value))
            end
        elseif name == 'air' then
            CreateThread(function()
                local i = 0
                if value < 1 then value = 1 end
                while i < value do
                    addUnderwaterTime()
                    Wait(1000)
                    i = i + 1
                end
            end)
        elseif name == 'stamina' then
            if type(value) == "table" then
                if not value[1] then return end
                if not value[2] then
                    addStamina(value[1])
                else
                    CreateThread(function()
                        local i = 0
                        local step = value[1] / value[2]
                        while i < value[2] do
                            addStamina(step)
                            Wait(1000)
                            i = i + 1
                        end
                    end)
                end
            else
                addStamina(value)
            end
        elseif name == 'speed' then
            if type(value) == "table" then
                if not value[1] then return end
                if not value[2] then return end
                speed(value[1], value[2])
            end
        elseif name == 'heal' then
            if type(value) == "table" then
                if not value[1] then return end
                if not value[2] then
                    heal(value[1])
                else
                    heal(value[1], value[2])
                end
            else
                heal(value)
            end
        elseif name == 'hot' then
            if type(value) ~= "table" or not value[1] or not value[2] then
                print("no hot today.")
                return
            end
            if hotCooldown then
                lib.notify({
                    description = 'You must wait before using this item again',
                    type = 'error'
                })
                return
            end
        
            hotCooldown = true
            local durationMs  = value[2] * 1000               -- total effect time
            local totalAmount = value[1]                      -- total health to restore
        
            local maxHealth = GetEntityMaxHealth(cache.ped)
            if maxHealth < 200 then maxHealth = 200 end
        
            local ticks      = 20                             -- keep your original 20 ticks
            local intervalMs = durationMs / ticks
            local increment  = totalAmount / ticks            -- may be fractional
            local remainder  = 0                              -- carries fractional part forward
        
            CreateThread(function()
                for i = 1, ticks do
                    local health = GetEntityHealth(cache.ped)
                    if health < maxHealth then
                        remainder = remainder + increment
                        local add = math.floor(remainder)     -- only whole points are applied
                        if add > 0 then
                            remainder = remainder - add
                            SetEntityHealth(cache.ped, lib.math.clamp(health + add, 0, maxHealth))
                        end
                    end
                    Wait(intervalMs)
                end
        
                Wait(1000)          -- small buffer before cooldown ends
                hotCooldown = false
            end)
        elseif name == 'armor' then
            if type(value) == "table" then
                if not value[1] then return end
                if not value[2] then
                    addArmor(value[1])
                else
                    addArmor(value[1], value[2])
                end
            else
                addArmor(value)
            end
        elseif name == 'stembleed' then
            if type(value) == "table" then
                if not value[1] then return end
                exports['wasabi_ambulance']:stemBleed(value[1])
            else
                exports['wasabi_ambulance']:stemBleed(value)
            end
        elseif name == 'painkiller' then
            if type(value) == 'table' then
                exports['wasabi_ambulance']:addPainKillers(value[1], value[2])
            else
                exports['wasabi_ambulance']:addPainKillers(value)
            end
        elseif name == 'barSpeed' then
            if type(value) == 'table' then
                exports['progressbar']:setSpeedUp(value[1], value[2])
            else
                exports['progressbar']:setSpeedUp(value)
            end
        elseif name == 'htoa' then
            local overflow = 0
            local time = 30 
            if type(value) == "table" then
                -- print("here1")
                if not value[1] then return end
                if not value[2] then
                    overflow = healthArmorOT(value[1], 200, 20, time)  
                else
                    -- print("here2")
                    overflow = healthArmorOT(value[1], 200, 20, time)
                end
            else
                overflow = healthArmorOT(value,200, 20,time)
            end
        elseif name == 'jump' then
            if type(value) == "table" then
                if not value[1] then return end
                superJump(value[1])
            else
                superJump(value)
            end
        elseif name == 'skill' then
            if type(value) ~= 'table' then
                return
            end
            if value[2] > 0 then
                TriggerServerEvent('OT_skills:addXP', value[1], value[2])
            else
                TriggerServerEvent('OT_skills:removeXP', value[1], math.abs(value[2]))
            end
        elseif name == 'luck' then
            if type(value) == "table" then
                if not value[1] then return end
                exports['ps-buffs']:AddBuff("luck", value[1])
            else
              exports['ps-buffs']:AddBuff("luck", value)
            end
        elseif name == 'vision_green' then
            if type(value) == "table" then
                if not value[1] then return end
                local duration = value[1] * 1000

                -- Start the alien green screen effect
                SetNightvision(true)
                QBCore.Functions.Notify("Night vision enabled!", "success")
        
                Citizen.SetTimeout(duration, function()
                    SetNightvision(false)
                    QBCore.Functions.Notify("Your vision is back to normal.", "success")
                end)
            end    
        end
    end
end
exports('setPlayerStatus', client.setPlayerStatus)


AddStateBagChangeHandler('inv_busy', ('player:%s'):format(cache.serverId), function(_, _, value)
    LocalPlayer.state:set('invBusy', value, false)
end)

local function export(exportName, func)
    AddEventHandler(('__cfx_export_%s_%s'):format(string.strsplit('.', exportName, 2)), function(setCB)
        setCB(func or function()
            error(("export '%s' is not supported when using ox_inventory"):format(exportName))
        end)
    end)
end

export('qb-inventory.HasItem', function(items, amount)
    amount = amount or 1

    if type(items) == 'table' then
        for _, v in pairs(items) do
            if Inventory.GetItemCount(v) < amount then
                return false
            end
        end

        return true
    else
        return Inventory.GetItemCount(items) >= amount
    end
end)
