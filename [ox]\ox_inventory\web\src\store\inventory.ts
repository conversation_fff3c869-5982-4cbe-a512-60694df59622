import { createSlice, current, isFulfilled, isPending, isRejected, PayloadAction } from '@reduxjs/toolkit';
import type { RootState } from '.';
import {
  moveSlotsReducer,
  refreshSlotsReducer,
  setupInventoryReducer,
  stackSlotsReducer,
  swapSlotsReducer,
} from '../reducers';
import { State, Inventory, InventoryType } from '../typings';

const initialState: State = {
  playerInventories: [
    {     
      id: '',
      type: '',
      slots: 0,
      maxWeight: 0,
      items: [],
    }
  ],
  targetInventories: [
    {     
      id: '',
      type: '',
      slots: 0,
      maxWeight: 0,
      items: [],
    }
  ],
  additionalMetadata: new Array(),
  itemAmount: 0,
  shiftPressed: false,
  isBusy: false,
};

export const inventorySlice = createSlice({
  name: 'inventory',
  initialState,
  reducers: {
    stackSlots: stackSlotsReducer,
    swapSlots: swapSlotsReducer,
    setupInventory: setupInventoryReducer,
    moveSlots: moveSlotsReducer,
    refreshSlots: refreshSlotsReducer,
    setAdditionalMetadata: (state, action: PayloadAction<Array<{ metadata: string; value: string }>>) => {
      const metadata = [];

      for (let i = 0; i < action.payload.length; i++) {
        const entry = action.payload[i];
        if (!state.additionalMetadata.find((el) => el.value === entry.value)) metadata.push(entry);
      }

      state.additionalMetadata = [...state.additionalMetadata, ...metadata];
    },
    setItemAmount: (state, action: PayloadAction<number>) => {
      state.itemAmount = action.payload;
    },
    setShiftPressed: (state, action: PayloadAction<boolean>) => {
      state.shiftPressed = action.payload;
    },
    setContainerWeight: (state, action: PayloadAction<number>) => {
      // const container = state.leftInventory.items.find((item) => item.metadata?.container === state.rightInventory.id);

      // if (!container) return;

      // container.weight = action.payload;
    }
  },
  extraReducers: (builder) => {
    builder.addMatcher(isPending, (state) => {
      state.isBusy = true;

      state.history = {
        playerInventories: current(state.playerInventories),
        targetInventories: current(state.targetInventories)
        // leftInventory: current(state.leftInventory),
        // rightInventory: current(state.rightInventory),
        // bodyInventory: current(state.bodyInventory),
        // playerInventory: current(state.playerInventory),
        // bagInventory: current(state.bagInventory),
      };
    });
    builder.addMatcher(isFulfilled, (state) => {
      state.isBusy = false;
    });
    builder.addMatcher(isRejected, (state) => {
      if (state.history && state.history.playerInventories && state.history.targetInventories) {
        state.playerInventories = state.history.targetInventories;
        state.playerInventories = state.history.targetInventories;
      }
      state.isBusy = false;
    });
  },
});

export const {
  setAdditionalMetadata,
  setItemAmount,
  setShiftPressed,
  setupInventory,
  swapSlots,
  moveSlots,
  stackSlots,
  refreshSlots,
  setContainerWeight,
} = inventorySlice.actions;
// export const selectLeftInventory = (state: RootState) => state.inventory.leftInventory;
// export const selectBodyInventory = (state: RootState) => state.inventory.bodyInventory;
export const selectPlayerInventory = (state: RootState) => state.inventory.playerInventories.find((element) => element.type == InventoryType.PLAYER);
// export const selectBagInventory = (state: RootState) => state.inventory.bagInventory;
// export const selectRightInventory = (state: RootState) => state.inventory.rightInventory;
export const selectItemAmount = (state: RootState) => state.inventory.itemAmount;
export const selectIsBusy = (state: RootState) => state.inventory.isBusy;

export const selectInventoryByType = (input: string, state: RootState):Inventory => {
  return state.inventory.playerInventories.find((element) => element.type == input) 
    ? state.inventory.playerInventories.find((element) => element.type == input)
    : state.inventory.targetInventories.find((element) => element.type == input)
    ? state.inventory.targetInventories.find((element) => element.type == input)
    : null
}

export const selectInventoryByID = (input: string, state: RootState):Inventory => {
  return state.inventory.playerInventories.find((element) => element.id == input)
    ? state.inventory.playerInventories.find((element) => element.id == input)
    : state.inventory.targetInventories.find((element) => element.id == input)
    ? state.inventory.targetInventories.find((element) => element.id == input)
    : null
}

export const selectPlayerInventories = (state: RootState) => state.inventory.playerInventories;
export const selectTargetInventories = (state: RootState) => state.inventory.targetInventories;

export const findInventoryByType = (input: string, state: State, ) => {
  return state.playerInventories.find((element) => element.type == input) 
    ? state.playerInventories.find((element) => element.type == input)
    : state.targetInventories.find((element) => element.type == input)
    ? state.targetInventories.find((element) => element.type == input)
    : null
}
export const findInventoryByID = (input: string, state: State, ) => {
  return state.playerInventories.find((element) => element.id == input)
    ? state.playerInventories.find((element) => element.id == input)
    : state.targetInventories.find((element) => element.id == input)
    ? state.targetInventories.find((element) => element.id == input)
    : null
}

export const findPlayerInventory = (state: State) => state.playerInventories.find((element) => element.type == InventoryType.PLAYER);

export const findPlayerInventories = (state: State) => state.playerInventories;
export const findTargetInventories = (state: State) => state.targetInventories;

export default inventorySlice.reducer;
