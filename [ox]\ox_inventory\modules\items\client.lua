if not lib then return end

local Items = require 'modules.items.shared' --[[@as table<string, OxClientItem>]]

local function sendDisplayMetadata(data)
    SendNUIMessage({
		action = 'displayMetadata',
		data = data
	})
end

--- use array of single key value pairs to dictate order
---@param metadata string | table<string, string> | table<string, string>[]
---@param value? string
local function displayMetadata(metadata, value)
	local data = {}

	if type(metadata) == 'string' then
        if not value then return end

        data = { { metadata = metadata, value = value } }
	elseif table.type(metadata) == 'array' then
		for i = 1, #metadata do
			for k, v in pairs(metadata[i]) do
				data[i] = {
					metadata = k,
					value = v,
				}
			end
		end
	else
		for k, v in pairs(metadata) do
			data[#data + 1] = {
				metadata = k,
				value = v,
			}
		end
	end

    if client.uiLoaded then
        return sendDisplayMetadata(data)
    end

    CreateThread(function()
        repeat Wait(100) until client.uiLoaded

        sendDisplayMetadata(data)
    end)
end

exports('displayMetadata', displayMetadata)

---@param _ table?
---@param name string?
---@return table?
local function getItem(_, name)
    if not name then return Items end

	if type(name) ~= 'string' then return end

    name = name:lower()

    if name:sub(0, 7) == 'weapon_' then
        name = name:upper()
    end

    return Items[name]
end

setmetatable(Items --[[@as table]], {
	__call = getItem
})

---@cast Items +fun(itemName: string): OxClientItem
---@cast Items +fun(): table<string, OxClientItem>

local function Item(name, cb)
	local item = Items[name]
	if item then
		if not item.client?.export and not item.client?.event then
			item.effect = cb
		end
	end
end


function ApplyCandyCornEffect(time)
    -- Start the animation effect
    AnimpostfxPlay("DrugsDrivingIn", 10000, 0)
    Citizen.Wait(2000)
    
    -- Timer for 12 seconds of speed boost and stamina reset
    local timer = 0
    while timer < time do
        SetRunSprintMultiplierForPlayer(PlayerId(), 1.10)
        ResetPlayerStamina(PlayerId())
        Citizen.Wait(1000)  -- Wait for 1 second
        timer = timer + 1
    end
    
    -- Stop the animation effect and reset speed
    AnimpostfxStop("DrugsDrivingIn")
    SetRunSprintMultiplierForPlayer(PlayerId(), 1.0)
end


local Mario = {
	cols = {
	  [16] =  {r=55,  g=55,   b=55},
	  [18] =  {r=55,  g=55,   b=135},
	  [24] =  {r=55,  g=95,   b=135},
	  [52] =  {r=95,  g=55,   b=55},
	  [67] =  {r=95,  g=135,  b=175},
	  [88] =  {r=135, g=55,   b=55},
	  [95] =  {r=135, g=95,   b=95},
	  [124] = {r=175, g=55,   b=55},
	  [133] = {r=175, g=95,   b=175},
	  [173] = {r=215, g=135,  b=95},
	  [203] = {r=255, g=95,   b=95},
	  [210] = {r=255, g=135,  b=135},
	  [216] = {r=255, g=175,  b=135},
	  [222] = {r=255, g=215,  b=135},
	  [231] = {r=255, g=255,  b=255},
	},
	bits = {
	  {
		133,133,133,133,88,88,88,88,88,88,133,133,133,133,133,133,
		133,133,133,88,124,222,222,124,124,124,88,133,133,133,133,133,
		133,133,133,88,124,231,231,203,203,203,124,88,133,133,133,133,
		133,133,88,88,88,88,88,88,88,203,203,124,88,88,133,133,
		133,88,124,203,203,203,203,124,124,88,203,203,124,124,88,133,
		133,88,88,88,88,88,88,88,88,88,124,203,203,124,88,133,
		133,133,133,95,231,231,210,231,231,210,88,88,88,88,88,133,
		133,133,133,95,231,67,216,67,231,210,210,52,52,95,133,133,
		133,133,133,95,231,16,216,16,231,216,210,52,52,216,95,133,
		133,133,95,216,216,216,216,216,216,216,52,52,52,216,95,133,
		133,133,16,210,216,216,210,210,16,216,216,52,210,95,133,133,
		133,16,16,16,210,210,16,16,16,16,216,210,210,52,52,133,
		133,133,133,16,16,16,16,16,216,216,210,210,52,52,133,133,
		133,133,133,133,95,210,210,210,210,210,210,95,133,133,133,133,
		133,133,95,95,24,18,88,88,88,18,18,88,88,88,133,133,
		133,95,231,24,18,124,124,124,18,24,203,203,203,124,88,133,
		95,231,222,18,124,203,203,18,24,124,95,95,95,203,124,88,
		95,222,222,18,124,124,124,18,24,95,231,231,231,95,124,88,
		133,95,18,24,18,18,18,24,95,231,231,231,231,222,95,88,
		133,133,18,222,67,67,222,222,95,231,231,231,222,222,95,133,
		133,52,52,222,67,67,222,222,67,95,222,222,222,95,133,133,
		52,173,173,52,24,67,67,67,67,24,95,95,95,52,133,133,
		52,95,95,173,52,67,24,24,67,67,24,24,18,95,52,133,
		52,52,95,95,52,24,24,18,24,67,67,67,18,95,52,52,
		52,52,95,95,52,24,18,18,18,24,24,67,18,95,95,52,
		133,52,52,95,52,18,133,133,133,18,18,24,18,173,95,52,
		133,52,52,52,133,133,133,133,133,133,133,18,18,173,95,52,
		133,133,133,133,133,133,133,133,133,133,133,133,133,52,52,133
	  },
	  {
		133,133,133,133,88,88,88,88,88,88,133,133,133,133,133,133,
		133,133,133,88,124,222,222,124,124,124,88,133,133,133,133,133,
		133,133,133,88,124,231,231,203,203,203,124,88,133,133,133,133,
		133,133,88,88,88,88,88,88,88,203,203,124,88,88,133,133,
		133,88,124,203,203,203,203,124,124,88,203,203,124,124,88,133,
		133,88,88,88,88,88,88,88,88,88,124,203,203,124,88,133,
		133,133,133,95,231,231,210,231,231,210,88,88,88,88,88,133,
		133,133,133,95,231,67,216,67,231,210,210,52,52,95,133,133,
		133,133,133,95,231,16,216,16,231,216,210,52,52,216,95,133,
		133,133,95,216,216,216,216,216,216,216,52,52,52,216,95,133,
		133,133,16,210,216,216,210,210,16,216,216,52,210,95,133,133,
		133,16,16,16,210,210,16,16,16,16,216,210,210,52,52,133,
		133,133,133,16,16,16,16,16,216,216,210,210,52,52,133,133,
		133,133,133,133,95,210,210,210,210,210,210,95,133,133,133,133,
		133,133,133,133,18,88,88,88,18,18,88,88,88,133,133,133,
		133,133,95,18,124,124,124,18,95,95,95,203,203,88,133,133,
		133,95,231,18,124,203,203,95,231,231,231,95,203,203,88,133,
		133,95,222,18,124,124,95,231,231,231,231,222,95,124,88,133,
		133,95,18,24,18,18,95,231,231,231,222,222,95,124,88,133,
		133,133,18,222,67,67,222,95,222,222,222,95,88,88,133,133,
		133,133,18,222,67,67,222,222,95,95,95,24,24,18,133,133,
		133,18,24,24,67,67,24,24,67,67,24,24,18,133,133,133,
		133,52,52,24,24,24,24,18,24,67,67,24,18,52,133,133,
		52,173,173,52,24,24,24,18,24,24,24,24,52,95,52,133,
		52,95,95,173,52,24,18,133,18,24,24,24,52,95,52,133,
		133,52,95,95,95,52,133,133,133,52,52,52,173,95,52,133,
		133,133,52,95,95,52,133,133,52,173,173,95,95,52,133,133,
		133,133,133,52,52,52,133,133,52,52,52,52,52,133,133,133
	  },
	  {
		133,133,133,133,88,88,88,88,88,88,133,133,133,133,133,133,
		133,133,133,88,124,222,222,124,124,124,88,133,133,133,133,133,
		133,133,133,88,124,231,231,203,203,203,124,88,133,133,133,133,
		133,133,88,88,88,88,88,88,88,124,203,124,88,88,133,133,
		133,88,124,203,203,203,203,124,124,88,203,203,124,124,88,133,
		133,88,88,88,88,88,88,88,88,88,124,203,203,124,88,133,
		133,133,133,95,231,231,210,231,231,210,88,88,88,88,88,133,
		133,133,133,95,231,67,216,67,231,210,210,52,52,95,133,133,
		133,133,133,95,231,16,216,16,231,216,210,52,52,210,95,133,
		133,133,95,216,216,216,216,216,216,216,52,52,52,210,95,133,
		133,133,16,210,216,216,210,210,16,216,216,52,210,95,133,133,
		133,16,16,16,210,210,16,16,16,16,216,210,210,52,52,133,
		133,133,133,16,16,16,16,16,216,216,210,210,52,52,133,133,
		133,133,133,133,95,210,210,210,210,210,210,95,133,133,133,133,
		133,133,133,133,18,88,95,95,95,88,88,88,88,133,133,133,
		133,133,133,18,88,95,231,231,231,95,203,203,124,88,133,133,
		133,133,133,18,95,231,231,231,231,222,95,203,203,88,133,133,
		133,133,18,88,95,231,231,231,222,222,95,203,124,88,133,133,
		133,133,18,18,18,95,222,222,222,95,124,124,124,88,133,133,
		133,133,18,222,67,222,95,95,95,88,88,88,88,18,133,133,
		133,133,18,222,67,222,222,67,24,24,24,24,24,18,133,133,
		133,133,133,18,67,67,67,67,67,24,24,24,18,133,133,133,
		133,133,133,18,24,18,67,67,67,24,24,24,18,133,133,133,
		133,133,133,133,18,24,18,67,24,24,24,18,133,133,133,133,
		133,133,133,133,18,18,18,18,18,18,18,18,133,133,133,133,
		133,133,133,133,52,95,52,173,173,95,95,52,133,133,133,133,
		133,133,133,52,95,52,173,173,95,95,95,52,133,133,133,133,
		133,133,133,52,52,52,52,52,52,52,52,52,133,133,133,133
	  },
	},
  }
  
  local Cubes = {}
  
  local LastPedInteraction = 0
  local LastPedTurn
  local MarioInit
  local PedSpawned
  local EvilPed
  
  local MarioState = 1
  local MarioTimer = 0
  local MarioLength = 15
  local MarioAlpha = 0
  local MarioAdder = 1
  local MarioZOff = -20.0
  local MarioZAdd = 0.01
  
  DoAcid = function(time)
	local song = (time and time >= 200000 and 2 or 1)
	SendNUIMessage({type = "playMusic", song = song})
  
	InitCubes()
  
	local step = 0
	local timer = GetGameTimer() 
	local ped = GetPlayerPed(-1)
	local lastPos = GetEntityCoords(ped)
  
	while GetGameTimer() - timer < time do
	  local plyPos = GetEntityCoords(GetPlayerPed(-1))
	  local dist = GetVecDist(lastPos,plyPos)
	  if dist > 1.0 then
		step = step + 1
		if step == 5 then
		  step = 0
		  local dir = (lastPos - plyPos)
		  local vel = GetEntityVelocity(GetPlayerPed(-1))
		  SetEntityCoordsNoOffset(GetPlayerPed(-1),plyPos.x + dir.x, plyPos.y + dir.y,plyPos.z)
		  ForcePedMotionState(GetPlayerPed(-1), -1115154469, 1, 1, 0)
		  SetEntityVelocity(GetPlayerPed(-1), vel.x,vel.y,vel.z)
		end
		lastPos = GetEntityCoords(GetPlayerPed(-1))
	  end
  
	  DrawToons()
	  DrawCubes()
  
	  if MarioInit and not PedSpawned then 
		PedSpawned = true
		Citizen.CreateThread(InitPed)
	  end
	  Wait(0)
	end
  
	ClearTimecycleModifier()
	ShakeGameplayCam('DRUNK_SHAKE', 0.0)  
	SetPedMotionBlur(GetPlayerPed(-1), false)
  
	SetEntityAsMissionEntity(EvilPed,true,true)
	DeleteEntity(EvilPed)
  
	SendNUIMessage({type = "stopMusic"})
  
	Cubes = {}
  
	LastPedInteraction = 0
	LastPedTurn = nil
	MarioInit = nil
	PedSpawned = nil
	EvilPed = nil
  
	MarioState = 1
	MarioTimer = 0
	MarioLength = 15
	MarioAlpha = 0
	MarioAdder = 1
	MarioZOff = -20.0
	MarioZAdd = 0.01
  end
  
  InitPed = function()
	local plyPed = GetPlayerPed(-1)
	local pos = GetEntityCoords(plyPed)
  
	local randomAlt     = math.random(0,359)
	local randomDist    = math.random(50,80)
	local spawnPos      = pos + PointOnSphere(0.0,randomAlt,randomDist)
  
	while World3dToScreen2d(spawnPos.x,spawnPos.y,spawnPos.z) and not IsPointOnRoad(spawnPos.x,spawnPos.y,spawnPos.z) do 
	  randomAlt   = math.random(0,359)
	  randomDist  = math.random(50,80)
	  spawnPos    = GetEntityCoords(GetPlayerPed(-1)) + PointOnSphere(0.0,randomAlt,randomSphere)
	  Citizen.Wait(0)
	end 
  
	EvilPed = ClonePed(plyPed, GetEntityHeading(plyPed), false, false)
	Wait(10)
	SetEntityCoordsNoOffset(EvilPed, spawnPos.x,spawnPos.y,spawnPos.z + 1.0)
	SetPedComponentVariation(EvilPed, 1, 60, 0, 0)
  
	SetEntityInvincible(EvilPed,true)
	SetBlockingOfNonTemporaryEvents(EvilPed,true)
  
	TrackEnt()
  end
  
  TrackEnt = function()
	while true do
	  local dist = GetVecDist(GetEntityCoords(GetPlayerPed(-1)), GetEntityCoords(EvilPed))
	  if dist > 5.0 then
		TaskGoToEntity(EvilPed, GetPlayerPed(-1), -1, 4.0, 100.0, 1073741824, 0)
		Wait(1000)
	  else       
		if not IsTaskMoveNetworkActive(EvilPed) then
		  RequestAnimDict("anim@mp_point")
		  while not HasAnimDictLoaded("anim@mp_point") do Wait(0); end
		  TaskMoveNetworkByName(EvilPed, "task_mp_pointing", 0.5, 0, "anim@mp_point", 24)
		  SetPedCurrentWeaponVisible(EvilPed, 0, 1, 1, 1)
		  SetPedConfigFlag(EvilPed, 36, 1)
		end
  
		if not LastPedTurn or (GetGameTimer() - LastPedTurn) > 1000 then
		  LastPedTurn = GetGameTimer()
		  TaskTurnPedToFaceEntity(EvilPed, GetPlayerPed(-1), -1)
		end
  
		SetTaskMoveNetworkSignalFloat (EvilPed, "Pitch",          0.4)
		SetTaskMoveNetworkSignalFloat (EvilPed, "Heading",        0.5)
		SetTaskMoveNetworkSignalBool  (EvilPed, "isBlocked",      false)
		SetTaskMoveNetworkSignalBool  (EvilPed, "isFirstPerson",  false)
  
		if IsPedRagdoll(EvilPed) then
		  while IsPedRagdoll(EvilPed) do Wait(0); end
		  ClearPedTasksImmediately(EvilPed)
		  Wait(10)
		end
		Wait(0)
	  end
	end
  end
  
  InitCubes = function()
	for i=1,50,1 do
	  local r = math.random(5,255)
	  local g = math.random(5,255)
	  local b = math.random(5,255)
	  local a = math.random(50,100)
  
	  local x = math.random(1,180)
	  local y = math.random(1,359)
	  local z = math.random(15,35)
  
	  Cubes[i] = {pos=PointOnSphere(x,y,z),points={x=x,y=y,z=z},col={r=r, g=g, b=b, a=a}}
	end  
  
	ShakeGameplayCam('DRUNK_SHAKE', 0.0) 
	SetTimecycleModifierStrength(0.0) 
	SetTimecycleModifier("BikerFilter")
	SetPedMotionBlur(GetPlayerPed(-1), true)
  
	local counter = 4000
	local tick = 0
	while tick < counter do
	  tick = tick + 1
	  local plyPos = GetEntityCoords(GetPlayerPed(-1))
	  local adder = 0.1 * (tick/40)
	  SetTimecycleModifierStrength(math.min(0.1 * (tick/(counter/40)),1.5))
	  ShakeGameplayCam('DRUNK_SHAKE', math.min(0.1 * (tick/(counter/40)),1.5))  
	  for k,v in pairs(Cubes) do
		local pos = plyPos + v.pos
		DrawBox(pos.x+adder,pos.y+adder,pos.z+adder,pos.x-adder,pos.y-adder,pos.z-adder, v.col.r,v.col.g,v.col.g,v.col.a)
		local points = {x=v.points.x+0.1,y=v.points.y+0.1,z=v.points.z}
		Cubes[k].points = points
		Cubes[k].pos = PointOnSphere(points.x,points.y,points.z)
	  end
	  Wait(0)
	end
  end
  
  DrawCubes = function()
	local position = GetEntityCoords(GetPlayerPed(-1))
	local adder = 10
	for k,v in pairs(Cubes) do
	  local addX = 0.1
	  local addY = 0.1
  
	  if k%4 == 0 then
		addY = -0.1
	  elseif k%3 == 0 then
		addX = -0.1
	  elseif k%2 == 0 then
		addX = -0.1
		addY = -0.1
	  end
  
	  local pos = position + v.pos
	  DrawBox(pos.x+adder,pos.y+adder,pos.z+adder,pos.x-adder,pos.y-adder,pos.z-adder, v.col.r,v.col.g,v.col.g,v.col.a)
	  local points = {x=v.points.x+addX,y=v.points.y+addY,z=v.points.z}
	  Cubes[k].points = points
	  Cubes[k].pos = PointOnSphere(points.x,points.y,points.z)
	end
  end
  
  DrawToons = function()
	local plyPed = GetPlayerPed(-1)
	local plyPos = GetEntityCoords(plyPed)
  
	local infront = vector3(plyPos.x+35.0, plyPos.y-8.0,plyPos.z)
	local behind  = vector3(plyPos.x-35.0, plyPos.y-8.0,plyPos.z)
  
	if (GetGameTimer() - MarioTimer) > 1000 then
	  MarioTimer = GetGameTimer()
	  MarioState = MarioState + MarioAdder
  
	  if MarioState > #Mario.bits then
		MarioAdder = -1
		MarioState = 2
	  elseif MarioState <= 0 then
		MarioState = 2
		MarioAdder = 1
	  end
	end
  
	DrawMario(infront)
	DrawMario(behind)
  end
  
  DrawMario = function(loc)
	local height = 0
	local width = 0
  
	if MarioZOff < 0.0 then MarioZOff = MarioZOff + MarioZAdd; end
	for k = #Mario.bits[MarioState],1,-1 do
	  local v = Mario.bits[MarioState][k]
	  local pos = vector3(loc.x,loc.y+width,loc.z+height)
	  local col = Mario.cols[v]    
  
	  if MarioAlpha < 255 then
		if not MarioInit then MarioInit = true; end
		MarioAlpha = MarioAlpha + 0.001
	  end
  
	  if v ~= 133 then
		DrawBox(pos.x+0.5,pos.y+0.5,pos.z+0.5 + MarioZOff, pos.x-0.5,pos.y-0.5,pos.z-0.5 + MarioZOff, col.r,col.g,col.b,math.floor(MarioAlpha))
	  end
  
	  width = width + 1
	  if width > MarioLength then
		width = 0
		height = height + 1
	  end
	end
  end
  
  GetVecDist = function(v1,v2)
	if not v1 or not v2 or not v1.x or not v2.x then return 0; end
	return math.sqrt(  ( (v1.x or 0) - (v2.x or 0) )*(  (v1.x or 0) - (v2.x or 0) )+( (v1.y or 0) - (v2.y or 0) )*( (v1.y or 0) - (v2.y or 0) )+( (v1.z or 0) - (v2.z or 0) )*( (v1.z or 0) - (v2.z or 0) )  )
  end
  
  PointOnSphere = function(alt,azu,radius,orgX,orgY,orgZ)
	local toradians = 0.017453292384744
	alt,azu,radius,orgX,orgY,orgZ = ( tonumber(alt or 0) or 0 ) * toradians, ( tonumber(azu or 0) or 0 ) * toradians, tonumber(radius or 0) or 0, tonumber(orgX or 0) or 0, tonumber(orgY or 0) or 0, tonumber(orgZ or 0) or 0
	if      vector3
	then
		return
		vector3(
			 orgX + radius * math.sin( azu ) * math.cos( alt ),
			 orgY + radius * math.cos( azu ) * math.cos( alt ),
			 orgZ + radius * math.sin( alt )
		)
	end
end


function GhostCupCakeEffect(time)
  local timer = 0
  AnimpostfxPlay("DrugsDrivingIn", 10000, 0)
  Citizen.Wait(5000)
  Citizen.CreateThread(function()
    while timer < time do
      if GetSelectedPedWeapon(PlayerPedId()) ~= GetHashKey("WEAPON_KNIFE")  then
        if GetSelectedPedWeapon(PlayerPedId()) ~= GetHashKey("WEAPON_UNARMED") then
          SetCurrentPedWeapon(PlayerPedId(), GetHashKey("WEAPON_UNARMED"),true)
        end
      end

        SetEntityVisible(PlayerPedId(), false, 0)
        Citizen.Wait(500)
        SetEntityVisible(PlayerPedId(), true, 0)
        Citizen.Wait(500)
        timer = timer + 1
    end
    AnimpostfxStop("DrugsDrivingIn")
    SetEntityVisible(PlayerPedId(), true, 0)
end)
end

function AlienEffect()
  StartScreenEffect("DrugsMichaelAliensFightIn", 3.0, 0)
  Citizen.Wait(math.random(5000, 8000))
  StartScreenEffect("DrugsMichaelAliensFight", 3.0, 0)
  Citizen.Wait(math.random(5000, 8000))    
  StartScreenEffect("DrugsMichaelAliensFightOut", 3.0, 0)
  StopScreenEffect("DrugsMichaelAliensFightIn")
  StopScreenEffect("DrugsMichaelAliensFight")
  StopScreenEffect("DrugsMichaelAliensFightOut")
end

--[[
function Effectlsd()
  Wait(math.random(2000,5000))
  AnimpostfxPlay("DrugsDrivingIn", 10000, 0)
  Wait(10000)
  AnimpostfxStop("DrugsDrivingIn")
  DoLsd(60000);
end]]

local ox_inventory = exports[shared.resource]
-----------------------------------------------------------------------------------------------
-- Clientside item use functions
-----------------------------------------------------------------------------------------------

--[[ Item('bandage', function(data, slot)
    local maxHealth = GetEntityMaxHealth(cache.ped)
    local health = GetEntityHealth(cache.ped)
    if health < maxHealth then
        ox_inventory:useItem(data, function(data)
            if data then
                SetEntityHealth(cache.ped, math.min(maxHealth, math.floor(health + maxHealth * 0.04))) -- Heal 8% of max health
                lib.notify({ description = 'You feel a tiny bit better' })
            end
        end)
    else
        lib.notify({ type = 'error', description = 'You don\'t need a bandage right now' })
    end
end) ]]
-- Item('bandage', function(data, slot)
--     local maxHealth = GetEntityMaxHealth(cache.ped)
--     local health = GetEntityHealth(cache.ped)
--     -- print("Max Health: " .. maxHealth) -- Debug print
--     -- print("Current Health: " .. health) -- Debug print
--     if health < maxHealth then
--         ox_inventory:useItem(data, function(data)
--             if data then
--                 SetEntityHealth(cache.ped, math.min(maxHealth, math.floor(health + maxHealth * 0.04))) -- Heal 8% of max health
--                 -- print("Health after bandage: " .. GetEntityHealth(cache.ped)) -- Debug print
--                 local healed = exports['wasabi_ambulance']:stemBleed(1)
--                 -- print("Bleeding stopped: " .. tostring(healed)) -- Debug print
--                 if healed then
--                     lib.notify({ description = 'You feel a tiny bit better and you stopped some bleeding' })
--                 else
--                     lib.notify({ description = 'You feel a tiny bit better' })
--                 end
--             end
--         end)
--     else
--         lib.notify({ type = 'error', description = 'You don\'t need a bandage right now' })
--     end
-- end)

-- Item('firstaid', function(data, slot)
--     local maxHealth = GetEntityMaxHealth(cache.ped)
--     local health = GetEntityHealth(cache.ped)
--     if health < maxHealth then
--         ox_inventory:useItem(data, function(data)
--             if data then
--                 SetEntityHealth(cache.ped, math.min(maxHealth, math.floor(health + maxHealth * 0.125))) -- Heal 25% of max health
-- 				--TriggerEvent('wasabi_ambulance:heal', false, true)
-- 				-- TriggerEvent('wasabi_ambulance:customInjuryClear')
--                 local healed = exports['wasabi_ambulance']:stemBleed(4)
--                 if healed then
--                     lib.notify({ description = 'You feel better already and you stopped the bleeding' })
--                 else
--                     lib.notify({ description = 'You feel better already' })
--                 end
--             end
--         end)
--     else
--         lib.notify({ type = 'error', description = 'You don\'t need first aid right now' })
--     end
-- end)

-- Item('ifaks', function(data, slot)
--     local maxHealth = GetEntityMaxHealth(cache.ped)
--     local health = GetEntityHealth(cache.ped)
--     if health < maxHealth then
--         ox_inventory:useItem(data, function(data)
--             if data then
--                 SetEntityHealth(cache.ped, math.min(maxHealth, math.floor(health + maxHealth * 0.25))) -- Heal 50% of max health
-- 				--TriggerEvent('wasabi_ambulance:heal', false, true)
-- 				-- TriggerEvent('wasabi_ambulance:customInjuryClear')
--                 local healed = exports['wasabi_ambulance']:stemBleed(4)
--                 if healed then
--                     lib.notify({ description = 'You feel better already and you stopped the bleeding' })
--                 else
--                     lib.notify({ description = 'You feel better already' })
--                 end
--             end
--         end)
--     else
--         lib.notify({ type = 'error', description = 'You don\'t need first aid right now' })
--     end
-- end)


-- Item('insurancenote', function(data, slot)
-- 	TriggerServerEvent('ambulance_addInsurance', slot)
-- end)

-- Item('advinsurancenote', function(data, slot)
-- 	TriggerServerEvent('ambulance_addInsurance', slot)
-- end)

Item('armour', function(data, slot)
	if GetPedArmour(cache.ped) < 100 then
		ox_inventory:useItem(data, function(data)
			if data then
				SetPlayerMaxArmour(PlayerData.id, 100)
				SetPedArmour(cache.ped, 100)
			end
		end)
	end
end)

Item('armor', function(data, slot) -- Adds 50% of body armor
	if GetPedArmour(cache.ped) < 100 then
		ox_inventory:useItem(data, function(data)
			if data then
				SetPlayerMaxArmour(PlayerData.id, 100)
				SetPedArmour(cache.ped, 50)
			end
		end)
	end
end)

Item('armorheavy', function(data, slot) -- Adds 100% of body armor
	if GetPedArmour(cache.ped) < 100 then
		ox_inventory:useItem(data, function(data)
			if data then
				SetPlayerMaxArmour(PlayerData.id, 100)
				SetPedArmour(cache.ped, 100)
			end
		end)
	end
end)

Item('armorpolice', function(data, slot) -- Adds 100% of body armor
	if GetPedArmour(cache.ped) < 100 then
		ox_inventory:useItem(data, function(data)
			if data then
				SetPlayerMaxArmour(PlayerData.id, 100)
				SetPedArmour(cache.ped, 100)
			end
		end)
	end
end)

client.parachute = false
Item('parachute', function(data, slot)
	if not client.parachute then
		ox_inventory:useItem(data, function(data)
			if data then
				local chute = `GADGET_PARACHUTE`
				SetPlayerParachuteTintIndex(PlayerData.id, -1)
				GiveWeaponToPed(cache.ped, chute, 0, true, false)
				SetPedGadget(cache.ped, chute, true)
				lib.requestModel(1269906701)
				client.parachute = {CreateParachuteBagObject(cache.ped, true, true), slot?.metadata?.type or -1}
				if slot.metadata.type then
					SetPlayerParachuteTintIndex(PlayerData.id, slot.metadata.type)
				end
			end
		end)
	end
end)



Item('clothing', function(data, slot)
	local metadata = slot.metadata

	if not metadata.drawable then return print('Clothing is missing drawable in metadata') end
	if not metadata.texture then return print('Clothing is missing texture in metadata') end

	if metadata.prop then
		if not SetPedPreloadPropData(cache.ped, metadata.prop, metadata.drawable, metadata.texture) then
			return print('Clothing has invalid prop for this ped')
		end
	elseif metadata.component then
		if not IsPedComponentVariationValid(cache.ped, metadata.component, metadata.drawable, metadata.texture) then
			return print('Clothing has invalid component for this ped')
		end
	else
		return print('Clothing is missing prop/component id in metadata')
	end

	ox_inventory:useItem(data, function(data)
		if data then
			metadata = data.metadata

			if metadata.prop then
				local prop = GetPedPropIndex(cache.ped, metadata.prop)
				local texture = GetPedPropTextureIndex(cache.ped, metadata.prop)

				if metadata.drawable == prop and metadata.texture == texture then
					return ClearPedProp(cache.ped, metadata.prop)
				end

				-- { prop = 0, drawable = 2, texture = 1 } = grey beanie
				SetPedPropIndex(cache.ped, metadata.prop, metadata.drawable, metadata.texture, false);
			elseif metadata.component then
				local drawable = GetPedDrawableVariation(cache.ped, metadata.component)
				local texture = GetPedTextureVariation(cache.ped, metadata.component)

				if metadata.drawable == drawable and metadata.texture == texture then
					return -- item matches (setup defaults so we can strip?)
				end

				-- { component = 4, drawable = 4, texture = 1 } = jeans w/ belt
				SetPedComponentVariation(cache.ped, metadata.component, metadata.drawable, metadata.texture, 0);
			end
		end
	end)
end)

------------sasp badge--------------------------------
Item('badge_sasp', function(data, slot) 
	local pos = GetEntityCoords(GetPlayerPed(-1))
    local rped = GetRandomPedAtCoord(pos['x'], pos['y'], pos['z'], 20.05, 20.05, 20.05, 6, _r)
	ox_inventory:useItem(data, function(data)
		if DoesEntityExist(rped) then
            TaskReactAndFleePed(rped, PlayerPedId())
        end
	end)
end)

-----------------------------------------------------------------------------------------
--- EFFECTS
Item('ccorn', function(data, slot) 
	ox_inventory:useItem(data, function(data)
		if data then
			SetTimecycleModifier("spectator2")
			AnimpostfxPlay("DrugsMichaelAliensFight", 10000001, true)
    		ShakeGameplayCam("DRUNK_SHAKE", 0.6)
			lib.notify({ description = 'This candy might be bad?!!' })
			Citizen.Wait(5000) -- 5 seconds

			AnimpostfxStopAll()
    		ShakeGameplayCam("DRUNK_SHAKE", 0.0)
			SetTimecycleModifierStrength(0.0)
		end
	end)
end)
Item('hcandyg', function(data, slot) 
	ox_inventory:useItem(data, function(data)
		if data then
			SetTimecycleModifier("spectator6")
			AnimpostfxPlay("DrugsMichaelAliensFight", 10000001, true)
    		ShakeGameplayCam("DRUNK_SHAKE", 0.6)
			lib.notify({ description = 'This candy might be bad?!!' })
			Citizen.Wait(5000) -- 5 seconds

			AnimpostfxStopAll()
    		ShakeGameplayCam("DRUNK_SHAKE", 0.0)
			SetTimecycleModifierStrength(0.0)
		end
	end)
end)
Item('hcandyr', function(data, slot) 
	ox_inventory:useItem(data, function(data)
		if data then
			SetTimecycleModifier("spectator6")
			AnimpostfxPlay("DrugsMichaelAliensFight", 10000001, true)
    		ShakeGameplayCam("DRUNK_SHAKE", 0.6)
			lib.notify({ description = 'This candy might be bad?!!' })
			Citizen.Wait(5000) -- 5 seconds

			AnimpostfxStopAll()
    		ShakeGameplayCam("DRUNK_SHAKE", 0.0)
			SetTimecycleModifierStrength(0.0)
		end
	end)
end)

--Weed drug effects -- 6 seconds ---

Item('regweed_joint', function(data, slot)
	ox_inventory:useItem(data, function(data)
		if data then
			SetTimecycleModifier("spectator6")
			AnimpostfxPlay("DrugsMichaelAliensFight", 10000001, true)
			ShakeGameplayCam("DRUNK_SHAKE", 0.6)

			Citizen.Wait(6000) -- 6 seconds

			AnimpostfxStopAll()
			ShakeGameplayCam("DRUNK_SHAKE", 0.0)
			SetTimecycleModifierStrength(0.0)
		end
	end)
end)
Item('bananakush_joint', function(data, slot)
	ox_inventory:useItem(data, function(data)
		if data then
			SetTimecycleModifier("spectator2")
			AnimpostfxPlay("HeistCelebPass", 10000001, true)
			ShakeGameplayCam("DRUNK_SHAKE", 0.6)

			Citizen.Wait(6000) -- 6 seconds

			AnimpostfxStopAll()
			ShakeGameplayCam("DRUNK_SHAKE", 0.0)
			SetTimecycleModifierStrength(0.0)
		end
	end)
end)
Item('purplehaze_joint', function(data, slot)
	ox_inventory:useItem(data, function(data)
		if data then
			SetTimecycleModifier("spectator6")
			AnimpostfxPlay("DrugsMichaelAliensFight", 10000001, true)
			ShakeGameplayCam("DRUNK_SHAKE", 0.6)

			Citizen.Wait(6000) -- 6 seconds

			AnimpostfxStopAll()
			ShakeGameplayCam("DRUNK_SHAKE", 0.0)
			SetTimecycleModifierStrength(0.0)
		end
	end)
end)
Item('bluedream_joint', function(data, slot)
	ox_inventory:useItem(data, function(data)
		if data then
			SetTimecycleModifier("spectator2")
			AnimpostfxPlay("DrugsTrevorClownsFight", 10000001, true)
			ShakeGameplayCam("DRUNK_SHAKE", 0.6)

			Citizen.Wait(6000) -- 6 seconds

			AnimpostfxStopAll()
			ShakeGameplayCam("DRUNK_SHAKE", 0.0)
			SetTimecycleModifierStrength(0.0)
		end
	end)
end)

Item('chuckydoll', function(data, slot)
	ox_inventory:useItem(data, function(data)
		if data then
			local itemName = "chuckydoll" -- Ensure this is correct
			print("Triggering changeped with item name: " .. itemName) -- Debug output
			TriggerEvent('changeped', itemName) -- Trigger the changeped event with the item name
		end
	end)
end)

Item('billysawdoll', function(data, slot)
	ox_inventory:useItem(data, function(data)
		if data then
			local itemName = "billysawdoll" -- Ensure this is correct
			TriggerEvent('changeped', itemName) -- Trigger the changeped event with the item name
		end
	end)
end)

Item('pennywisedoll', function(data, slot)
	ox_inventory:useItem(data, function(data)
		if data then
			local itemName = "pennywisedoll" -- Ensure this is correct
			TriggerEvent('changeped', itemName) -- Trigger the changeped event with the item name
		end
	end)
end)

Item('brightburndoll', function(data, slot)
	ox_inventory:useItem(data, function(data)
		if data then
			local itemName = "brightburndoll" -- Ensure this is correct
			TriggerEvent('changeped', itemName) -- Trigger the changeped event with the item name
		end
	end)
end)

Item('brightburndoll', function(data, slot)
	ox_inventory:useItem(data, function(data)
		if data then
			local itemName = "brightburndoll" -- Ensure this is correct
			TriggerEvent('changeped', itemName) -- Trigger the changeped event with the item name
		end
	end)
end)

Item('candy_corn', function(data, slot)
    ox_inventory:useItem(data, function(data)
        if data then
			DoAcid(20000)  -- Call the reusable effect function
        end
    end)
end)

Item('halloween_lollipop', function(data, slot)
    ox_inventory:useItem(data, function(data)
        if data then
            GhostCupCakeEffect(30)  -- Call the reusable effect function
        end
    end)
end)

Item('alien_gummy_brains', function(data, slot)
    ox_inventory:useItem(data, function(data)
        if data then
            AlienEffect()  -- Call the reusable effect function
        end
    end)
end)

--candyCornEffect
--billysawdoll
--- end of effects
-----------------------------------------------------------------------------------------


--- ZAT CRAFTING
Item('craftingtable', function(data, slot)
	local model = "gr_prop_gr_bench_04b"
	local id = "CraftingTable"..math.random(111111,999999)
	TriggerEvent("zat-crafting:client:PlaceTable", id, model)
end)

Item('x_harddrivepart', function(data, slot)
	TriggerServerEvent("combineParts:server",'x_harddrivepart',10,'x_harddrive',1)
end)

Item('accesscardpart', function(data, slot)
	TriggerServerEvent("combineParts:server",'accesscardpart',10,'accesscard',1)
end)

Item('accesscardpart2', function(data, slot)
	TriggerServerEvent("combineParts:server",'accesscardpart2',10,'accesscard2',1)
end)

Item('x_devicepart', function(data, slot)
	TriggerServerEvent("combineParts:server",'x_devicepart',10,'x_device',1)
end)

Item('greensimfragment ', function(data, slot)
	TriggerServerEvent("combineParts:server",'greensimfragment ',5,'greensim',1)
end)

Item('lockpickpart', function(data, slot)
	TriggerServerEvent("combineParts:server",'lockpickpart',5,'lockpick',1)
end)

--racing stuff

Item('race_money', function(data, slot)
	TriggerServerEvent("combineParts:server",'race_money',1,'cash',1000)
end)

Item('armorplate', function(data, slot) 
	TriggerServerEvent('combineTwoParts:server', 'armor', 1, 'armorplate', 1, 'armorheavy', 1)
end)

--[[
Item('stage1_engine', function(data, slot)
	TriggerServerEvent('combineTwoParts:server', 'stage1_engine', 1, 'stage2_component', 1, 'stage2_engine', 1)
end)
Item('stage1_transmission', function(data, slot)
	TriggerServerEvent('combineTwoParts:server', 'stage1_transmission', 1, 'stage2_component', 1, 'stage2_transmission', 1)
end)
Item('stage1_brakes', function(data, slot)
	TriggerServerEvent('combineTwoParts:server', 'stage1_brakes', 1, 'stage2_component', 1, 'stage2_brakes', 1)
end)
Item('stage2_engine', function(data, slot)
	TriggerServerEvent('combineTwoParts:server', 'stage2_engine', 1, 'stage3_component', 1, 'stage3_engine', 1)
end)
Item('stage2_transmission', function(data, slot)
	TriggerServerEvent('combineTwoParts:server', 'stage2_transmission', 1, 'stage3_component', 1, 'stage3_transmission', 1)
end)
Item('stage2_brakes', function(data, slot)
	TriggerServerEvent('combineTwoParts:server', 'stage2_brakes', 1, 'stage3_component', 1, 'stage3_brakes', 1)
end)

-- Combine armor with plate
Item('armorplate', function(data, slot) 
	TriggerServerEvent('combineTwoParts:server', 'armor', 1, 'armorplate', 1, 'armorheavy', 1)
end)]]

--weapons for gangs


-- ZAT FARMING
-- POTS
local Pots = {
	['farm_pot_xs'] = {
		model       = 'prop_milk_crate_xs',
	},
	['farm_pot_s'] = {
		model       = 'prop_milk_crate_s',
	},
	['farm_pot_m'] = {
		model       = 'prop_milk_crate_m',
	},
	['farm_pot_l'] = {
		model       = 'prop_milk_crate_l',
	},    
}

for k, v in pairs(Pots) do 
	Item(k, function(data, slot)
		ox_inventory:useItem(data, function(data)
			if data then
				TriggerEvent("zat-farming:client:UseItem", slot.name, v.model, 'pots')
				TriggerServerEvent('zat-farming:server:removeitem', slot.name, 1, slot.slot)
			end
		end)
	end)
end
-- LAMPS

local Lights = { -- add any lights you want ...
	['farm_light_s'] = { 
		model       = 'h4_prop_x17_sub_lampa_small_blue',
	},                   
	['farm_light_l'] = { 
		model       = 'h4_prop_x17_sub_lampa_large_blue',
	}, 
}

for k, v in pairs(Lights) do 
	Item(k, function(data, slot)
		ox_inventory:useItem(data, function(data)
			if data then
				TriggerEvent("zat-farming:client:UseItem", slot.name, v.model, 'lights')
				TriggerServerEvent('zat-farming:server:removeitem', slot.name, 1, slot.slot)
			end
		end)
	end)
end

-- TOOLS 
local Tools = { -- add any lights you want ...
    ['farm_mil'] = { 
        model       = 'prop_farming_mil',
    },                   
    ['farm_compost'] = { 
        model       = 'prop_farming_compost',
    }, 
    ['farm_sink_s'] = { 
        model       = 'prop_ff_sink_02',
    }, 
    ['farm_sink_m'] = { 
        model       = 'prop_bar_sink_01',
    }, 
    ['moon_distillation'] = { 
        model       = 'prop_moonshine',
    },  
}

for k, v in pairs(Tools) do 
	Item(k, function(data, slot)
		ox_inventory:useItem(data, function(data)
			if data then
				TriggerEvent("zat-farming:client:UseItem", slot.name, v.model, 'tools')
				TriggerServerEvent('zat-farming:server:removeitem', slot.name, 1, slot.slot)
			end
		end)
	end)
end

-- Water Can

Item('farm_water', function(data, slot)
	ox_inventory:useItem(data, function(data)
		if data then
			if slot.metadata.water == nil then
				slot.metadata.water = 0
			end
			if slot.metadata.water == 0 then
				-- Remove Item
				TriggerEvent('zat-farming:client:FillWater', slot)
			end
		end
	end)
end)

 
-- Moonshine
Item('moon_moonshine_pack', function(data, slot)
	ox_inventory:useItem(data, function(data)
		if data then
			if slot.metadata.purity == nil then
				slot.metadata.purity = 0
			end
			TriggerServerEvent('zat-farming:server:additem', 'moon_moonshine', 20, slot.metadata)
			TriggerServerEvent('zat-farming:server:removeitem', slot.name, 1, slot.slot)
		end
	end)
end)

Item('moon_moonshine', function(data, slot)
	ox_inventory:useItem(data, function(data)
		if data then
			TriggerEvent('zat-farming:client:StartEffect', slot)
			TriggerServerEvent('zat-farming:server:removeitem', slot.name, 1, slot.slot)
		end
	end)
end)
--zat car rentalpaper

Item('zat-rentalpaper', function(data, slot)
	TriggerEvent("zat-carrental:client:UseRentalPaper", slot.metadata)
end)
----------------------------------------------------------------------------------------------

exports('Items', function(item) return getItem(nil, item) end)
exports('ItemList', function(item) return getItem(nil, item) end)

RegisterNetEvent('ox_inventory:ClientagraphicNovelTeleport', function (coords)
	if lib.progressBar({
		duration = 10000,
		label = "You are losing control of your urges...",
		useWhileDead = false,
		allowCuffed = false,
		canCancel = false,
		disable = {
			move = true,
			combat = true,
		}
	}) then
	-- 	"teleport", "Whisking you away!", duration, false, true, {
	-- 	disableMovement = true,
	-- 	disableCarMovement = false,
	-- 	disableMouse = false,
	-- 	disableCombat = true,
	-- }, {}, {}, {} 
		local coordZ = coords.z
		local isSafe, z = GetGroundZFor_3dCoord(
			coords.x,
			coords.y,
			coords.z+0.5,
			false
		)

		if isSafe then coordZ = z end

		-- if destination.allowVehicle and cache.vehicle then
		-- 	SetPedCoordsKeepVehicle(
		-- 		cache.ped,
		-- 		destination.coords.x,
		-- 		destination.coords.y,
		-- 		coordZ
		-- 	)

		-- 	SetVehicleOnGroundProperly(cache.vehicle)
		-- else
			SetEntityCoords(
				cache.ped,
				coords.x,
				coords.y,
				coordZ,
				true, false, false, false
			)
		-- end

		if type(coords) == 'vector4' then
			SetEntityHeading(cache.ped, coords.w)
		end
	end
end)
----------------------------------------------------------------------------------------------

----[[Clothing Bag]]----

--[[Functions]]--
-- Function to check if cooldown is active
local function isOnCooldown(slot)
    local slotData = PlayerData.inventory[slot]
    if not slotData?.metadata?.lastUsed then return false end
    return (GetGameTimer() - slotData.metadata.lastUsed) < 600000 -- 10 minutes in milliseconds
end

-- Function to get remaining cooldown time in minutes
local function getRemainingCooldown(slot)
    local slotData = PlayerData.inventory[slot]
    if not slotData?.metadata?.lastUsed then return 0 end
    local remaining = math.ceil((600000 - (GetGameTimer() - slotData.metadata.lastUsed)) / 60000)
    return remaining > 0 and remaining or 0
end

-- Function to get current outfit components
local function getCurrentOutfit()
    local outfit = {
        components = {},
        props = {}
    }
    
    -- Save clothing components (body parts)
    for i = 0, 11 do
        outfit.components[i] = {
            drawable = GetPedDrawableVariation(cache.ped, i),
            texture = GetPedTextureVariation(cache.ped, i),
            palette = GetPedPaletteVariation(cache.ped, i)
        }
    end
    
    -- Save props (accessories)
    for i = 0, 7 do
        outfit.props[i] = {
            drawable = GetPedPropIndex(cache.ped, i),
            texture = GetPedPropTextureIndex(cache.ped, i)
        }
    end
    
    return outfit
end

-- Function to apply saved outfit
local function applyOutfit(outfit)
    -- Apply clothing components
    for componentId, data in pairs(outfit.components) do
        SetPedComponentVariation(cache.ped, componentId, data.drawable, data.texture, data.palette)
    end
    
    -- Apply props
    for propId, data in pairs(outfit.props) do
        if data.drawable == -1 then
            ClearPedProp(cache.ped, propId)
        else
            SetPedPropIndex(cache.ped, propId, data.drawable, data.texture, true)
        end
    end
end

-- Function to convert GTA native outfit to onex format
local function convertToOnexFormat(outfit)
    local onexOutfit = {
        ["pants"] = {
            item = outfit.components[4].drawable,
            texture = outfit.components[4].texture
        },
        ["arms"] = {
            item = outfit.components[3].drawable,
            texture = outfit.components[3].texture
        },
        ["shirt"] = {
            item = outfit.components[8].drawable,
            texture = outfit.components[8].texture
        },
        ["torso"] = {
            item = outfit.components[11].drawable,
            texture = outfit.components[11].texture
        },
        ["shoes"] = {
            item = outfit.components[6].drawable,
            texture = outfit.components[6].texture
        },
        ["glass"] = {
            item = outfit.props[1].drawable,
            texture = outfit.props[1].texture
        },
        ["ear"] = {
            item = outfit.props[2].drawable,
            texture = outfit.props[2].texture
        },
        ["vest"] = {
            item = outfit.components[9].drawable,
            texture = outfit.components[9].texture
        },
        ["hat"] = {
            item = outfit.props[0].drawable,
            texture = outfit.props[0].texture
        },
        ["mask"] = {
            item = outfit.components[1].drawable,
            texture = outfit.components[1].texture
        },
        ["decals"] = {
            item = outfit.components[10].drawable,
            texture = outfit.components[10].texture
        }
    }
    return onexOutfit
end

--[[Events]]--
-- Save current outfit to bag
RegisterNetEvent('clothing_bag:saveOutfit')
AddEventHandler('clothing_bag:saveOutfit', function(slot)
	print("Attempting to save outfit...")
	lib.callback('srp-society:server:playerHasSkill', false, function(hasSkill)
		if hasSkill then
			local outfit = getCurrentOutfit()
			local metadata = {
				outfit = outfit,
				label = "Saved Outfit",
				lastUsed = 0 -- Initialize cooldown timer
			}

			TriggerServerEvent('clothing_bag:updateMetadata', slot, metadata)
			lib.notify({
				title = 'Success',
				description = 'Current outfit saved to bag',
				type = 'success'
			})
		else
			print("You don't have the required society skills to use this feature.")
			lib.notify({
				title = 'Error',
				description = "You don't have the required society skills to use the clothing bag.",
				type = 'error'
			})
		end
	end,'GangEssentials2')
end)

-- Load outfit from bag
RegisterNetEvent('clothing_bag:loadOutfit')
AddEventHandler('clothing_bag:loadOutfit', function(slot)
	print("Attempting to load outfit...")
	lib.callback('srp-society:server:playerHasSkill', false, function(hasSkill)
		if hasSkill then
			local slotData = PlayerData.inventory[slot]
			if not slotData or not slotData.metadata or not slotData.metadata.outfit then
				lib.notify({
					title = 'Error',
					description = 'No outfit saved in this bag',
					type = 'error'
				})
				return
			end

			if isOnCooldown(slot) then
				lib.notify({
					title = 'Cooldown Active',
					description = string.format('Please wait %d minutes', getRemainingCooldown(slot)),
					type = 'error'
				})
				return
			end

			if lib.progressBar({
					duration = math.random(3000, 6000),
					label = 'Changing clothes...',
					position = 'bottom',
					useWhileDead = false,
					canCancel = true,
					disable = {
						car = true,
						move = true,
						combat = true,
					},
					anim = {
						dict = 'clothingshirt',
						clip = 'try_shirt_positive_d'
					},
				}) then
				-- Convert and apply outfit using onex-creation
				local onexOutfit = convertToOnexFormat(slotData.metadata.outfit)
				TriggerEvent('onex-creation:client:loadCustomOutfit', onexOutfit)

				-- Update cooldown
				local metadata = slotData.metadata
				metadata.lastUsed = GetGameTimer()
				TriggerServerEvent('clothing_bag:updateMetadata', slot, metadata)

				lib.notify({
					title = 'Success',
					description = 'Changed into saved outfit',
					type = 'success'
				})
			end
		else
			lib.notify({
				description = "You don't have the required society skills to use the clothing bag.",
				title = 'Error',
				type = 'error'
			})
		end
	end,'GangEssentials2')
end)

--[[exports]]--
exports('clothing_bag', function(data, slot)
    lib.registerContext({
        id = 'clothing_bag_menu',
        title = 'Clothing Bag',
        options = {
            {
                title = 'Save Current Outfit',
                description = 'Save your current outfit to the bag',
                icon = 'fas fa-save',
                onSelect = function()
                    TriggerEvent('clothing_bag:saveOutfit', slot.slot)
                end
            },
            {
                title = 'Change to Saved Outfit',
                description = 'Change into the saved outfit (10 min cooldown)',
                icon = 'fas fa-tshirt',
                onSelect = function()
                    TriggerEvent('clothing_bag:loadOutfit', slot.slot)
                end
            }
        }
    })
    
    lib.showContext('clothing_bag_menu')
end)

----[[End Clothing Bag]]----

exports('openCraftingUI', function()
    TriggerEvent('openCraftingUI', 'smelting') -- Directly trigger the event for smelting
end)

----[[Weapon Parts Crate]]----
Item('crate_weapon', function(data, slot)
    ox_inventory:useItem(data, function(data)
        if data then
            -- Animation dictionary and name
            local animDict = "anim@amb@clubhouse@tutorial@bkr_tut_ig3@"
            local anim = "machinic_loop_mechandplayer"
            
            -- Load animation
            lib.requestAnimDict(animDict)
            
            -- Spawn crate prop in front of player
            local playerPed = cache.ped
            local coords = GetEntityCoords(playerPed)
            local forward = GetEntityForwardVector(playerPed)
            local x = coords.x + forward.x * 0.7
            local y = coords.y + forward.y * 1.0
            local z = coords.z - 1.0
            
            -- Create the crate prop
            local crateProp = CreateObject(`ba_prop_battle_rsply_crate_gr_02a`, x, y, z, true, true, true)
            PlaceObjectOnGroundProperly(crateProp)
            FreezeEntityPosition(crateProp, true)
            
            -- Set crate heading to match player's heading
            local playerHeading = GetEntityHeading(playerPed)
            SetEntityHeading(crateProp, playerHeading)

            -- Set player heading towards crate
            local crate_coords = GetEntityCoords(crateProp)
            local player_coords = GetEntityCoords(playerPed)
            local heading = GetHeadingFromVector_2d(crate_coords.x - player_coords.x, crate_coords.y - player_coords.y)
            SetEntityHeading(playerPed, heading)

            -- Start mechanic/kneeling animation
            TaskPlayAnim(playerPed, animDict, anim, 8.0, -8.0, -1, 1, 0, false, false, false)

            if lib.progressBar({
                duration = 5000,
                label = 'Opening crate...',
                useWhileDead = false,
                canCancel = true,
                disable = {
                    car = true,
                    move = true,
                    combat = true,
                },
            }) then
                -- List of possible weapon parts
                local weaponParts = {
                    {item = 'smg_stock', amount = 1, chance = 100},
                    {item = 'smg_frame', amount = 1, chance = 100},
                    {item = 'smg_barrel', amount = 1, chance = 100},
                    {item = 'smg_parts', amount = 5, chance = 100},
                    {item = 'smg_trigger', amount = 1, chance = 100},
                }

                -- Clear animations
                ClearPedTasks(playerPed)
                
                -- Delete the crate prop
                DeleteObject(crateProp)
                SetModelAsNoLongerNeeded(`ba_prop_battle_rsply_crate_gr_02a`)

                -- Trigger server event to give random parts
                TriggerServerEvent('ox_inventory:giveWeaponParts', weaponParts)
            else
                -- Clear animations if cancelled
                ClearPedTasks(playerPed)
                
                -- Delete the crate prop
                DeleteObject(crateProp)
                SetModelAsNoLongerNeeded(`ba_prop_battle_rsply_crate_gr_02a`)
            end
        end
    end)
end)
----[[End Weapon Parts Crate]]----
----[[Weapon Repair Kit]]----
-- Define your additional repair requirements here
-- If a weapon is explicitly listed in weaponRepairRequirements, it will override group requirements
local weaponRepairRequirements = {
    -- Example of overriding a specific weapon by exact hash
    [GetHashKey('WEAPON_KRISSVECTOR')] = {
    },
	[GetHashKey('WEAPON_CARBINERIFLE_MK2')] = {
    },
	[GetHashKey('WEAPON_VINTAGEPISTOL')] = {

    },
	[GetHashKey('WEAPON_SNSPISTOL')] = {

    },
	[GetHashKey('WEAPON_fM1_P226')] = {

    },
	[GetHashKey('WEAPON_tglock19')] = {

    },
	[GetHashKey('WEAPON_HEAVYPISTOL')] = {

    },
	[GetHashKey('WEAPON_REVOLVER_MK2')] = {

    },
}

-- fallback requirements by weapon group
local weaponGroupRequirements = {
    [GetHashKey('GROUP_PISTOL')] = {
		{ name = 'pistol_parts', count = 1 },
        { name = 'merryweather_parts', count = 1 }
    },
    [GetHashKey('GROUP_SMG')] = {
        { name = 'smg_parts', count = 1 },
        { name = 'merryweather_parts', count = 2 }
    },
    [GetHashKey('GROUP_RIFLE')] = {
		{ name = 'ar_parts', count = 1 },
        { name = 'merryweather_parts', count = 3 }
    },
    -- Add more if needed ...
}

Item('repair_weaponkit', function(data, slot)
    local ped = PlayerPedId()
    local currentWeapon = exports.ox_inventory:getCurrentWeapon()

    if currentWeapon then
        local customReq = weaponRepairRequirements[currentWeapon.hash]
        local groupReq = weaponGroupRequirements[GetWeapontypeGroup(currentWeapon.hash)]
        -- Final requirement set
        local requirementList = customReq or groupReq
        -- If no requirements defined for this weapon or group, just repair it
        if requirementList then
            -- Check that the player has the required items
            for _, req in ipairs(requirementList) do
                local itemCount = exports.ox_inventory:Search('count', req.name)
                if itemCount < req.count then
                    lib.notify({
                        type = 'error',
                        description = ('You need %d of %s to repair this weapon.'):format(req.count, req.name),
                        duration = 5000
                    })
                    return
                end
            end
        end

        if lib.progressBar({
            duration = 5000,
            label = 'Repairing Weapon...',
            useWhileDead = false,
            canCancel = true,
            disable = { car = true, move = true, combat = true },
            anim = {
                dict = 'anim@amb@clubhouse@tutorial@bkr_tut_ig3@',
                clip = 'machinic_loop_mechandplayer',
                flag = 1
            },
            prop = {
                model = `prop_tool_screwdvr01`,
                pos = vec3(0.03, 0.03, 0.02),
                rot = vec3(0.0, 0.0, -1.5)
            }
        }) then

            -- If requirements exist, remove the items
            if requirementList then
                for _, req in ipairs(requirementList) do
                    TriggerServerEvent('ox_inventory:removeItem', req.name, req.count)
                end
            end

            -- Update the weapon durability
            local success = lib.callback.await('ox_inventory:updateWeaponDurability', source, currentWeapon.slot, 100)

            if success ~= false then
                -- Remove the repair kit itself
                TriggerServerEvent('ox_inventory:removeItem', data.name, 1, data.metadata, slot)

                -- Success sound/notification
                PlaySoundFrontend(-1, 'Drill_Pin_Break', 'DLC_HEIST_FLEECA_SOUNDSET', 1)
                lib.notify({
                    type = 'success',
                    description = 'Successfully repaired your weapon!',
                    duration = 5000
                })
            else
                lib.notify({
                    type = 'error',
                    description = 'Failed to repair weapon!',
                    duration = 5000
                })
            end
        else
            lib.notify({
                type = 'error',
                description = 'Cancelled weapon repair',
                duration = 3000
            })
        end
    else
        lib.notify({
            type = 'error',
            description = 'You must have a weapon equipped to repair it!',
            duration = 5000
        })
    end
end)


----[[End Weapon Repair Kit]]----
----[[humane_labs_lockedbox]]----
Item('humane_labs_lockedbox', function(data, slot)
    exports['srp-humanlabs']:openHumanLabsBox(data, slot)
end)
----[[End humane_labs_lockedbox]]----


-- Watering Can Use Handler for srp-farming
local srpFarmingCans = {
    'small_water_can',
    'medium_water_can',
    'large_water_can',
    'premium_water_can'
}

for _, canName in ipairs(srpFarmingCans) do
    Item(canName, function(data, slot)
        ox_inventory:useItem(data, function(data)
            if data then
                -- Trigger srp-farming's refill event
                TriggerEvent('srp-farming:client:checkWaterSource', slot)
            end
        end)
    end)
end
-- End srp-farming watering can handler

-- Fertilizer Use Handler for srp-farming
local srpFarmingFertilizers = {
    'basic_fertilizer',
    'advanced_fertilizer',
    'premium_fertilizer',
    'organic_fertilizer',
    'super_fertilizer'
}

for _, fertName in ipairs(srpFarmingFertilizers) do
    Item(fertName, function(data, slot)
        ox_inventory:useItem(data, function(data)
            if data then
                -- Trigger srp-farming's fertilizing event
                TriggerEvent('srp-farming:client:checkPlantForFertilizing', slot)
            end
        end)
    end)
end
-- End srp-farming fertilizer handler


----------------------------------------------------------------------------------------------
return Items
