import { createSlice, PayloadAction } from '@reduxjs/toolkit';

interface menusState {
  open: boolean;
  count: number;
  maxCount: number;
  help: boolean;
  settings: boolean;
}

const initialState: menusState = {
  open: false,
  count: 0,
  maxCount: 0,
  help: false,
  settings: false,
};

export const menusSlice = createSlice({
  name: 'split',
  initialState,
  reducers: {
    openSplit(state, action: PayloadAction<{ count: number, }>) {
      state.open = true;
      state.count = 1;
      state.maxCount = action.payload.count;
    },
    closeSplit(state) {
      state.open = false;
    },
    setSplit(state, action: PayloadAction<{ count: number}>) {
      state.count = action.payload.count;
    },
    toggleHelp(state) {
      state.help = !state.help;
      // console.log(state.help);
    },
    toggleSettings(state) {
      state.settings = !state.settings;
      // console.log(state.help);
    }
  },
});

export const { openSplit, closeSplit, setSplit, toggleHelp, toggleSettings } = menusSlice.actions;

export default menusSlice.reducer;
