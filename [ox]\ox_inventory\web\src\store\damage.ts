import { createSlice, PayloadAction } from '@reduxjs/toolkit';
import { bodyPart } from '../typings/damage';

interface bodyPartI {
    label:  string,
    limp:   boolean,
    level:  number,
    bleed:  number,
    type:   string,
}

interface damageState {
    head:       bodyPartI,
    neck:       bodyPartI,
    spine:      bodyPartI,
    upper_body:  bodyPartI,
    lower_body:  bodyPartI,
    left_arm:   bodyPartI,
    left_leg:   bodyPartI,
    right_arm:  bodyPartI,
    right_leg:  bodyPartI,
}

const initialState: damageState = {
    head: {
        label:  "HEAD",
        limp:   false,
        level:  0,
        bleed:  0,
        type:   "",
    },
    neck: {
        label:  "NECK",
        limp:   false,
        level:  0,
        bleed:  0,
        type:   "",
    },
    spine: {
        label:  "SPINE",
        limp:   false,
        level:  0,
        bleed:  0,
        type:   "",
    },
    upper_body: {
        label:  "UPPER BODY",
        limp:   false,
        level:  0,
        bleed:  0,
        type:   "",
    },
    lower_body: {
        label:  "LOWER BODY",
        limp:   false,
        level:  0,
        bleed:  0,
        type:   "",
    },
    left_arm: {
        label:  "LEFT ARM",
        limp:   false,
        level:  0,
        bleed:  0,
        type:   "",
    },
    left_leg: {
        label:  "LEFT LEG",
        limp:   false,
        level:  0,
        bleed:  0,
        type:   "",
    },
    right_arm: {
        label:  "RIGHT ARM",
        limp:   false,
        level:  0,
        bleed:  0,
        type:   "",
    },
    right_leg: {
        label:  "RIGHT LEG",
        limp:   false,
        level:  0,
        bleed:  0,
        type:   "",
    },
}

const damageSlice = createSlice({
    name: 'damage',
    initialState,
    reducers: {
        setDamage(state, action: PayloadAction<{ head: bodyPart, neck: bodyPart, spine:bodyPart, upperBody:bodyPart, lowerBody:bodyPart, left_arm:bodyPart, left_leg:bodyPart, right_arm:bodyPart, right_leg:bodyPart, }>) {
            // state = action.payload;
            
            for(const name in action.payload){
                state[name] = action.payload[name]
            }
        },
    }
})

export const { setDamage } = damageSlice.actions;
export default damageSlice.reducer;
