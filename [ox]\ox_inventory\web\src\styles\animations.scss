@keyframes pulse {
	0% {
		transform: scale(0.95);
		box-shadow: 0 0 0 0 hsla(calc(var(--sectionTitleHue) - 1.2deg) var(--sectionTitleSat) var(--sectionTitleLum) / 0.7);
	}

	70% {
		transform: scale(1);
		box-shadow: 0 0 0 7px rgba(0, 0, 0, 0);
	}

	100% {
		transform: scale(0.95);
		box-shadow: 0 0 0 0 rgba(0, 0, 0, 0);
	}
}

// Transitions
.transition-fade-enter {
  opacity: 0;
}

.transition-fade-enter-active {
  opacity: 1;
  transition: opacity 200ms;
}

.transition-fade-exit {
  opacity: 1;
}

.transition-fade-exit-active {
  opacity: 0;
  transition: opacity 200ms;
}

.transition-slide-up-enter {
  transform: translateY(200px)
}

.transition-slide-up-enter-active {
  transform: translateY(0px);
  transition: all 200ms;
}

.transition-slide-up-exit {
  transform: translateY(0px);
}

.transition-slide-up-exit-active {
  transform: translateY(200px);
  transition: all 200ms;
}