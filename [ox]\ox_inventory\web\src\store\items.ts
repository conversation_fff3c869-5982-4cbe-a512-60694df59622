import { ItemData } from '../typings/item';

export const Items: {
  [key: string]: ItemData | undefined;
} = {
  water: {
    name: 'water',
    close: false,
    label: 'VODA',
    stack: true,
    usable: true,
    count: 0,
  },
  burger: {
    name: 'burger',
    close: false,
    label: 'BURGR',
    stack: false,
    usable: false,
    count: 0,
  },
  lockpick: {
    name: 'lockpick',
    close: false,
    label: 'Lockpicks',
    stack: true,
    usable: true,
    count: 0,
  },
  iron: {
    name: 'iron',
    close: false,
    label: 'Iron',
    stack: true,
    usable: false,
    count: 0,
  },
  copper: {
    name: 'copper',
    close: false,
    label: 'Copper',
    stack: true,
    usable: false,
    count: 0,
  },
  powersaw: {
    name: 'powersaw',
    close: false,
    label: 'Power Saw',
    stack: false,
    usable: true,
    count: 0,
  },
};
