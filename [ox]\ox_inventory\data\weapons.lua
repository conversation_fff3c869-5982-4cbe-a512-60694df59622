return {
	Weapons = {
		['WEAPON_BATTLERIFLE'] = {
			label = 'Battle Rifle',
			weight = 3300,
			durability = 0.03,
			ammoname = 'ammo-rifle2',
			caliber = '7.62x51mm',
		},

		['WEAPON_SNOWLAUNCHER'] = {
			label = 'Snowball Launcher',
			weight = 1000,
			durability = 0.03,
			ammoname = 'snowball',
		},

		['WEAPON_TECPISTOL'] = {
			label = 'Tactical SMG',
			weight = 1500,
			durability = 0.075,
			ammoname = 'ammo-9',
			caliber = '9x19mm',
			buttons = {
				{
					label = 'Rename Weapon',
					action = function (slot)
						RenameItem(slot)
					end
				}
			},
		},

		['WEAPON_ADVANCEDRIFLE'] = {
			label = 'Advanced Rifle',
			weight = 3100,
			durability = 0.03,
			ammoname = 'ammo-rifle',
			caliber = '5.56x45mm',
		},

		['WEAPON_APPISTOL'] = {
			label = 'AP Pistol',
			weight = 1400,
			durability = 0.1,
			ammoname = 'ammo-9',
			caliber = '9x19mm',
			buttons = {
				{
					label = 'Rename Weapon',
					action = function (slot)
						RenameItem(slot)
					end
				}
			},
		},

		['WEAPON_ASSAULTRIFLE'] = {
			label = 'Assault Rifle',
			weight = 4500,
			durability = 0.03,
			ammoname = 'ammo-rifle2',
			caliber = '5.56x45mm',
		},

		['WEAPON_ASSAULTRIFLE_MK2'] = {
			label = 'Assault Rifle MK2',
			weight = 2950,
			durability = 0.03,
			ammoname = 'ammo-rifle2',
			caliber = '5.56x45mm',
		},

		['WEAPON_ASSAULTSHOTGUN'] = {
			label = 'Assault Shotgun',
			weight = 5200,
			durability = 0.05,
			ammoname = 'ammo-shotgun',
			caliber = '12 Gauge',
		},

		['WEAPON_ASSAULTSMG'] = {
			label = 'Assault SMG',
			weight = 2900,
			durability = 0.05,
			ammoname = 'ammo-rifle',
			caliber = '5.56x45mm',
		},

		['WEAPON_FAMAS'] = {
			label = 'FAMAS',
			weight = 3100,
			durability = 0.03,
			ammoname = 'ammo-rifle',
			caliber = '5.56x45mm',
			groups = { ['police'] = 0 },
		},

		['WEAPON_M4A1_MOD'] = {
			label = 'M4A1 MODs',
			weight = 3100,
			durability = 0.03,
			ammoname = 'ammo-rifle',
			caliber = '5.56x45mm',
			description = 'M4A1 Police Issued',
			groups = { ['police'] = 0 },
		},

		['WEAPON_BALL'] = {
			label = 'Ball',
			weight = 149,
			throwable = true,
		},

		['WEAPON_BAT'] = {
			label = 'Bat',
			weight = 1134,
			durability = 0.1,
		},

		['WEAPON_BATTLEAXE'] = {
			label = 'Battle Axe',
			weight = 2500,
			durability = 0.1,
		},

		['WEAPON_BOTTLE'] = {
			label = 'Bottle',
			weight = 350,
			durability = 0.1,
		},

		['WEAPON_BULLPUPRIFLE'] = {
			label = 'Bullpup Rifle',
			weight = 2900,
			durability = 0.03,
			ammoname = 'ammo-rifle',
			caliber = '5.56x45mm',
		},

		['WEAPON_BULLPUPRIFLE_MK2'] = {
			label = 'Bullpup Rifle MK2',
			weight = 2900,
			durability = 0.03,
			ammoname = 'ammo-rifle',
			caliber = '5.56x45mm',
		},

		['WEAPON_BULLPUPSHOTGUN'] = {
			label = 'Bullpup Shotgun',
			weight = 3100,
			durability = 0.2,
			ammoname = 'ammo-shotgun',
			caliber = '12 Gauge',
		},

		['WEAPON_BZGAS'] = {
			label = 'BZ Gas',
			weight = 600,
			throwable = true,
		},

		['WEAPON_CARBINERIFLE'] = {
			label = 'Carbine Rifle',
			weight = 3100,
			durability = 0.03,
			ammoname = 'ammo-rifle',
			caliber = '5.56x45mm',
		},

		['WEAPON_CARBINERIFLE_MK2'] = {
			label = 'Carbine Rifle MK2',
			weight = 3000,
			durability = 0.03,
			ammoname = 'ammo-rifle',
			caliber = '5.56x45mm',
		},

		['WEAPON_CERAMICPISTOL'] = {
			label = 'Ceramic Pistol',
			weight = 800,
			durability = 0.2,
			ammoname = 'ammo-9',
			caliber = '9x19mm',
			buttons = {
				{
					label = 'Rename Weapon',
					action = function (slot)
						RenameItem(slot)
					end
				}
			},
		},

		['WEAPON_PISTOLXM3'] = {
			label = 'WM 29 Pistol',
			weight = 969,
			durability = 0.2,
			ammoname = 'ammo-9',
			caliber = '9x19mm',
			buttons = {
				{
					label = 'Rename Weapon',
					action = function (slot)
						RenameItem(slot)
					end
				}
			},
		},

		['WEAPON_COMBATMG'] = {
			label = 'Combat MG',
			weight = 7500,
			durability = 0.02,
			ammoname = 'ammo-rifle',
			caliber = '5.56x45mm',
		},

		['WEAPON_COMBATMG_MK2'] = {
			label = 'Combat MG MK2',
			weight = 8000,
			durability = 0.02,
			ammoname = 'ammo-rifle2',
			caliber = '5.56x45mm',
		},

		['WEAPON_COMBATPDW'] = {
			label = 'Combat PDW',
			weight = 2300,
			durability = 0.1,
			ammoname = 'ammo-9',
			caliber = '9x19mm',
		},


		['WEAPON_COMBATSHOTGUN'] = {
			label = 'Combat Shotgun',
			weight = 4400,
			durability = 0.2,
			ammoname = 'ammo-shotgun',
			caliber = '12 Gauge',
		},

		['WEAPON_COMPACTLAUNCHER'] = {
			label = 'Compact Grenade Launcher',
			weight = 2500,
			durability = 0.05,
			ammoname = 'ammo-grenade',
			caliber = '40mm',
		},

		['WEAPON_COMPACTRIFLE'] = {
			label = 'Compact Rifle',
			weight = 3600,
			durability = 0.05,
			ammoname = 'ammo-rifle2',
			caliber = '5.56x45mm',
		},

		['WEAPON_CROWBAR'] = {
			label = 'Crowbar',
			weight = 2500,
			durability = 0.1,
		},

		['WEAPON_DAGGER'] = {
			label = 'Dagger',
			weight = 800,
			durability = 0.1,
		},

		['WEAPON_DBSHOTGUN'] = {
			label = 'Double Barrel Shotgun',
			weight = 3175,
			durability = 0.4,
			ammoname = 'ammo-shotgun',
			caliber = '12 Gauge',
		},

		['WEAPON_DOUBLEACTION'] = {
			label = 'Double Action Revolver',
			weight = 940,
			durability = 0.2,
			ammoname = 'ammo-38',
			caliber = '.38 Special',
		},

		['WEAPON_EMPLAUNCHER'] = {
			label = 'Compact EMP Launcher',
			weight = 2750,
			durability = 0.2,
			ammoname = 'ammo-emp',
			caliber = '40mm',
		},

		['WEAPON_FIREEXTINGUISHER'] = {
			label = 'Fire Extinguisher',
			weight = 2500,
			durability = 0.04,
		},

		['WEAPON_FIREWORK'] = {
			label = 'Firework Launcher',
			weight = 1000,
			durability = 0.5,
			ammoname = 'ammo-firework'
		},

		['WEAPON_FLARE'] = {
			label = 'Flare',
			weight = 250,
			throwable = true,
		},

		['WEAPON_FLAREGUN'] = {
			label = 'Flare Gun',
			weight = 1000,
			durability = 0.5,
			ammoname = 'ammo-flare'
		},

		['WEAPON_FLASHLIGHT'] = {
			label = 'Flashlight',
			weight = 125,
			durability = 0.1,
		},

		['WEAPON_GOLFCLUB'] = {
			label = 'Golf Club',
			weight = 330,
			durability = 0.1,
		},

		['WEAPON_GRENADE'] = {
			label = 'Grenade',
			weight = 400,
			throwable = true,
		},

		['WEAPON_GRENADELAUNCHER'] = {
			label = 'Grenade Launcher',
			weight = 6500,
			durability = 0.05,
			ammoname = 'ammo-grenade'
		},

		['WEAPON_GUSENBERG'] = {
			label = 'Gusenberg',
			weight = 4900,
			durability = 0.04,
			ammoname = 'ammo-45'
		},

		['WEAPON_HAMMER'] = {
			label = 'Hammer',
			weight = 1200,
			durability = 0.1,
		},

		['WEAPON_HATCHET'] = {
			label = 'Hatchet',
			weight = 1000,
			durability = 0.1,
		},

		['WEAPON_HEAVYRIFLE'] = {
			label = 'Heavy Rifle',
			weight = 3300,
			durability = 0.2,
			ammoname = 'ammo-rifle',
			caliber = '7.62x51mm',
		},

		['WEAPON_HAZARDCAN'] = {
			label = 'Hazard Can',
			weight = 12000,
		},

		['WEAPON_METALDETECTOR'] = {
			label = 'Metal Detector',
			weight = 1200,
		},

		['WEAPON_HOMINGLAUNCHER'] = {
			label = 'Homing Launcher',
			weight = 10000,
			durability = 0.6,
			ammoname = 'ammo-rocket'
		},

		['WEAPON_FERTILIZERCAN'] = {
			label = 'Fertilizer Can',
			weight = 12000,
		},

		['WEAPON_HEAVYPISTOL'] = {
			label = 'Heavy Pistol',
			weight = 1100,
			durability = 0.2,
			ammoname = 'ammo-45',
			caliber = '.45 ACP',
			groups = { ['police'] = 0 },
			buttons = {
				{
					label = 'Rename Weapon',
					action = function (slot)
						RenameItem(slot)
					end
				}
			},
		},

		['WEAPON_HEAVYSHOTGUN'] = {
			label = 'Heavy Shotgun',
			weight = 3600,
			durability = 0.1,
			ammoname = 'ammo-shotgun',
			caliber = '12 Gauge',
		},

		['WEAPON_HEAVYSNIPER'] = {
			label = 'Heavy Sniper',
			weight = 12700,
			durability = 0.5,
			ammoname = 'ammo-heavysniper',
			caliber = '.50 BMG',
		},

		['WEAPON_HEAVYSNIPER_MK2'] = {
			label = 'Heavy Sniper MK2',
			weight = 14000,
			durability = 0.5,
			ammoname = 'ammo-heavysniper', 
			caliber = '.50 BMG',
		},

		['WEAPON_KNIFE'] = {
			label = 'Knife',
			weight = 300,
			durability = 0.1,
		},

		['WEAPON_KNUCKLE'] = {
			label = 'Knuckle Dusters',
			weight = 300,
			durability = 0.1,
		},

		['WEAPON_MACHETE'] = {
			label = 'Machete',
			weight = 1000,
			durability = 0.1,
		},

		['WEAPON_MACHINEPISTOL'] = {
			label = 'Machine Pistol',
			weight = 1400,
			durability = 0.05,
			ammoname = 'ammo-9',
			caliber = '9x19mm',
			groups = { ['police'] = 0 },
		},

		['WEAPON_MARKSMANPISTOL'] = {
			label = 'Marksman Pistol',
			weight = 1588,
			durability = 0.5,
			ammoname = 'ammo-22',
			caliber = '.22 LR',
		},

		['WEAPON_MARKSMANRIFLE'] = {
			label = 'Marksman Rifle',
			weight = 7500,
			durability = 0.4,
			ammoname = 'ammo-sniper',
			caliber = '7.62x51mm',
		},

		['WEAPON_MARKSMANRIFLE_MK2'] = {
			label = 'Marksman Rifle MK2',
			weight = 4000,
			durability = 0.4,
			ammoname = 'ammo-sniper',
			caliber = '7.62x51mm',
		},

		['WEAPON_MG'] = {
			label = 'Machine Gun',
			weight = 9000,
			durability = 0.02,
			ammoname = 'ammo-rifle2',
			caliber = '7.62x51mm',
		},

		['WEAPON_MINIGUN'] = {
			label = 'Minigun',
			weight = 38500,
			durability = 0.1,
			ammoname = 'ammo-rifle2',
			caliber = '7.62x51mm',
		},

		['WEAPON_MICROSMG'] = {
			label = 'Micro SMG',
			weight = 3000,
			durability = 0.1,
			ammoname = 'ammo-45',
			caliber = '.45 ACP',
		},

		['WEAPON_MILITARYRIFLE'] = {
			label = 'Military Rifle',
			weight = 3600,
			durability = 0.03,
			ammoname = 'ammo-rifle',
			caliber = '5.56x45mm',
		},

		['WEAPON_MINISMG'] = {
			label = 'Mini SMG',
			weight = 1270,
			durability = 0.05,
			ammoname = 'ammo-9',
			caliber = '9x19mm',
		},

		['WEAPON_MOLOTOV'] = {
			label = 'Molotov',
			weight = 1800,
			throwable = true,
		},

		['WEAPON_MUSKET'] = {
			label = 'Musket',
			weight = 4500,
			durability = 0.5,
			ammoname = 'ammo-musket',
			caliber = '.50 Ball',
		},

		['WEAPON_NAVYREVOLVER'] = {
			label = 'Navy Revolver',
			weight = 4000,
			durability = 0.2,
			ammoname = 'ammo-44',
			caliber = '.44 Magnum',
		},

		['WEAPON_NIGHTSTICK'] = {
			label = 'Nightstick',
			weight = 1000,
			durability = 0.1,
			groups = { ['police'] = 0 },
		},

		['WEAPON_PETROLCAN'] = {
			label = 'Jerry Can',
			weight = 4000,
			buttons = {
				{
					label = 'Rename Weapon',
					action = function (slot)
						RenameItem(slot)
					end
				}
			},
		},

		['WEAPON_GADGETPISTOL'] = {
			label = 'Perico Pistol',
			weight = 1750,
			durability = 0.1,
			ammoname = 'ammo-9',
			caliber = '9x19mm',
		},

		['WEAPON_PIPEBOMB'] = {
			label = 'Pipe Bomb',
			weight = 1800,
			throwable = true,
		},

		['WEAPON_PISTOL'] = {
			label = 'Pistol',
			weight = 1130,
			durability = 0.1,
			ammoname = 'ammo-9',
			caliber = '9x19mm',
			buttons = {
				{
					label = 'Rename Weapon',
					action = function (slot)
						RenameItem(slot)
					end
				}
			},
		},

		['WEAPON_PISTOL50'] = {
			label = 'Pistol .50',
			weight = 2000,
			durability = 0.1,
			ammoname = 'ammo-50',
			caliber = '.50 AE',
			buttons = {
				{
					label = 'Rename Weapon',
					action = function (slot)
						RenameItem(slot)
					end
				}
			},
		},

		['WEAPON_PISTOL_MK2'] = {
			label = 'Pistol MK2',
			weight = 1000,
			durability = 0.5,
			ammoname = 'ammo-9',
			caliber = '9x19mm',
			buttons = {
				{
					label = 'Rename Weapon',
					action = function (slot)
						RenameItem(slot)
					end
				}
			},
		},

		['WEAPON_POOLCUE'] = {
			label = 'Pool Cue',
			weight = 146,
			durability = 0.1,
		},

		['WEAPON_CANDYCANE'] = {
			label = 'Candy Cane',
			weight = 85,
			durability = 0.1,
		},

		['WEAPON_PROXMINE'] = {
			label = 'Proximity Mine',
			weight = 2500,
			throwable = true,
		},
		['WEAPON_ACIDPACKAGE'] = {
			label = 'News Paper',
			weight = 85,
			throwable = true,
		},
		['WEAPON_PUMPSHOTGUN'] = {
			label = 'Pump Shotgun',
			weight = 3400,
			durability = 0.1,
			ammoname = 'ammo-shotgun',
			caliber = '12 Gauge',
		},

		['WEAPON_PUMPSHOTGUN_MK2'] = {
			label = 'Bean Bag Shotgun',
			weight = 3200,
			durability = 0.1,
			ammoname = 'ammo-shotgun',
			caliber = '12 Gauge',
		},

		['WEAPON_RAILGUN'] = {
			label = 'Railgun',
			weight = 3570,
			durability = 0.5,
			ammoname = 'ammo-railgun',
			caliber = '7.62x51mm',
		},

		['WEAPON_RAILGUNXM3'] = {
			label = 'Railgun XM3',
			weight = 3570,
			durability = 0.5,
			ammoname = 'ammo-railgun',
			caliber = '7.62x51mm',
		},

		['WEAPON_RAYCARBINE'] = {
			label = 'Unholy Hellbringer',
			weight = 3620,
			durability = 0.2,
			ammoname = 'ammo-laser',
			caliber = '7.62x51mm',
		},

		['WEAPON_RAYPISTOL'] = {
			label = 'Up-n-Atomizer',
			weight = 1540,
			durability = 0.5,
		},

		['WEAPON_FLAMETHROWER'] = {
			label = 'Flamethrower',
			weight = 1540,
			durability = 0.5,
		},

		['WEAPON_REVOLVER'] = {
			label = 'Revolver',
			weight = 2260,
			durability = 0.1,
			ammoname = 'ammo-44',
			caliber = '.44 Magnum',
		},

		['WEAPON_REVOLVER_MK2'] = {
			label = 'Revolver MK2',
			weight = 2600,
			durability = 0.1,
			ammoname = 'ammo-44',
			caliber = '.44 Magnum',
			groups = { ['police'] = 0 },
		},

		['WEAPON_RPG'] = {
			label = 'RPG',
			weight = 5000,
			durability = 0.3,
			ammoname = 'ammo-rocket'
		},

		['WEAPON_SAWNOFFSHOTGUN'] = {
			label = 'Sawn Off Shotgun',
			weight = 2380,
			durability = 0.1,
			ammoname = 'ammo-shotgun',
			caliber = '12 Gauge',
		},

		['WEAPON_SMG'] = {
			label = 'SMG',
			weight = 3084,
			durability = 0.08,
			ammoname = 'ammo-45',
			caliber = '.45 ACP',
		},

		['WEAPON_SMG_MK2'] = {
			label = 'SMG Mk2',
			weight = 2700,
			durability = 0.05,
			ammoname = 'ammo-45',
			caliber = '.45 ACP',
		},

		['WEAPON_SMOKEGRENADE'] = {
			label = 'Smoke Grenade',
			weight = 600,
			throwable = true,
		},

		['WEAPON_SNIPERRIFLE'] = {
			label = 'Sniper Rifle',
			weight = 5000,
			durability = 0.5,
			ammoname = 'ammo-sniper',
			caliber = '7.62x51mm',
		},

		['WEAPON_SNOWBALL'] = {
			label = 'Snow Ball',
			weight = 5,
			throwable = true,
		},

		['WEAPON_SNSPISTOL'] = {
			label = 'SNS Pistol',
			weight = 465,
			durability = 0.1,
			ammoname = 'ammo-9',
			caliber = '9x19mm',
			buttons = {
				{
					label = 'Rename Weapon',
					action = function (slot)
						RenameItem(slot)
					end
				}
			},
		},

		['WEAPON_SNSPISTOL_MK2'] = {
			label = 'SNS Pistol MK2',
			weight = 465,
			durability = 0.1,
			ammoname = 'ammo-45',
			caliber = '.45 ACP',
			buttons = {
				{
					label = 'Rename Weapon',
					action = function (slot)
						RenameItem(slot)
					end
				}
			},
		},

		['WEAPON_SPECIALCARBINE'] = {
			label = 'Special Carbine',
			weight = 3000,
			durability = 0.03,
			ammoname = 'ammo-rifle',
			caliber = '5.56x45mm',
		},

		['WEAPON_SPECIALCARBINE_MK2'] = {
			label = 'Special Carbine MK2',
			weight = 3370,
			durability = 0.03,
			ammoname = 'ammo-rifle',
			caliber = '5.56x45mm',
		},

		['WEAPON_STICKYBOMB'] = {
			label = 'Sticky Bomb',
			weight = 1000,
			throwable = true,
		},

		['WEAPON_STONE_HATCHET'] = {
			label = 'Stone Hatchet',
			weight = 800,
			durability = 0.1,
		},

		['WEAPON_STUNGUN'] = {
			label = 'Tazer',
			weight = 227,
			durability = 0.1,
			ammoname = 'ammo-stun',
			caliber = 'Stun Charge',
		},

		['WEAPON_AUTOSHOTGUN'] = {
			label = 'Sweeper Shotgun',
			weight = 4400,
			durability = 0.05,
			ammoname = 'ammo-shotgun',
			caliber = '12 Gauge',
		},

		['WEAPON_SWITCHBLADE'] = {
			label = 'Switchblade',
			weight = 300,
			durability = 0.1,
			anim = { 'anim@melee@switchblade@holster', 'unholster', 200, 'anim@melee@switchblade@holster', 'holster', 600 },
		},

		['WEAPON_VINTAGEPISTOL'] = {
			label = 'Vintage Pistol',
			weight = 700,
			durability = 0.1,
			ammoname = 'ammo-9',
			caliber = '9x19mm',
			buttons = {
				{
					label = 'Rename Weapon',
					action = function (slot)
						RenameItem(slot)
					end
				}
			},
		},

		['WEAPON_RAYMINIGUN'] = {
			label = 'Widowmaker',
			weight = 7000,
			durability = 0.1,
			ammoname = 'ammo-laser'
		},

		['WEAPON_WRENCH'] = {
			label = 'Wrench',
			weight = 2500,
			durability = 0.1,
		},

		['WEAPON_PRECISIONRIFLE'] = {
			label = 'Precision Rifle',
			weight = 4800,
			durability = 0.4,
			ammoname = 'ammo-sniper',
			caliber = '7.62x51mm',
		},

		['WEAPON_TACTICALRIFLE'] = {
			label = 'Tactical Rifle',
			weight = 3400,
			durability = 0.03,
			ammoname = 'ammo-rifle',
			caliber = '5.56x45mm',
		},

		['WEAPON_TEARGAS'] = {
			label = 'Tear Gas',
			weight = 600,
			throwable = true,
		},

		--knifes cause i want to 

		['WEAPON_KARAMBITKNIFE'] = {
			label = 'karambit',
			weight = 300,
			durability = 0.1,
		},
		['WEAPON_SAFARIBFKNIFE'] = {
			label = 'Safari butterfly knife',
			weight = 300,
			durability = 0.1,
		},
		['WEAPON_SCORCHEDBFKNIFE'] = {
			label = 'Scorched butterfly knife',
			weight = 300,
			durability = 0.1,
		},
		['WEAPON_SLAUGHTERBFKNIFE'] = {
			label = 'Slaughter butterfly knife',
			weight = 300,
			durability = 0.1,
		},
		['WEAPON_STAINEDRBFKNIFE'] = {
			label = 'stained butterfly knife',
			weight = 300,
			durability = 0.1,
		},
		['WEAPON_URBANRBFKNIFE'] = {
			label = 'Urban butterfly knife',
			weight = 300,
			durability = 0.1,
		},
		['WEAPON_HUNTSMANKNIFE'] = {
			label = 'Huntsman knife',
			weight = 300,
			durability = 0.1,
		},
		['WEAPON_GUTKNIFE'] = {
			label = 'Gut knife',
			weight = 300,
			durability = 0.1,
		},
		['WEAPON_PERFORATOR'] = {
			label = 'Perforator',
			weight = 300,  
			durability = 0.1,  
		},
		
		['WEAPON_BAYONETKNIFE'] = {
			label = 'Bayonet Knife',
			weight = 300,  
			durability = 0.1,  
		},
		
		['WEAPON_BLUEBFKNIFE'] = {
			label = 'Blue Butterfly Knife',
			weight = 300,  
			durability = 0.1,  
		},
		
		['WEAPON_BFKNIFE'] = {
			label = 'Butterfly Knife',
			weight = 300,  
			durability = 0.1,  
		},
		
		['WEAPON_CHBFKNIFE'] = {
			label = 'Case Hardened Butterfly Knife',
			weight = 300,  
			durability = 0.1,  
		},
		
		['WEAPON_CRIMSONBFKNIFE'] = {
			label = 'Crimson Butterfly Knife',
			weight = 300,  
			durability = 0.1,  
		},
		
		['WEAPON_FADEBFKNIFE'] = {
			label = 'Fade Butterfly Knife',
			weight = 300,  
			durability = 0.1,  
		},
		
		['WEAPON_FLIPKNIFE'] = {
			label = 'Flip Knife',
			weight = 300,  
			durability = 0.1,  
		},
		
		['WEAPON_FORESTBFKNIFE'] = {
			label = 'Forest Butterfly Knife',
			weight = 300,  
			durability = 0.1,  
		},
		['WEAPON_BASICSILVER'] = {
			label = 'Basic Silver Knife',
			weight = 300,
			durability = 0.1,
		},

		['WEAPON_DARKGREENCAMO'] = {
			label = 'Dark Green Camo Knife',
			weight = 300,
			durability = 0.1,
		},

		['WEAPON_DSWORD'] = {
			label = 'Dragon Sword',
			weight = 300,
			durability = 0.1,
		},

		['WEAPON_GREENCAMO'] = {
			label = 'Green Camo Knife',
			weight = 300,
			durability = 0.1,
		},

		['WEAPON_KATANA2'] = {
			label = 'Katana',
			weight = 300,
			durability = 0.1,
		},

		['WEAPON_PINKFADE'] = {
			label = 'Pink Fade Knife',
			weight = 300,
			durability = 0.1,
		},

		['WEAPON_RAINBOWKITTY'] = {
			label = 'Rainbow Kitty Knife',
			weight = 300,
			durability = 0.1,
		},

		['WEAPON_REDCAMO'] = {
			label = 'Red Camo Knife',
			weight = 300,
			durability = 0.1,
		},

		['WEAPON_WARAXE'] = {
			label = 'War Axe',
			weight = 300,
			durability = 0.1,
		},

		--lightsabers


		['WEAPON_BLUELIGHTSABER'] = {
			label = 'Blue Lightsaber',
			weight = 300,
			durability = 0.1,
		},

		['WEAPON_GREENLIGHTSABER'] = {
			label = 'Green Lightsaber',
			weight = 300,
			durability = 0.1,
		},

		['WEAPON_MAGENTALIGHTSABER'] = {
			label = 'Magenta Lightsaber',
			weight = 300,
			durability = 0.1,
		},

		['WEAPON_ORANGELIGHTSABER'] = {
			label = 'Orange Lightsaber',
			weight = 300,
			durability = 0.1,
		},

		['WEAPON_PURPLELIGHTSABER'] = {
			label = 'Purple Lightsaber',
			weight = 300,
			durability = 0.1,
		},

		['WEAPON_REDLIGHTSABER'] = {
			label = 'Red Lightsaber',
			weight = 300,
			durability = 0.1,
		},

		['WEAPON_TURQOISELIGHTSABER'] = {
			label = 'Turquoise Lightsaber',
			weight = 300,
			durability = 0.1,
		},

		['WEAPON_WHITELIGHTSABER'] = {
			label = 'White Lightsaber',
			weight = 300,
			durability = 0.1,
		},

		['WEAPON_YELLOWLIGHTSABER'] = {
			label = 'Yellow Lightsaber',
			weight = 300,
			durability = 0.1,
		},

		--swords


		['WEAPON_ZAMORAK'] = {
			label = 'Zamorak god sword',
			weight = 300,
			durability = 0.1,
		},
		['WEAPON_ARMADYL'] = {
			label = 'Armadyl god sword',
			weight = 300,
			durability = 0.1,
		},
		['WEAPON_BANDOS'] = {
			label = 'Bandos god sword',
			weight = 300,
			durability = 0.1,
		},
		['WEAPON_SARADOMIN'] = {
			label = 'Aradomin god sword',
			weight = 300,
			durability = 0.1,
		},
		['WEAPON_APPEASE'] = {
			label = 'Appease',
			weight = 300,
			durability = 0.1,
		},
		['WEAPON_KATANA'] = {
			label = 'Appease',
			weight = 300,
			durability = 0.1,
		},

		['WEAPON_BLACKCLIFFSLASHER'] = {
			label = 'Blackcliff Slasher',
			weight = 300,
			durability = 0.1,
		},
		['WEAPON_BLOODTAINTEDGREATSWORD'] = {
			label = 'Bloodtainted Greatsword',
			weight = 300,
			durability = 0.1,
		},
		['WEAPON_DEBATECLUB'] = {
			label = 'Debate Club',
			weight = 300,
			durability = 0.1,
		},
		['WEAPON_FAVONIUSGREATSWORD'] = {
			label = 'Favonius Greatsword',
			weight = 300,
			durability = 0.1,
		},

		['WEAPON_LITHICBLADE'] = {
			label = 'Lithic Blade',
			weight = 300,
			durability = 0.1,
		},
		['WEAPON_MORAX'] = {
			label = 'Morax',
			weight = 300,
			durability = 0.1,
		},
		['WEAPON_OLDMERCSPAL'] = {
			label = 'Old Mercs Pal',
			weight = 300,
			durability = 0.1,
		},
		['WEAPON_PROTOTYPEAMINUS'] = {
			label = 'Prototype Aminus',
			weight = 300,
			durability = 0.1,
		},
		['WEAPON_PROTOTYPEARCAIC'] = {
			label = 'Prototype Archaic',
			weight = 300,
			durability = 0.1,
		},
		['WEAPON_QUARTZ'] = {
			label = 'Quartz',
			weight = 300,
			durability = 0.1,
		},
		['WEAPON_RAINSLASHER'] = {
			label = 'Rainslasher',
			weight = 300,
			durability = 0.1,
		},
		['WEAPON_ROYALGREATSWORD'] = {
			label = 'Royal Greatsword',
			weight = 300,
			durability = 0.1,
		},
		['WEAPON_SACRIFICIALGREATSWORD'] = {
			label = 'Sacrificial Greatsword',
			weight = 300,
			durability = 0.1,
		},
		['WEAPON_SKYRIDERGREATSWORD'] = {
			label = 'Skyrider Greatsword',
			weight = 300,
			durability = 0.1,
		},
		['WEAPON_SKYWARDPRIDE'] = {
			label = 'Skyward Pride',
			weight = 300,
			durability = 0.1,
		},
		['WEAPON_SNOWTOMBEDSTARSILVER'] = {
			label = 'Snow-Tombed Starsilver',
			weight = 300,
			durability = 0.1,
		},
		['WEAPON_THEBELL'] = {
			label = 'The Bell',
			weight = 300,
			durability = 0.1,
		},
		['WEAPON_THEUNFORGED'] = {
			label = 'The Unforged',
			weight = 300,
			durability = 0.1,
		},
		['WEAPON_WASTERGREATSWORD'] = {
			label = 'Waster Greatsword',
			weight = 300,
			durability = 0.1,
		},
		['WEAPON_WHITEBLIND'] = {
			label = 'Whiteblind',
			weight = 300,
			durability = 0.1,
		},
		['WEAPON_WHITEIRONGREATSWORD'] = {
			label = 'White Iron Greatsword',
			weight = 300,
			durability = 0.1,
		},
		['WEAPON_WIDSITH'] = {
			label = 'Widsith',
			weight = 300,
			durability = 0.1, 
		},
		['WEAPON_WOLFSGRAVESTONE'] = { 
			label = 'Wolfs Gravestone',
			weight = 300,
			durability = 0.1,
		},

		--custom ars

		['WEAPON_GLITCHPOPVANDAL'] = {
			label = 'Glitchpop Vandal',
			weight = 300,
			durability = 0.1,
			ammoname = 'ammo-9'
		},
		['WEAPON_REAVERVANDAL'] = {
			label = 'Reaver Vandal',
			weight = 300,
			durability = 0.1,
			ammoname = 'ammo-9'
		},
		['WEAPON_BLASTXPHANTOM'] = {
			label = 'BlastX Phantom',
			weight = 300,
			durability = 0.1,
			ammoname = 'ammo-9'
		},
		['WEAPON_AK47CURSEDANGEL'] = {
			label = 'Cursed Angel AK47',
			weight = 300,
			durability = 0.1,
			ammoname = 'ammo-9'
		},
		['WEAPON_DARKKNIGHTAK'] = {
			label = 'Dark Knight AK',
			weight = 300,
			durability = 0.1,
			ammoname = 'ammo-9'
		},
		['WEAPON_HOTSHOTWELDER'] = {
			label = 'Hotshot Welder',
			weight = 300,
			durability = 0.1,
			ammoname = 'ammo-9'
		},
		['WEAPON_ORIGINVANDAL'] = {
			label = 'Origin Vandal',
			weight = 300,
			durability = 0.1,
			ammoname = 'ammo-9'
		},
		['WEAPON_RIFLEVERSION2'] = {
			label = 'Rifle V2',
			weight = 300,
			durability = 0.1,
			ammoname = 'ammo-9'
		},
		['WEAPON_RIOTM4'] = {
			label = 'Riot M4',
			weight = 300,
			durability = 0.1,
			ammoname = 'ammo-9'
		},
		['WEAPON_SG556GLOW'] = {
			label = 'SG556 Glow',
			weight = 300,
			durability = 0.1,
			ammoname = 'ammo-9'
		},

		--custom deagles
		['WEAPON_BADGEDEAGLE'] = {
			label = 'Badge Deagle',
			weight = 300,
			durability = 0.1,
			ammoname = 'ammo-9'
		},
		['WEAPON_CHROMATICDEAGLE'] = {
			label = 'Chromatic Deagle',
			weight = 300,
			durability = 0.1,
			ammoname = 'ammo-9'
		},
		['WEAPON_DEAGLEGUNGIRL'] = {
			label = 'Luna Girl Deagle',
			weight = 300,
			durability = 0.1,
			ammoname = 'ammo-9'
		},
		['WEAPON_DEAGLEKILLCONFIRMED'] = {
			label = 'Kill Confirmed Deagle',
			weight = 300,
			durability = 0.1,
			ammoname = 'ammo-9'
		},
		['WEAPON_DEVASTATORDEAGLE'] = {
			label = 'Devastator Deagle',
			weight = 300,
			durability = 0.1,
			ammoname = 'ammo-9'
		},

		--custom pistols
		['WEAPON_SLAYERHEAVYPISTOL'] = {
			label = 'Slayer Heavy Pistol',
			weight = 300,
			durability = 0.1,
			ammoname = 'ammo-9'
		},
		['WEAPON_USPSKILLCONFIRMED'] = {
			label = 'USP-S Kill Confirmed',
			weight = 300,
			durability = 0.1,
			ammoname = 'ammo-9'
		},

		--custom smgs

		['WEAPON_BLASTXSPECTRE'] = {
			label = 'Blastx Spectre',
			weight = 300,
			durability = 0.1,
			ammoname = 'ammo-9'
		},
		['WEAPON_HAYMAKERDARKMATTER'] = {
			label = 'Haymaker Dark Matter',
			weight = 300,
			durability = 0.1,
			ammoname = 'ammo-9'
		},
		['WEAPON_NERFBLASTER'] = {
			label = 'Nerf Blaster',
			weight = 300,
			durability = 0.1,
			ammoname = 'ammo-9'
		},
		['WEAPON_VIOLETMP5'] = {
			label = 'Violet MP5',
			weight = 300,
			durability = 0.1,
			ammoname = 'ammo-9'
		},

		--custom uzis

		['WEAPON_K7HYPERBEAST'] = {
			label = 'K7 Hyperbeast',
			weight = 300,
			durability = 0.1,
			ammoname = 'ammo-9'
		},
		['WEAPON_UZILUNA'] = {
			label = 'Uzi Luna',
			weight = 300,
			durability = 0.1,
			ammoname = 'ammo-9'
		},

--------------------------------------------------------------------------------------------------------
-----------------------------------------------------------------------------------------------------------
-----------------------------------------------------------------------------------------------------------
----kyro weapon pack start
---
		['WEAPON_FN57'] = {
            label = 'FN Five-seveN',
            weight = 1500,
            durability = 0.1,
            ammoname = 'ammo-9'
        },

		['WEAPON_T1911'] = {
            label = 'TAN 1911',
            weight = 2000,
            durability = 0.1,
            ammoname = 'ammo-45'
        },

		['WEAPON_GLOCK21'] = {
            label = 'GLOCK 21',
            weight = 2000,
            durability = 0.1,
            ammoname = 'ammo-45'
        },

		['WEAPON_TARP'] = {
            label = 'TAN ARP',
            weight = 4600,
            durability = 0.1,
            ammoname = 'ammo-rifle'
        },

		['WEAPON_LBTARP'] = {
            label = 'LB TAN ARP',
            weight = 5500,
            durability = 0.1,
            ammoname = 'ammo-rifle2'
        },

		['WEAPON_WOARP'] = {
            label = 'WHITE-OUT ARP',
            weight = 4000,
            durability = 0.1,
            ammoname = 'ammo-rifle'
        },

		['WEAPON_SR40'] = {
            label = 'RUGER SR40',
            weight = 1400,
            durability = 0.1,
            ammoname = 'ammo-9'
        },

		['WEAPON_FM1_GLOCK19'] = {
            label = 'GLOCK 19',
            weight = 2000,
            durability = 0.1,
            ammoname = 'ammo-9'
        },

		['WEAPON_DMK18'] = {
            label = 'DESERET MK18',
            weight = 6000,
            durability = 0.1,
            ammoname = 'ammo-rifle',
			caliber = '5.56x45mm',
        },

		['WEAPON_BLACKARP'] = {
            label = 'BLACK ARP',
            weight = 5500,
            durability = 0.1,
            ammoname = 'ammo-rifle'
        },

		['WEAPON_REDM4A1'] = {
            label = 'RED DRAG M4A1',
            weight = 6200,
            durability = 0.1,
            ammoname = 'ammo-rifle'
        },

		['WEAPON_BLUEGLOCKS'] = {
            label = 'BLUE GLOCK SWITCH',
            weight = 2000,
            durability = 0.1,
            ammoname = 'ammo-9'
        },

		['WEAPON_COMBATPISTOL'] = {
			label = 'Combat Pistol',
			weight = 785,
			durability = 0.2,
			ammoname = 'ammo-9',
			caliber = '9x19mm',
			buttons = {
				{
					label = 'Rename Weapon',
					action = function (slot)
						RenameItem(slot)
					end
				},
				{
					label = 'Add Kit',
					action = function (slot)	
						lib.progressBar({
							duration = 3000, -- Duration of the progress bar (3 seconds)
							label = 'Adding Weapons Kit...',
							useWhileDead = false,
							canCancel = false,
							disable = {
								car = true, -- Disable while in a vehicle
								move = false, -- Disable movement
							},
						})		
						-- Trigger the server event to combine parts
						TriggerServerEvent('combineTwoParts:server', 'WEAPON_COMBATPISTOL', 1, 'glock_kit', 1, 'WEAPON_ILLGLOCK17', 1)
					end
				}
			},
		},
		['WEAPON_ILLGLOCK17'] = { 
			label = 'ILLEGAL GLOCK 17',
			weight = 2000,
			durability = 0.1,
			ammoname = 'ammo-9',
			caliber = '9x19mm',
			buttons = {
				{
					label = 'Rename Weapon',
					action = function (slot)
						RenameItem(slot)
					end
				},
				{
					label = 'Add Kit',
					action = function (slot)	
						lib.progressBar({
							duration = 3000, -- Duration of the progress bar (3 seconds)
							label = 'Adding Weapons Kit...',
							useWhileDead = false,
							canCancel = false,
							disable = {
								car = true, -- Disable while in a vehicle
								move = false, -- Disable movement
							},
						})		
						-- Trigger the server event to combine parts
						TriggerServerEvent('combineTwoParts:server', 'WEAPON_ILLGLOCK17', 1, 'gold_switch', 1, 'WEAPON_MIDASGLOCK', 1)
	
					end
				},
				{
					label = 'Remove Kit',
					action = function (slot)
						-- Show progress bar
						lib.progressBar({
							duration = 3000, -- Duration of the progress bar (3 seconds)
							label = 'Removing Weapons Kit...',
							useWhileDead = false,
							canCancel = false,
							disable = {
								car = true, -- Disable while in a vehicle
								move = false, -- Disable movement
							},
						})
						TriggerServerEvent('uncombineTwoParts:server', 'WEAPON_ILLGLOCK17', 'WEAPON_COMBATPISTOL', 'glock_kit', 1)
					end
				},
			},
		},
		['WEAPON_MIDASGLOCK'] = {
            label = 'MIDAS GLOCK',
            weight = 2000,
            durability = 0.2,
            ammoname = 'ammo-9',
			caliber = '9x19mm',
			buttons = {
				{
					label = 'Rename Weapon',
					action = function (slot)
						RenameItem(slot)
					end
				},
				{
					label = 'Add Kit',
					action = function (slot)	
						lib.progressBar({
							duration = 3000, -- Duration of the progress bar (3 seconds)
							label = 'Adding Weapons Kit...',
							useWhileDead = false,
							canCancel = false,
							disable = {
								car = true, -- Disable while in a vehicle
								move = false, -- Disable movement
							},
						})		
						-- Trigger the server event to combine parts
						TriggerServerEvent('combineTwoParts:server', 'WEAPON_MIDASGLOCK', 1, 'beam_kit', 1, 'WEAPON_GLOCKBEAMS', 1)
	
					end
				},
				{
					label = 'Remove Kit',
					action = function (slot)
						-- Show progress bar
						lib.progressBar({
							duration = 3000, -- Duration of the progress bar (3 seconds)
							label = 'Removing Weapons Kit...',
							useWhileDead = false,
							canCancel = false,
							disable = {
								car = true, -- Disable while in a vehicle
								move = false, -- Disable movement
							},
						})
						TriggerServerEvent('uncombineTwoParts:server', 'WEAPON_MIDASGLOCK', 'WEAPON_ILLGLOCK17', 'gold_switch', 1)
					end
				}
			},
		},

		['WEAPON_GLOCKBEAMS'] = {
			label = 'GLOCK BEAM SWITCH',
			weight = 2000,
			durability = 0.2,
			ammoname = 'ammo-9',
			caliber = '9x19mm',
			buttons = {
				{
					label = 'Rename Weapon',
					action = function (slot)
						RenameItem(slot)
					end
				},
				{
					label = 'Remove Kit',
					action = function (slot)
						-- Show progress bar
						lib.progressBar({
							duration = 3000, -- Duration of the progress bar (3 seconds)
							label = 'Removing Weapons Kit...',
							useWhileDead = false,
							canCancel = false,
							disable = {
								car = true, -- Disable while in a vehicle
								move = false, -- Disable movement
							},
						})
						TriggerServerEvent('uncombineTwoParts:server', 'WEAPON_GLOCKBEAMS', 'WEAPON_MIDASGLOCK', 'beam_kit', 1)
					end
				}
			},
		},

		['WEAPON_MGGLOCK'] = {
            label = 'MG GLOCK',
            weight = 2000,
            durability = 0.1,
            ammoname = 'ammo-9'
        },

		['WEAPON_TGLOCK19'] = {
            label = 'TAN G19',
            weight = 2100,
            durability = 0.1,
            ammoname = 'ammo-9',
			groups = { ['police'] = 0 },
        },


		['WEAPON_TEC9S'] = {
            label = 'TEC 9 W STRAP',
            weight = 4500,
            durability = 0.2,
            ammoname = 'ammo-9'
        },

		['WEAPON_CHAIR'] = {
			label = 'BRAWL CHAIR',
			weight = 3000,
			durability = 0.3,
		},

		['WEAPON_REDARP'] = {
            label = 'RED DRAG ARP',
            weight = 4500,
            durability = 0.1,
            ammoname = 'ammo-rifle'
        },

		['WEAPON_P30L'] = {
            label = 'H&K P30L',
            weight = 1900,
            durability = 0.1,
            ammoname = 'ammo-9'
        },

		['WEAPON_GLOCK41'] = {
            label = 'GLOCK 41',
            weight = 1700,
            durability = 0.1,
            ammoname = 'ammo-45',
			caliber = '9x19mm',
        },

		['WEAPON_THOMPSON'] = {
            label = 'BLACK THOMPSON',
            weight = 6000,
            durability = 0.1,
            ammoname = 'ammo-45'
        },

		['WEAPON_RAM7'] = {
            label = 'RAM-7',
            weight = 5200,
            durability = 0.1,
            ammoname = 'ammo-rifle'
        },

		['WEAPON_M500'] = {
            label = 'MOSSBERG 500',
            weight = 4500,
            durability = 0.1,
            ammoname = 'ammo-shotgun'
        },

		['WEAPON_R590'] = {
            label = 'REMINGTON 590',
            weight = 3100,
            durability = 0.1,
            ammoname = 'ammo-shotgun'
        },

		['WEAPON_BAR15'] = {
            label = 'AR-15',
            weight = 5500,
            durability = 0.1,
            ammoname = 'ammo-rifle'
        },

		['WEAPON_BSCAR'] = {
            label = 'BLACK SCAR',
            weight = 5500,
            durability = 0.1,
            ammoname = 'ammo-rifle2'
        },

		['WEAPON_AXE'] = {
			label = 'AXE',
			weight = 2000,
			durability = 0.1,
		},

		['WEAPON_P210'] = {
            label = 'P210 CARRY',
            weight = 1800,
            durability = 0.1,
            ammoname = 'ammo-9'
        },

		['WEAPON_KRISSVECTOR'] = {
            label = 'KRISS VECTOR',
            weight = 3900,
            durability = 0.1,
            ammoname = 'ammo-9',
			groups = { ['police'] = 0 },
        },
		
		['WEAPON_FM1_M9A3'] = {
			label = 'FM1 M9A3',
			weight = 2000,
			durability = 0.1,
			ammoname = 'ammo-9'
		},
		
		['WEAPON_FM1_CZ75'] = {
			label = 'FM1 CZ75',
			weight = 2000,
			durability = 0.1,
			ammoname = 'ammo-9'
		},
		
		['WEAPON_FM1_P226'] = {
			label = 'FM1 P226',
			weight = 2000,
			durability = 0.1,
			ammoname = 'ammo-9',
			caliber = '9x19mm',
		},
		
		['WEAPON_FM1_P320'] = {
			label = 'FM1 P320',
			weight = 2000,
			durability = 0.1,
			ammoname = 'ammo-9'
		},
		['WEAPON_PEPPERSPRAY'] = {
			label = 'PEPPERSPRAY',
			weight = 1000,
			durability = 0.1,
			groups = { ['police'] = 0 },

		},
		['WEAPON_ANTIDOTE'] = {
			label = 'Pepperspray Antitdote',
			weight = 1000,
			durability = 0.1,
		},
		['WEAPON_STUNGRENADE'] = {
			label = 'Stun Grenade',
			weight = 1000,
			throwable = true
		},
		['WEAPON_HUNTINGRIFLE'] = {
			label = 'Hunting Rifle',
			weight = 5000,
			durability = 0.5,
			ammoname = 'gg_hunting_rifleammo'
		},	
		['WEAPON_CROSSBOW'] = {
			label = 'Hunting Crossbow',
			weight = 5000,
			durability = 0.5,
			ammoname = 'gg_hunting_arrowammo'
		},

	----hobo weapons dumpster v2

	['WEAPON_HOBO_TOILET'] = {
		label = 'Toilet Seat',
		weight = 1000,
		durability = 0.1,
	},
	
	['WEAPON_HOBO_STICK'] = {
		label = 'Hobo Stick',
		weight = 800,
		durability = 0.1,
	},
	
	['WEAPON_HOBO_SPONGE'] = {
		label = 'Wet Sponge',
		weight = 300,
		throwable = true,
	},
	
	['WEAPON_HOBO_MOP'] = {
		label = 'Mop',
		weight = 900,
		durability = 0.1,
	},
	
	['WEAPON_HOBO_SHARD'] = {
		label = 'Broken Glass',
		weight = 400,
		durability = 0.1,
	},
	
	['WEAPON_HOBO_REBAR'] = {
		label = 'Rebar',
		weight = 1200,
		durability = 0.1,
	},
	
	['WEAPON_HOBO_PLANK'] = {
		label = 'Wooden Plank',
		weight = 1000,
		durability = 0.1,
	},
	
	['WEAPON_HOBO_OLDMACHETE'] = {
		label = 'Old Machete',
		weight = 1100,
		durability = 0.1,
	},
	
	['WEAPON_HOBO_DUSTERS'] = {
		label = 'Bolt Knuckles',
		weight = 500,
		durability = 0.1,
	},
	
	['WEAPON_HOBO_BRICK'] = {
		label = 'Brick',
		weight = 1000,
		throwable = true,
	},
	
	['WEAPON_HOBO_RATSTICK'] = {
		label = 'Rat Stick',
		weight = 700,
		durability = 0.1,
	},
	
	['WEAPON_HOBO_PIPE'] = {
		label = 'Pipe',
		weight = 1100,
		durability = 0.1,
	},
	
	['WEAPON_HOBO_DIRTYNEEDLE'] = {
		label = 'Dirty Needle',
		weight = 200,
		durability = 0.1,
	},
},

--handgunpack

--start to comps

	Components = {
		['at_flashlight'] = {
			label = 'Tactical Flashlight',
			weight = 120,
			type = 'flashlight',
			client = {
				component = {
					`COMPONENT_AT_AR_FLSH`,
					`COMPONENT_AT_AR_FLSH_REH`,
					`COMPONENT_AT_PI_FLSH`,
					`COMPONENT_AT_PI_FLSH_02`,
					`COMPONENT_AT_PI_FLSH_03`,
				    `COMPONENT_AT_PI_PDG19G4_FLSH`, -- pd
					`COMPONENT_AT_AR_PDHK417_FLSH`,	-- pd
					`COMPONENT_AT_PI_PINKGLOCK19FLSH`, -- female
                    `COMPONENT_AT_G18C_FLSH`, -- v1
                    `COMPONENT_AT_AR_NSR_FLSH`, -- v1
					`COMPONENT_AT_AR_HERAARMS_FLSH`, -- v2
                    `COMPONENT_AT_PI_G2C_FLSH`, -- v3
                    `COMPONENT_AT_PI_PMR_FLSH`, -- v3
                    `COMPONENT_AT_PI_G19X_FLSH`, -- v3
                    `COMPONENT_AT_PI_G26_FLSH`, -- v3
					`COMPONENT_AT_AR_BARPFLSH`, -- v4
					`COMPONENT_AT_PI_RUGER57FLSH`, -- v4
					`COMPONENT_AT_AR_BAR15_FLSH`, -- v5
					`COMPONENT_AT_AR_DMK18_FLSH`, -- v5
					`COMPONENT_AT_PI_GLOCKBEAMS_FLSH`, -- v5 check
					`COMPONENT_AT_PI_FLSH`, -- v5 check
				},
				usetime = 2500
			}
		},

		['at_suppressor_light'] = {
			label = 'Suppressor',
			weight = 280,
			type = 'muzzle',
			client = {
				component = {
					`COMPONENT_AT_PI_SUPP`,
					`COMPONENT_AT_PI_SUPP_02`,
					`COMPONENT_CERAMICPISTOL_SUPP`,
					`COMPONENT_PISTOLXM3_SUPP`,
					`COMPONENT_AT_PI_PDG19G4_SUPP`, -- pd
					`COMPONENT_AT_AR_PINKPM9SUPP_02`, -- feamle
					`COMPONENT_AT_AR_PINKMK18_SUPP`, -- female
					`COMPONENT_AT_AR_NSR_SUPP`, -- v1
					`COMPONENT_AT_AR_HERAARMS_SUPP`, -- v2
					`COMPONENT_AT_AR_MAXIM9_SUPP`, -- v2
					`COMPONENT_AT_AR_HONEYBADGER_SUPP`, -- v2
					`COMPONENT_AT_AR_MP9_SUPP`, -- v2
					`COMPONENT_AT_PI_FN502V2_SUPP`, -- v2
					`COMPONENT_AT_AR_P416_SUPP`, -- v3
					`COMPONENT_AT_SR_P90_SUPP`, -- v3
					`COMPONENT_AT_AR_DMK18_SUPP`, -- v5
					`COMPONENT_AT_AR_REDM4A1_SUPP`, -- v5
				},
				usetime = 2500
			}
		},

		['at_suppressor_heavy'] = {
			label = 'Tactical Suppressor',
			weight = 280,
			type = 'muzzle',
			client = {
                image = 'rifle_suppressor.png',
				component = {
					`COMPONENT_AT_AR_SUPP`,
					`COMPONENT_AT_AR_SUPP_02`,
					`COMPONENT_AT_SR_SUPP`,
					`COMPONENT_AT_SR_SUPP_03`,
					`COMPONENT_w_at_famas_supp`,	
					`w_at_famas_supp`,
					`w_at_m4a1_mod_supp`,
				},
				usetime = 2500
			}
		},

		['at_grip'] = {
			label = 'Grip',
			type = 'grip',
			weight = 280,
			client = {
				component = {
					`COMPONENT_AT_AR_AFGRIP`,
					`COMPONENT_AT_AR_AFGRIP_02`,
					`COMPONENT_AT_AR_PINKMK18_AFGRIP`, -- female
					`COMPONENT_AT_AR_HERAARMS_AFGRIP`, -- v2
					`COMPONENT_AT_AR_LVOCA_AFGRIP`, -- v2
					`COMPONENT_AT_AR_LOKAFGRIP`, -- v4
					`COMPONENT_AT_AR_PLRAFGRIP`, -- v4
					`COMPONENT_AT_AR_BAR15_AFGRIP`, -- v5
					`COMPONENT_AT_AR_DMK18_AFGRIP`, -- v5
					`w_atm4a1_mod_afgrip`,
				},
				usetime = 2500
			}
		},

		['at_barrel'] = {
			label = 'Heavy Barrel',
			type = 'barrel',
			weight = 280,
			client = {
				component = {
					`COMPONENT_AT_AR_BARREL_02`,
					`COMPONENT_AT_BP_BARREL_02`,
					`COMPONENT_AT_CR_BARREL_02`,
					`COMPONENT_AT_MG_BARREL_02`,
					`COMPONENT_AT_MRFL_BARREL_02`,
					`COMPONENT_AT_SB_BARREL_02`,
					`COMPONENT_AT_SC_BARREL_02`,
					`COMPONENT_AT_SR_BARREL_02`,
				},
				usetime = 2500
			}
		},

		['at_clip_extended_pistol'] = {
			label = 'Extended Pistol Clip',
			type = 'magazine',
			weight = 280,
			client = {
				component = {
					`COMPONENT_APPISTOL_CLIP_02`,
					`COMPONENT_CERAMICPISTOL_CLIP_02`,
					`COMPONENT_COMBATPISTOL_CLIP_02`,
					`COMPONENT_HEAVYPISTOL_CLIP_02`,
					`COMPONENT_PISTOL_CLIP_02`,
					`COMPONENT_PISTOL_MK2_CLIP_02`,
					`COMPONENT_PISTOL50_CLIP_02`,
					`COMPONENT_SNSPISTOL_CLIP_02`,
					`COMPONENT_SNSPISTOL_MK2_CLIP_02`,
					`COMPONENT_VINTAGEPISTOL_CLIP_02`,
                    `COMPONENT_TECPISTOL_CLIP_02`,
  					`COMPONENT_PINKGLOCK19_CLIP_02`, -- female
					`COMPONENT_PXDS9_CLIP_02`, -- female
                    `COMPONENT_P226_CLIP_02`, -- v1
                    `COMPONENT_G18C_CLIP_02`, -- v1
                    `COMPONENT_G17_CLIP_02`, -- v1
                    `COMPONENT_GARDONE_CLIP_02`, -- v1
					`COMPONENT_CJ_CLIP_02`, -- v2
					`COMPONENT_M45A1V2_CLIP_02`, -- v2
					`COMPONENT_B93R_CLIP_02`, -- v2
					`COMPONENT_MAKAROV_CLIP_02`, -- v2
					`COMPONENT_659_CLIP_02`, -- v2
                    `COMPONENT_G2C_CLIP_02`, -- v3
                    `COMPONENT_TGLOCK_CLIP_02`, -- v3
                    `COMPONENT_G26_CLIP_02`, -- v3
					`COMPONENT_ARPISTOL_BOXMAG`, -- v4
					`COMPONENT_GLOCK17_CLIP_02`, -- v4
					`COMPONENT_GLOCK18C_CLIP_02`, -- v4
					`COMPONENT_PDG19G4_CLIP_02`, -- pd
					`COMPONENT_BLUEGLOCKS_CLIP_02`, -- v5
					`COMPONENT_GLOCK41_CLIP_02`, -- v5
					`COMPONENT_GLOCKBEAMS_CLIP_02`, -- v5
					`COMPONENT_ILLGLOCK17_CLIP_02`, -- v5
					`COMPONENT_MGGLOCK_CLIP_02`, -- v5
					`COMPONENT_MIDASGLOCK_CLIP_02`, -- v5
					`COMPONENT_TGLOCK19_CLIP_02`, -- v5
				},
				usetime = 2500
			}
		},

		['at_clip_extended_smg'] = {
			label = 'Extended SMG Clip',
			type = 'magazine',
			weight = 280,
			client = {
				component = {
					`COMPONENT_ASSAULTSMG_CLIP_02`,
					`COMPONENT_COMBATPDW_CLIP_02`,
					`COMPONENT_MACHINEPISTOL_CLIP_02`,
					`COMPONENT_MICROSMG_CLIP_02`,
					`COMPONENT_MINISMG_CLIP_02`,
					`COMPONENT_SMG_CLIP_02`,
					`COMPONENT_SMG_MK2_CLIP_02`,
					`COMPONENT_PINKMP9_CLIP_02`, -- female
					`COMPONENT_MAC_CLIP_02`, -- v1
					`COMPONENT_TEC9_CLIP_02`, -- v1
					`CCOMPONENT_MAGPULPDR_CLIP_02`, -- v2
					`COMPONENT_MP9_CLIP_02`, -- v2
					`COMPONENT_TUZI_CLIP_02`, -- v3
					`COMPONENT_TEC9S_CLIP_02`, -- v5
				},
				usetime = 2500
			}
		},

		['at_clip_extended_shotgun'] = {
			label = 'Extended Shotgun Clip',
			type = 'magazine',
			weight = 280,
			client = {
				component = {
					`COMPONENT_ASSAULTSHOTGUN_CLIP_02`,
					`COMPONENT_HEAVYSHOTGUN_CLIP_02`,
					`COMPONENT_AA12_CLIP_02`, -- v2
				},
				usetime = 2500
			}
		},

		['at_clip_extended_rifle'] = {
			label = 'Extended Rifle Clip',
			type = 'magazine',
			weight = 280,
			client = {
				component = {
					`COMPONENT_ADVANCEDRIFLE_CLIP_02`,
					`COMPONENT_ASSAULTRIFLE_CLIP_02`,
					`COMPONENT_ASSAULTRIFLE_MK2_CLIP_02`,
					`COMPONENT_BULLPUPRIFLE_CLIP_02`,
					`COMPONENT_BULLPUPRIFLE_MK2_CLIP_02`,
					`COMPONENT_CARBINERIFLE_CLIP_02`,
					`COMPONENT_CARBINERIFLE_MK2_CLIP_02`,
					`COMPONENT_COMPACTRIFLE_CLIP_02`,
					`COMPONENT_HEAVYRIFLE_CLIP_02`,
					`COMPONENT_MILITARYRIFLE_CLIP_02`,
					`COMPONENT_SPECIALCARBINE_CLIP_02`,
					`COMPONENT_SPECIALCARBINE_MK2_CLIP_02`,
					`COMPONENT_TACTICALRIFLE_CLIP_02`,
					`COMPONENT_CARBINERIFLE_BOXMAG`,
					`COMPONENT_PDHK417_CLIP_02`, -- pd
					`COMPONENT_PINKSCAR_CLIP_02`, -- female 
					`COMPONENT_ACE_CLIP_02`, -- v2
					`COMPONENT_HERAARMS_BOXMAG`, -- v2
					`COMPONENT_G36K_CLIP_02`, -- v2
                    `COMPONENT_DDM4V7_CLIP_02`, -- v3
                    `COMPONENT_SCARV3_CLIP_02`, -- v3
                    `COMPONENT_AUGA1_CLIP_02`, -- v3
					`COMPONENT_BARP_CLIP_02`, -- v4
					`COMPONENT_PLR_CLIP_02`, -- v4   
					`COMPONENT_BSCAR_CLIP_02`, -- v5
					`COMPONENT_LBTANARP_CLIP_02`, -- v5
					`COMPONENT_RAM7_CLIP_02`, -- v5
					`COMPONENT_REDARP_CLIP_02`, -- v5
					`COMPONENT_REDM4A1_CLIP_02`, -- v5
					`COMPONENT_BLACKARP_CLIP_02`, -- v5
					`COMPONENT_TANARP_CLIP_02`, -- v5
					`COMPONENT_WOARP_CLIP_02`, -- v5
					`w_ar_m4a1_mod_mag2`,
				},
				usetime = 2500
			}
		},

		['at_clip_extended_mg'] = {
			label = 'Extended MG Clip',
			type = 'magazine',
			weight = 280,
			client = {
                image = 'at_clip_drum.png',
				component = {
					`COMPONENT_GUSENBERG_CLIP_02`,
					`COMPONENT_MG_CLIP_02`,
					`COMPONENT_COMBATMG_CLIP_02`,
					`COMPONENT_COMBATMG_MK2_CLIP_02`,
				},
				usetime = 2500
			}
		},

		['at_clip_extended_sniper'] = {
			label = 'Extended Sniper Clip',
			type = 'magazine',
			weight = 280,
			client = {
                image = 'at_clip_extended2.png',
				component = {
					`COMPONENT_HEAVYSNIPER_MK2_CLIP_02`,
					`COMPONENT_MARKSMANRIFLE_CLIP_02`,
					`COMPONENT_MARKSMANRIFLE_MK2_CLIP_02`,
				},
				usetime = 2500
			}
		},

		['at_clip_drum_smg'] = {
			label = 'SMG Drum',
			type = 'magazine',
			weight = 280,
			client = {
                image = 'at_clip_drum.png',
				component = {
					`COMPONENT_COMBATPDW_CLIP_03`,
					`COMPONENT_MACHINEPISTOL_CLIP_03`,
					`COMPONENT_SMG_CLIP_03`,
				},
				usetime = 2500
			}
		},

		['at_clip_drum_shotgun'] = {
			label = 'Shotgun Drum',
			type = 'magazine',
			weight = 280,
			client = {
                image = 'at_clip_drum.png',
				component = {
					`COMPONENT_HEAVYSHOTGUN_CLIP_03`
				},
				usetime = 2500
			}
		},

		['at_clip_drum_rifle'] = {
			label = 'Rifle Drum',
			type = 'magazine',
			weight = 280,
			client = {
				component = {
					`COMPONENT_ASSAULTRIFLE_CLIP_03`,
					`COMPONENT_COMPACTRIFLE_CLIP_03`,
					`COMPONENT_CARBINERIFLE_CLIP_03`,
					`COMPONENT_SPECIALCARBINE_CLIP_03`,
					`COMPONENT_TANARP_CLIP_03`, -- v5
					`COMPONENT_LBTANARP_CLIP_03`, -- v5
					`COMPONENT_WOARP_CLIP_03`, -- v5
					`COMPONENT_REDARP_CLIP_03`, -- v5
					`COMPONENT_THOMPSON_CLIP_02` -- v5
				},
				usetime = 2500
			}
		},

		['at_compensator'] = {
			label = 'Compensator',
			type = 'muzzle',
			weight = 280,
			client = {
				component = {
					`COMPONENT_AT_PI_COMP`,
					`COMPONENT_AT_PI_COMP_02`,
					`COMPONENT_AT_PI_COMP_03`
				},
				usetime = 2500
			}
		},

		['at_scope_macro'] = {
			label = 'Macro Scope',
			type = 'sight',
			weight = 280,
			client = {
				component = {
					`COMPONENT_AT_SCOPE_MACRO`,
					`COMPONENT_AT_SCOPE_MACRO_02`,
					`COMPONENT_AT_SCOPE_MACRO_MK2`,
					`COMPONENT_AT_SCOPE_MACRO_02_MK2`,
					`COMPONENT_AT_SCOPE_MACRO_02_SMG_MK2`
				},
				usetime = 2500
			}
		},

		['at_scope_small'] = {
			label = 'Small Scope',
			type = 'sight',
			weight = 280,
			client = {
				component = {
					`COMPONENT_AT_SCOPE_SMALL`,
					`COMPONENT_AT_SCOPE_SMALL_02`,
					`COMPONENT_AT_SCOPE_SMALL_MK2`,
					`COMPONENT_AT_SCOPE_SMALL_SMG_MK2`,
					`COMPONENT_AT_PDG19G4_SCOPE_SMALL`, -- pd
					`COMPONENT_W_AT_FAMAS_SCOPE`, -- v5
					`w_at_famas_scope`,
					`w_at_m4a1_mod_scope_small`,
				},
				usetime = 2500
			}
		},

		['at_scope_medium'] = {
			label = 'Medium Scope',
			type = 'sight',
			weight = 280,
			client = {
				component = {
					`COMPONENT_AT_SCOPE_MEDIUM`,
					`COMPONENT_AT_SCOPE_MEDIUM_MK2`,
					`COMPONENT_AT_PINKMK18_SCOPE_MEDIUM`, -- female
					`COMPONENT_AT_PDHK417_SCOPE_MEDIUM`, -- pd
					`COMPONENT_AT_SCOPE_HERAARMS_MEDIUM`, -- v2
					`COMPONENT_AT_MP9_SCOPE_MACRO`, -- v2
					`COMPONENT_AT_ARPISTOLSCOPE_MEDIUM`, -- v4
					`COMPONENT_AT_BAR15_SCOPE_MEDIUM`, -- v5
					`COMPONENT_AT_DMK18_SCOPE_MEDIUM`, -- v5
				},
				usetime = 2500
			}
		},

        ['at_scope_large'] = {
			label = 'Large Scope',
			type = 'sight',
			weight = 280,
			client = {
				component = {
					`COMPONENT_AT_SCOPE_LARGE_MK2`,
					`COMPONENT_AT_SCOPE_LARGE`,
					`COMPONENT_AT_BARRETT50_SCOPE_MAX`, -- v1
					`COMPONENT_AT_AWP_SCOPE_LARGE`, -- v3
					`COMPONENT_AT_M200_SCOPE_LARGE`, -- v3
					`COMPONENT_AT_AS50_SCOPE_LARGE`, -- v3
				},
				usetime = 2500
			}
		},

		['at_clip_drum_pistol'] = {
			label = '50 Round Drum',
			type = 'magazine',
			weight = 500,
			client = {
				component = {
					`COMPONENT_GLOCK19X_CLIP_03`, -- v4
					`COMPONENT_GLOCK19_CLIP_03`, -- v4
					`COMPONENT_GLOCK40_CLIP_03`, -- v4
					`COMPONENT_GLOCK40S_CLIP_03`, -- v4
					`COMPONENT_GLOCK18C_CLIP_03`, -- v4
					`COMPONENT_ILLGLOCK17_CLIP_03`, -- v5
					`COMPONENT_BLUEGLOCKS_CLIP_03`, -- v5
					`COMPONENT_GLOCKBEAMS_CLIP_03`, -- v5
					`COMPONENT_MGGLOCK_CLIP_03`, -- v5
					`COMPONENT_TGLOCK19_CLIP_03`, -- v5
					`COMPONENT_MIDASGLOCK_CLIP_03`, -- v5
				},
				usetime = 2500
			}
		},

		['at_clip_100_pistol'] = {
			label = '100 Round Mag',
			type = 'magazine',
			weight = 800,
			client = {
				component = {
					`COMPONENT_GLOCK19X_CLIP_04`, -- v4
					`COMPONENT_GLOCK40_CLIP_04`, -- v4
					`COMPONENT_GLOCK40S_CLIP_04`, -- v4
					`COMPONENT_ILLGLOCK17_CLIP_05`, -- v5
					`COMPONENT_BLUEGLOCKS_CLIP_04`, -- v5
					`COMPONENT_GLOCKBEAMS_CLIP_04`, -- v5
					`COMPONENT_MGGLOCK_CLIP_04`, -- v5
					`COMPONENT_TGLOCK19_CLIP_04`, -- v5
					`COMPONENT_MIDASGLOCK_CLIP_04`, -- v5
				},
				usetime = 5500
			}
		},

		['at_clip_clear'] = {
			label = 'Clear Clip',
			type = 'magazine',
			weight = 280,
			client = {
				component = {
					`COMPONENT_TANARP_CLIP_04`, -- rifle
					`COMPONENT_LBTANARP_CLIP_04`, -- rifle
					`COMPONENT_WOARP_CLIP_04`, -- rifle
					`COMPONENT_ILLGLOCK17_CLIP_04`, -- pistol
					`COMPONENT_BLUEGLOCKS_CLIP_05`, -- pistol
					`COMPONENT_GLOCKBEAMS_CLIP_05`, -- pistol
					`COMPONENT_MGGLOCK_CLIP_05`, -- pistol
					`COMPONENT_TGLOCK19_CLIP_05`, -- pistol
					`COMPONENT_MIDASGLOCK_CLIP_05`, -- pistol
					`COMPONENT_REDARP_CLIP_04`, -- rifle
				},
				usetime = 2500
			}
		},
		
		['at_scope_advanced'] = {
			label = 'Advanced Scope',
			type = 'sight',
			weight = 280,
			client = {
				component = {
					`COMPONENT_AT_SCOPE_MAX`
				},
				usetime = 2500
			}
		},

		['at_scope_nv'] = {
			label = 'NV Scope',
			type = 'sight',
			weight = 420,
			client = {
				component = {
					`COMPONENT_AT_SCOPE_NV`
				},
				usetime = 2500
			}
		},

		['at_scope_thermal'] = {
			label = 'Thermal Scope',
			type = 'sight',
			weight = 420,
			client = {
				component = {
					`COMPONENT_AT_SCOPE_THERMAL`
				},
				usetime = 2500
			}
		},

		['at_scope_holo'] = {
			label = 'Holographic Sight',
			type = 'sight',
			weight = 280,
			client = {
				component = {
					`COMPONENT_AT_PI_RAIL`,
					`COMPONENT_AT_PI_RAIL_02`,
					`COMPONENT_AT_SIGHTS`,
					`COMPONENT_AT_SIGHTS_SMG`
				},
				usetime = 2500
			}
		},

		['at_muzzle_flat'] = {
			label = 'Flat Muzzle',
			type = 'muzzle',
			weight = 80,
			client = {
				component = {
					`COMPONENT_AT_MUZZLE_01`
				},
				usetime = 2500
			}
		},

		['at_muzzle_tactical'] = {
			label = 'Tactical Muzzle',
			type = 'muzzle',
			weight = 80,
			client = {
				component = {
					`COMPONENT_AT_MUZZLE_02`
				},
				usetime = 2500
			}
		},

		['at_muzzle_fat'] = {
			label = 'Fat Muzzle',
			type = 'muzzle',
			weight = 80,
			client = {
				component = {
					`COMPONENT_AT_MUZZLE_03`
				},
				usetime = 2500
			}
		},

		['at_muzzle_precision'] = {
			label = 'Precision Muzzle',
			type = 'muzzle',
			weight = 80,
			client = {
				component = {
					`COMPONENT_AT_MUZZLE_04`
				},
				usetime = 2500
			}
		},

		['at_muzzle_heavy'] = {
			label = 'Heavy Muzzle',
			type = 'muzzle',
			weight = 80,
			client = {
				component = {
					`COMPONENT_AT_MUZZLE_05`
				},
				usetime = 2500
			}
		},

		['at_muzzle_slanted'] = {
			label = 'Slanted Muzzle',
			type = 'muzzle',
			weight = 80,
			client = {
				component = {
					`COMPONENT_AT_MUZZLE_06`
				},
				usetime = 2500
			}
		},

		['at_muzzle_split'] = {
			label = 'Split Muzzle',
			type = 'muzzle',
			weight = 80,
			client = {
				component = {
					`COMPONENT_AT_MUZZLE_07`
				},
				usetime = 2500
			}
		},

		['at_muzzle_squared'] = {
			label = 'Squared Muzzle',
			type = 'muzzle',
			weight = 80,
			client = {
				component = {
					`COMPONENT_AT_MUZZLE_08`
				},
				usetime = 2500
			}
		},

		['at_muzzle_bell'] = {
			label = 'Bell Muzzle',
			type = 'muzzle',
			weight = 80,
			client = {
				component = {
					`COMPONENT_AT_MUZZLE_09`
				},
				usetime = 2500
			}
		},

		['at_skin_luxe'] = {
			label = 'Luxury Weapon Kit',
			type = 'skin',
			weight = 50,
			client = {
				component = {
					`COMPONENT_ASSAULTRIFLE_VARMOD_LUXE`,
					`COMPONENT_ASSAULTSMG_VARMOD_LOWRIDER`,
					`COMPONENT_CARBINERIFLE_VARMOD_LUXE`,
					`COMPONENT_COMBATPISTOL_VARMOD_LOWRIDER`,
					`COMPONENT_MARKSMANRIFLE_VARMOD_LUXE`,
					`COMPONENT_MG_VARMOD_LOWRIDER`,
					`COMPONENT_MICROSMG_VARMOD_LUXE`,
					`COMPONENT_PISTOL_VARMOD_LUXE`,
					`COMPONENT_PUMPSHOTGUN_VARMOD_LOWRIDER`,
					`COMPONENT_SMG_VARMOD_LUXE`
				},
				usetime = 2500
			}
		},

		['at_skin_wood'] = {
			label = 'Wood Weapon Kit',
			type = 'skin',
			weight = 50,
			client = {
				component = {
					`COMPONENT_HEAVYPISTOL_VARMOD_LUXE`,
					`COMPONENT_SNIPERRIFLE_VARMOD_LUXE`,
					`COMPONENT_SNSPISTOL_VARMOD_LOWRIDER`
				},
				usetime = 2500
			}
		},

		['at_skin_metal'] = {
			label = 'Metal Weapon Kit',
			type = 'skin',
			weight = 50,
			client = {
				component = {
					`COMPONENT_ADVANCEDRIFLE_VARMOD_LUXE`,
					`COMPONENT_APPISTOL_VARMOD_LUXE`,
					`COMPONENT_BULLPUPRIFLE_VARMOD_LOW`,
					`COMPONENT_SAWNOFFSHOTGUN_VARMOD_LUXE`,
					`COMPONENT_SPECIALCARBINE_VARMOD_LOWRIDER`
				},
				usetime = 2500
			}
		},

		['at_skin_pearl'] = {
			label = 'Pearl Weapon Kit',
			type = 'skin',
			weight = 50,
			client = {
				component = {
					`COMPONENT_PISTOL50_VARMOD_LUXE`
				},
				usetime = 2500
			}
		},

		['at_skin_ballas'] = {
			label = 'Ballas Weapon Kit',
			type = 'skin',
			weight = 50,
			client = {
				component = {
					`COMPONENT_KNUCKLE_VARMOD_BALLAS`
				},
				usetime = 2500
			}
		},

		['at_skin_diamond'] = {
			label = 'Diamond Weapon Kit',
			type = 'skin',
			weight = 50,
			client = {
				component = {
					`COMPONENT_KNUCKLE_VARMOD_DIAMOND`
				},
				usetime = 2500
			}
		},

		['at_skin_dollar'] = {
			label = 'Dollar Weapon Kit',
			type = 'skin',
			weight = 50,
			client = {
				component = {
					`COMPONENT_KNUCKLE_VARMOD_DOLLAR`
				},
				usetime = 2500
			}
		},

		['at_skin_hate'] = {
			label = 'Hate Weapon Kit',
			type = 'skin',
			weight = 50,
			client = {
				component = {
					`COMPONENT_KNUCKLE_VARMOD_HATE`
				},
				usetime = 2500
			}
		},

		['at_skin_king'] = {
			label = 'King Weapon Kit',
			type = 'skin',
			weight = 50,
			client = {
				component = {
					`COMPONENT_KNUCKLE_VARMOD_KING`
				},
				usetime = 2500
			}
		},

		['at_skin_love'] = {
			label = 'Love Weapon Kit',
			type = 'skin',
			weight = 50,
			client = {
				component = {
					`COMPONENT_KNUCKLE_VARMOD_LOVE`
				},
				usetime = 2500
			}
		},

		['at_skin_pimp'] = {
			label = 'Pimp Weapon Kit',
			type = 'skin',
			weight = 50,
			client = {
				component = {
					`COMPONENT_KNUCKLE_VARMOD_PIMP`
				},
				usetime = 2500
			}
		},

		['at_skin_player'] = {
			label = 'Player Weapon Kit',
			type = 'skin',
			weight = 50,
			client = {
				component = {
					`COMPONENT_KNUCKLE_VARMOD_PLAYER`
				},
				usetime = 2500
			}
		},

		['at_skin_vagos'] = {
			label = 'Vagos Weapon Kit',
			type = 'skin',
			weight = 50,
			client = {
				component = {
					`COMPONENT_KNUCKLE_VARMOD_VAGOS`
				},
				usetime = 2500
			}
		},

		['at_skin_blagueurs'] = {
			label = 'Blagueurs Weapon Kit',
			type = 'skin',
			weight = 50,
			client = {
				component = {
					`COMPONENT_BAT_VARMOD_XM3`
				},
				usetime = 2500
			}
		},

		['at_skin_splatter'] = {
			label = 'Splatter Weapon Kit',
			type = 'skin',
			weight = 50,
			client = {
				component = {
					`COMPONENT_BAT_VARMOD_XM3_01`
				},
				usetime = 2500
			}
		},

		['at_skin_bulletholes'] = {
			label = 'Bullet Holes Weapon Kit',
			type = 'skin',
			weight = 50,
			client = {
				component = {
					`COMPONENT_BAT_VARMOD_XM3_02`
				},
				usetime = 2500
			}
		},

		['at_skin_burgershot'] = {
			label = 'Burger Shot Weapon Kit',
			type = 'skin',
			weight = 50,
			client = {
				component = {
					`COMPONENT_BAT_VARMOD_XM3_03`
				},
				usetime = 2500
			}
		},

		['at_skin_cluckinbell'] = {
			label = 'Cluckin Bell Weapon Kit',
			type = 'skin',
			weight = 50,
			client = {
				component = {
					`COMPONENT_BAT_VARMOD_XM3_04`
				},
				usetime = 2500
			}
		},

		['at_skin_fatalincursion'] = {
			label = 'Fatal Incursion Weapon Kit',
			type = 'skin',
			weight = 50,
			client = {
				component = {
					`COMPONENT_BAT_VARMOD_XM3_05`
				},
				usetime = 2500
			}
		},

		['at_skin_luchalibre'] = {
			label = 'Lucha Libre Weapon Kit',
			type = 'skin',
			weight = 50,
			client = {
				component = {
					`COMPONENT_BAT_VARMOD_XM3_06`
				},
				usetime = 2500
			}
		},

		['at_skin_trippy'] = {
			label = 'Trippy Weapon Kit',
			type = 'skin',
			weight = 50,
			client = {
				component = {
					`COMPONENT_BAT_VARMOD_XM3_07`
				},
				usetime = 2500
			}
		},

		['at_skin_tiedye'] = {
			label = 'Tie-Dye Weapon Kit',
			type = 'skin',
			weight = 50,
			client = {
				component = {
					`COMPONENT_BAT_VARMOD_XM3_08`
				},
				usetime = 2500
			}
		},

		['at_skin_wall'] = {
			label = 'Wall Weapon Kit',
			type = 'skin',
			weight = 50,
			client = {
				component = {
					`COMPONENT_BAT_VARMOD_XM3_09`
				},
				usetime = 2500
			}
		},

		['at_skin_vip'] = {
			label = 'VIP Weapon Kit',
			type = 'skin',
			weight = 50,
			client = {
				component = {
					`COMPONENT_REVOLVER_VARMOD_BOSS`,
					`COMPONENT_SWITCHBLADE_VARMOD_VAR1`
				},
				usetime = 2500
			}
		},

		['at_skin_bodyguard'] = {
			label = 'Bodyguard Weapon Kit',
			type = 'skin',
			weight = 50,
			client = {
				component = {
					`COMPONENT_REVOLVER_VARMOD_GOON`,
					`COMPONENT_SWITCHBLADE_VARMOD_VAR2`
				},
				usetime = 2500
			}
		},

		['at_skin_festive'] = {
			label = 'Festive Weapon Kit',
			type = 'skin',
			weight = 50,
			client = {
				component = {
					`COMPONENT_RAYPISTOL_VARMOD_XMAS18`
				},
				usetime = 2500
			}
		},

		['at_skin_security'] = {
			label = 'Security Weapon Kit',
			type = 'skin',
			weight = 50,
			client = {
				component = {
					`COMPONENT_APPISTOL_VARMOD_SECURITY`,
					`COMPONENT_MICROSMG_VARMOD_SECURITY`,
				},
				usetime = 2500
			}
		},

		['at_skin_camo'] = {
			label = 'Camo Weapon Kit',
			type = 'skin',
			weight = 50,
			client = {
				component = {
					`COMPONENT_ASSAULTRIFLE_MK2_CAMO`,
					`COMPONENT_BULLPUPRIFLE_MK2_CAMO`,
					`COMPONENT_CARBINERIFLE_MK2_CAMO`,
					`COMPONENT_COMBATMG_MK2_CAMO`,
					`COMPONENT_HEAVYSNIPER_MK2_CAMO`,
					`COMPONENT_MARKSMANRIFLE_MK2_CAMO`,
					`COMPONENT_PISTOL_MK2_CAMO`,
					`COMPONENT_PUMPSHOTGUN_MK2_CAMO`,
					`COMPONENT_REVOLVER_MK2_CAMO`,
					`COMPONENT_SMG_MK2_CAMO`,
					`COMPONENT_SNSPISTOL_MK2_CAMO`,
					`COMPONENT_SPECIALCARBINE_MK2_CAMO`,
				},
				usetime = 2500
			}
		},

		['at_skin_brushstroke'] = {
			label = 'Brushstroke Weapon Kit',
			type = 'skin',
			weight = 50,
			client = {
				component = {
					`COMPONENT_ASSAULTRIFLE_MK2_CAMO_02`,
					`COMPONENT_BULLPUPRIFLE_MK2_CAMO_02`,
					`COMPONENT_CARBINERIFLE_MK2_CAMO_02`,
					`COMPONENT_COMBATMG_MK2_CAMO_02`,
					`COMPONENT_HEAVYSNIPER_MK2_CAMO_02`,
					`COMPONENT_MARKSMANRIFLE_MK2_CAMO_02`,
					`COMPONENT_PISTOL_MK2_CAMO_02`,
					`COMPONENT_PUMPSHOTGUN_MK2_CAMO_02`,
					`COMPONENT_REVOLVER_MK2_CAMO_02`,
					`COMPONENT_SMG_MK2_CAMO_02`,
					`COMPONENT_SNSPISTOL_MK2_CAMO_02`,
					`COMPONENT_SPECIALCARBINE_MK2_CAMO_02`,
				},
				usetime = 2500
			}
		},

		['at_skin_woodland'] = {
			label = 'Woodland Weapon Kit',
			type = 'skin',
			weight = 50,
			client = {
				component = {
					`COMPONENT_ASSAULTRIFLE_MK2_CAMO_03`,
					`COMPONENT_BULLPUPRIFLE_MK2_CAMO_03`,
					`COMPONENT_CARBINERIFLE_MK2_CAMO_03`,
					`COMPONENT_COMBATMG_MK2_CAMO_03`,
					`COMPONENT_HEAVYSNIPER_MK2_CAMO_03`,
					`COMPONENT_MARKSMANRIFLE_MK2_CAMO_03`,
					`COMPONENT_PISTOL_MK2_CAMO_03`,
					`COMPONENT_PUMPSHOTGUN_MK2_CAMO_03`,
					`COMPONENT_REVOLVER_MK2_CAMO_03`,
					`COMPONENT_SMG_MK2_CAMO_03`,
					`COMPONENT_SNSPISTOL_MK2_CAMO_03`,
					`COMPONENT_SPECIALCARBINE_MK2_CAMO_03`,
				},
				usetime = 2500
			}
		},

		['at_skin_skull'] = {
			label = 'Skull Weapon Kit',
			type = 'skin',
			weight = 50,
			client = {
				component = {
					`COMPONENT_ASSAULTRIFLE_MK2_CAMO_04`,
					`COMPONENT_BULLPUPRIFLE_MK2_CAMO_04`,
					`COMPONENT_CARBINERIFLE_MK2_CAMO_04`,
					`COMPONENT_COMBATMG_MK2_CAMO_04`,
					`COMPONENT_HEAVYSNIPER_MK2_CAMO_04`,
					`COMPONENT_MARKSMANRIFLE_MK2_CAMO_04`,
					`COMPONENT_PISTOL_MK2_CAMO_04`,
					`COMPONENT_PUMPSHOTGUN_MK2_CAMO_04`,
					`COMPONENT_REVOLVER_MK2_CAMO_04`,
					`COMPONENT_SMG_MK2_CAMO_04`,
					`COMPONENT_SNSPISTOL_MK2_CAMO_04`,
					`COMPONENT_SPECIALCARBINE_MK2_CAMO_04`,
				},
				usetime = 2500
			}
		},

		['at_skin_sessanta'] = {
			label = 'Sessanta Weapon Kit',
			type = 'skin',
			weight = 50,
			client = {
				component = {
					`COMPONENT_ASSAULTRIFLE_MK2_CAMO_05`,
					`COMPONENT_BULLPUPRIFLE_MK2_CAMO_05`,
					`COMPONENT_CARBINERIFLE_MK2_CAMO_05`,
					`COMPONENT_COMBATMG_MK2_CAMO_05`,
					`COMPONENT_HEAVYSNIPER_MK2_CAMO_05`,
					`COMPONENT_MARKSMANRIFLE_MK2_CAMO_05`,
					`COMPONENT_PISTOL_MK2_CAMO_05`,
					`COMPONENT_PUMPSHOTGUN_MK2_CAMO_05`,
					`COMPONENT_REVOLVER_MK2_CAMO_05`,
					`COMPONENT_SMG_MK2_CAMO_05`,
					`COMPONENT_SNSPISTOL_MK2_CAMO_05`,
					`COMPONENT_SPECIALCARBINE_MK2_CAMO_05`,
				},
				usetime = 2500
			}
		},

		['at_skin_perseus'] = {
			label = 'Perseus Weapon Kit',
			type = 'skin',
			weight = 50,
			client = {
				component = {
					`COMPONENT_ASSAULTRIFLE_MK2_CAMO_06`,
					`COMPONENT_BULLPUPRIFLE_MK2_CAMO_06`,
					`COMPONENT_CARBINERIFLE_MK2_CAMO_06`,
					`COMPONENT_COMBATMG_MK2_CAMO_06`,
					`COMPONENT_HEAVYSNIPER_MK2_CAMO_06`,
					`COMPONENT_MARKSMANRIFLE_MK2_CAMO_06`,
					`COMPONENT_PISTOL_MK2_CAMO_06`,
					`COMPONENT_PUMPSHOTGUN_MK2_CAMO_06`,
					`COMPONENT_REVOLVER_MK2_CAMO_06`,
					`COMPONENT_SMG_MK2_CAMO_06`,
					`COMPONENT_SNSPISTOL_MK2_CAMO_06`,
					`COMPONENT_SPECIALCARBINE_MK2_CAMO_06`,
				},
				usetime = 2500
			}
		},

		['at_skin_leopard'] = {
			label = 'Leopard Weapon Kit',
			type = 'skin',
			weight = 50,
			client = {
				component = {
					`COMPONENT_ASSAULTRIFLE_MK2_CAMO_07`,
					`COMPONENT_BULLPUPRIFLE_MK2_CAMO_07`,
					`COMPONENT_CARBINERIFLE_MK2_CAMO_07`,
					`COMPONENT_COMBATMG_MK2_CAMO_07`,
					`COMPONENT_HEAVYSNIPER_MK2_CAMO_07`,
					`COMPONENT_MARKSMANRIFLE_MK2_CAMO_07`,
					`COMPONENT_PISTOL_MK2_CAMO_07`,
					`COMPONENT_PUMPSHOTGUN_MK2_CAMO_07`,
					`COMPONENT_REVOLVER_MK2_CAMO_07`,
					`COMPONENT_SMG_MK2_CAMO_07`,
					`COMPONENT_SNSPISTOL_MK2_CAMO_07`,
					`COMPONENT_SPECIALCARBINE_MK2_CAMO_07`,
				},
				usetime = 2500
			}
		},

		['at_skin_zebra'] = {
			label = 'Zebra Weapon Kit',
			type = 'skin',
			weight = 50,
			client = {
				component = {
					`COMPONENT_ASSAULTRIFLE_MK2_CAMO_08`,
					`COMPONENT_BULLPUPRIFLE_MK2_CAMO_08`,
					`COMPONENT_CARBINERIFLE_MK2_CAMO_08`,
					`COMPONENT_COMBATMG_MK2_CAMO_08`,
					`COMPONENT_HEAVYSNIPER_MK2_CAMO_08`,
					`COMPONENT_MARKSMANRIFLE_MK2_CAMO_08`,
					`COMPONENT_PISTOL_MK2_CAMO_08`,
					`COMPONENT_PUMPSHOTGUN_MK2_CAMO_08`,
					`COMPONENT_REVOLVER_MK2_CAMO_08`,
					`COMPONENT_SMG_MK2_CAMO_08`,
					`COMPONENT_SNSPISTOL_MK2_CAMO_08`,
					`COMPONENT_SPECIALCARBINE_MK2_CAMO_08`,
				},
				usetime = 2500
			}
		},

		['at_skin_geometric'] = {
			label = 'Geometric Weapon Kit',
			type = 'skin',
			weight = 50,
			client = {
				component = {
					`COMPONENT_ASSAULTRIFLE_MK2_CAMO_09`,
					`COMPONENT_BULLPUPRIFLE_MK2_CAMO_09`,
					`COMPONENT_CARBINERIFLE_MK2_CAMO_09`,
					`COMPONENT_COMBATMG_MK2_CAMO_09`,
					`COMPONENT_HEAVYSNIPER_MK2_CAMO_09`,
					`COMPONENT_MARKSMANRIFLE_MK2_CAMO_09`,
					`COMPONENT_PISTOL_MK2_CAMO_09`,
					`COMPONENT_PUMPSHOTGUN_MK2_CAMO_09`,
					`COMPONENT_REVOLVER_MK2_CAMO_09`,
					`COMPONENT_SMG_MK2_CAMO_09`,
					`COMPONENT_SNSPISTOL_MK2_CAMO_09`,
					`COMPONENT_SPECIALCARBINE_MK2_CAMO_09`,
				},
				usetime = 2500
			}
		},

		['at_skin_boom'] = {
			label = 'Boom Weapon Kit',
			type = 'skin',
			weight = 50,
			client = {
				component = {
					`COMPONENT_ASSAULTRIFLE_MK2_CAMO_10`,
					`COMPONENT_BULLPUPRIFLE_MK2_CAMO_10`,
					`COMPONENT_CARBINERIFLE_MK2_CAMO_10`,
					`COMPONENT_COMBATMG_MK2_CAMO_10`,
					`COMPONENT_HEAVYSNIPER_MK2_CAMO_10`,
					`COMPONENT_MARKSMANRIFLE_MK2_CAMO_10`,
					`COMPONENT_PISTOL_MK2_CAMO_10`,
					`COMPONENT_PUMPSHOTGUN_MK2_CAMO_10`,
					`COMPONENT_REVOLVER_MK2_CAMO_10`,
					`COMPONENT_SMG_MK2_CAMO_10`,
					`COMPONENT_SNSPISTOL_MK2_CAMO_10`,
					`COMPONENT_SPECIALCARBINE_MK2_CAMO_10`,
				},
				usetime = 2500
			}
		},

		['at_skin_patriotic'] = {
			label = 'Patriotic Weapon Kit',
			type = 'skin',
			weight = 50,
			client = {
				component = {
					`COMPONENT_ASSAULTRIFLE_MK2_CAMO_IND_01`,
					`COMPONENT_BULLPUPRIFLE_MK2_CAMO_IND_01`,
					`COMPONENT_CARBINERIFLE_MK2_CAMO_IND_01`,
					`COMPONENT_COMBATMG_MK2_CAMO_IND_01`,
					`COMPONENT_HEAVYSNIPER_MK2_CAMO_IND_01`,
					`COMPONENT_MARKSMANRIFLE_MK2_CAMO_IND_01`,
					`COMPONENT_PISTOL_MK2_CAMO_IND_01`,
					`COMPONENT_PUMPSHOTGUN_MK2_CAMO_IND_01`,
					`COMPONENT_REVOLVER_MK2_CAMO_IND_01`,
					`COMPONENT_SMG_MK2_CAMO_IND_01`,
					`COMPONENT_SNSPISTOL_MK2_CAMO_IND_01`,
					`COMPONENT_SPECIALCARBINE_MK2_CAMO_IND_01`,
				},
				usetime = 2500
			}
		},

		['rifle_defaultclip'] = {
			label = 'Default Clip',
			weight = 600,
			type = 'clip',
			client = {
			  component = { 'w_ar_famas_mag1' },
			  usetime    = 0,
			}
		  },
		  ['rifle_extendedclip'] = {
			label = 'Extended Clip',
			weight = 900,
			type = 'clip',
			client = {
			  component = { 'w_ar_m4a1_mod_mag2' },
			  usetime    = 0,
			}
		  },
		  ['rifle_sight'] = {
			label = 'Red-Dot Sight',
			weight = 300,
			type   = 'sight',
			client = {
			  component = { 'w_at_famas_sight' },
			  usetime    = 2500,
			}
		  },
		  ['rifle_scope'] = {
			label = '4× Scope',
			weight = 500,
			type   = 'scope',
			client = {
			  component = { 'w_at_m4a1_mod_scope_small' },
			  usetime    = 2500,
			}
		  },
		  ['rifle_suppressor'] = {
			label = 'Suppressor',
			weight = 400,
			type   = 'suppressor',
			client = {
			  component = { 'w_at_m4a1_mod_supp' },
			  usetime    = 2500,
			}
		  },
		
	},

	Ammo = {
		['ammo-22'] = {
			label = '.22 Long Rifle',
			weight = 3,
		},

		['ammo-38'] = {
			label = '.38 LC',
			weight = 15,
		},

		['ammo-44'] = {
			label = '.44 Magnum',
			weight = 16,
		},

		['ammo-45'] = {
			label = '.45 ACP',
			weight = 15,
		},

		['ammo-50'] = {
			label = '.50 AE',
			weight = 25,
		},

		['ammo-9'] = {
			label = '9mm',
			weight = 5,
		},

		['ammo-firework'] = {
			label = 'Firework',
			weight = 200,
		},

		['ammo-flare'] = {
			label = 'Flare round',
			weight = 38,
		},

		['ammo-grenade'] = {
			label = '40mm Explosive',
			weight = 400,
		},

		['ammo-heavysniper'] = {
			label = '.50 BMG',
			weight = 51,
		},

		['ammo-laser'] = {
			label = 'Laser charge',
			weight = 1,
		},

		['ammo-stun'] = {
			label = 'Stun charge',
			weight = 1,
		},

		['ammo-musket'] = {
			label = '.50 Ball',
			weight = 38,
		},

		['ammo-railgun'] = {
			label = 'Railgun charge',
			weight = 150,
		},

		['ammo-rifle'] = {
			label = '5.56x45',
			weight = 4,
		},

		['ammo-rifle2'] = {
			label = '7.62x39',
			weight = 8,
		},

		['ammo-rocket'] = {
			label = 'Rocket',
			weight = 500,
		},

		['ammo-shotgun'] = {
			label = '12 Gauge',
			weight = 38,
		},

		['ammo-sniper'] = {
			label = '7.62x51',
			weight = 9,
		},

		['ammo-emp'] = {
			label = 'EMP round',
			weight = 400,
		},
		['WEAPON_DIGISCANNER'] = {
			label = 'Signal booster',
			weight = 1200,
		},
		['snowball'] = {
			label = 'snowball launcher ammo',
			weight = 3,
		},
		['gg_hunting_arrowammo'] = {
			label = 'Arrow Ammo',
			weight = 20,
		},		
		['gg_hunting_rifleammo'] = {
			label = 'Hunting Rifle Ammo',
			weight = 20,
		},
	}
}
