{"version": 3, "sources": ["../../node_modules/@floating-ui/react/dist/floating-ui.react.mjs", "../../node_modules/@floating-ui/utils/dist/floating-ui.utils.mjs", "../../node_modules/@floating-ui/utils/dom/dist/floating-ui.utils.dom.mjs", "../../node_modules/@floating-ui/utils/react/dist/floating-ui.utils.react.mjs", "../../node_modules/@floating-ui/core/node_modules/@floating-ui/utils/dist/floating-ui.utils.mjs", "../../node_modules/@floating-ui/core/dist/floating-ui.core.mjs", "../../node_modules/@floating-ui/dom/node_modules/@floating-ui/utils/dist/floating-ui.utils.mjs", "../../node_modules/@floating-ui/dom/node_modules/@floating-ui/utils/dist/floating-ui.utils.dom.mjs", "../../node_modules/@floating-ui/dom/dist/floating-ui.dom.mjs", "../../node_modules/@floating-ui/react-dom/dist/floating-ui.react-dom.mjs", "../../node_modules/tabbable/src/index.js"], "sourcesContent": ["import * as React from 'react';\nimport { useLayoutEffect, useEffect, useRef } from 'react';\nimport { floor } from '@floating-ui/utils';\nimport { stopEvent, getDocument, isMouseLikePointerType, contains, activeElement, isSafari, isTypeableElement, getTarget, getPlatform, isReactEvent, isRootElement, isEventTargetWithin, isVirtualClick, isVirtualPointerEvent, isMac, getUserAgent } from '@floating-ui/utils/react';\nimport { platform, getOverflowAncestors, useFloating as useFloating$1, offset, detectOverflow } from '@floating-ui/react-dom';\nexport { arrow, autoPlacement, autoUpdate, computePosition, detectOverflow, flip, getOverflowAncestors, hide, inline, limitShift, offset, platform, shift, size } from '@floating-ui/react-dom';\nimport { isElement, isHTMLElement, getWindow, isLastTraversableNode, getParentNode, getComputedStyle } from '@floating-ui/utils/dom';\nimport { tabbable } from 'tabbable';\nimport { createPortal, flushSync } from 'react-dom';\n\n/**\n * Merges an array of refs into a single memoized callback ref or `null`.\n * @see https://floating-ui.com/docs/useMergeRefs\n */\nfunction useMergeRefs(refs) {\n  return React.useMemo(() => {\n    if (refs.every(ref => ref == null)) {\n      return null;\n    }\n    return value => {\n      refs.forEach(ref => {\n        if (typeof ref === 'function') {\n          ref(value);\n        } else if (ref != null) {\n          ref.current = value;\n        }\n      });\n    };\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, refs);\n}\n\nconst ARROW_UP = 'ArrowUp';\nconst ARROW_DOWN = 'ArrowDown';\nconst ARROW_LEFT = 'ArrowLeft';\nconst ARROW_RIGHT = 'ArrowRight';\nfunction isDifferentRow(index, cols, prevRow) {\n  return Math.floor(index / cols) !== prevRow;\n}\nfunction isIndexOutOfBounds(listRef, index) {\n  return index < 0 || index >= listRef.current.length;\n}\nfunction getMinIndex(listRef, disabledIndices) {\n  return findNonDisabledIndex(listRef, {\n    disabledIndices\n  });\n}\nfunction getMaxIndex(listRef, disabledIndices) {\n  return findNonDisabledIndex(listRef, {\n    decrement: true,\n    startingIndex: listRef.current.length,\n    disabledIndices\n  });\n}\nfunction findNonDisabledIndex(listRef, _temp) {\n  let {\n    startingIndex = -1,\n    decrement = false,\n    disabledIndices,\n    amount = 1\n  } = _temp === void 0 ? {} : _temp;\n  const list = listRef.current;\n  let index = startingIndex;\n  do {\n    var _list$index, _list$index2;\n    index = index + (decrement ? -amount : amount);\n  } while (index >= 0 && index <= list.length - 1 && (disabledIndices ? disabledIndices.includes(index) : list[index] == null || ((_list$index = list[index]) == null ? void 0 : _list$index.hasAttribute('disabled')) || ((_list$index2 = list[index]) == null ? void 0 : _list$index2.getAttribute('aria-disabled')) === 'true'));\n  return index;\n}\nfunction getGridNavigatedIndex(elementsRef, _ref) {\n  let {\n    event,\n    orientation,\n    loop,\n    cols,\n    disabledIndices,\n    minIndex,\n    maxIndex,\n    prevIndex,\n    stopEvent: stop = false\n  } = _ref;\n  let nextIndex = prevIndex;\n  if (event.key === ARROW_UP) {\n    stop && stopEvent(event);\n    if (prevIndex === -1) {\n      nextIndex = maxIndex;\n    } else {\n      nextIndex = findNonDisabledIndex(elementsRef, {\n        startingIndex: nextIndex,\n        amount: cols,\n        decrement: true,\n        disabledIndices\n      });\n      if (loop && (prevIndex - cols < minIndex || nextIndex < 0)) {\n        const col = prevIndex % cols;\n        const maxCol = maxIndex % cols;\n        const offset = maxIndex - (maxCol - col);\n        if (maxCol === col) {\n          nextIndex = maxIndex;\n        } else {\n          nextIndex = maxCol > col ? offset : offset - cols;\n        }\n      }\n    }\n    if (isIndexOutOfBounds(elementsRef, nextIndex)) {\n      nextIndex = prevIndex;\n    }\n  }\n  if (event.key === ARROW_DOWN) {\n    stop && stopEvent(event);\n    if (prevIndex === -1) {\n      nextIndex = minIndex;\n    } else {\n      nextIndex = findNonDisabledIndex(elementsRef, {\n        startingIndex: prevIndex,\n        amount: cols,\n        disabledIndices\n      });\n      if (loop && prevIndex + cols > maxIndex) {\n        nextIndex = findNonDisabledIndex(elementsRef, {\n          startingIndex: prevIndex % cols - cols,\n          amount: cols,\n          disabledIndices\n        });\n      }\n    }\n    if (isIndexOutOfBounds(elementsRef, nextIndex)) {\n      nextIndex = prevIndex;\n    }\n  }\n\n  // Remains on the same row/column.\n  if (orientation === 'both') {\n    const prevRow = floor(prevIndex / cols);\n    if (event.key === ARROW_RIGHT) {\n      stop && stopEvent(event);\n      if (prevIndex % cols !== cols - 1) {\n        nextIndex = findNonDisabledIndex(elementsRef, {\n          startingIndex: prevIndex,\n          disabledIndices\n        });\n        if (loop && isDifferentRow(nextIndex, cols, prevRow)) {\n          nextIndex = findNonDisabledIndex(elementsRef, {\n            startingIndex: prevIndex - prevIndex % cols - 1,\n            disabledIndices\n          });\n        }\n      } else if (loop) {\n        nextIndex = findNonDisabledIndex(elementsRef, {\n          startingIndex: prevIndex - prevIndex % cols - 1,\n          disabledIndices\n        });\n      }\n      if (isDifferentRow(nextIndex, cols, prevRow)) {\n        nextIndex = prevIndex;\n      }\n    }\n    if (event.key === ARROW_LEFT) {\n      stop && stopEvent(event);\n      if (prevIndex % cols !== 0) {\n        nextIndex = findNonDisabledIndex(elementsRef, {\n          startingIndex: prevIndex,\n          disabledIndices,\n          decrement: true\n        });\n        if (loop && isDifferentRow(nextIndex, cols, prevRow)) {\n          nextIndex = findNonDisabledIndex(elementsRef, {\n            startingIndex: prevIndex + (cols - prevIndex % cols),\n            decrement: true,\n            disabledIndices\n          });\n        }\n      } else if (loop) {\n        nextIndex = findNonDisabledIndex(elementsRef, {\n          startingIndex: prevIndex + (cols - prevIndex % cols),\n          decrement: true,\n          disabledIndices\n        });\n      }\n      if (isDifferentRow(nextIndex, cols, prevRow)) {\n        nextIndex = prevIndex;\n      }\n    }\n    const lastRow = floor(maxIndex / cols) === prevRow;\n    if (isIndexOutOfBounds(elementsRef, nextIndex)) {\n      if (loop && lastRow) {\n        nextIndex = event.key === ARROW_LEFT ? maxIndex : findNonDisabledIndex(elementsRef, {\n          startingIndex: prevIndex - prevIndex % cols - 1,\n          disabledIndices\n        });\n      } else {\n        nextIndex = prevIndex;\n      }\n    }\n  }\n  return nextIndex;\n}\n\nlet rafId = 0;\nfunction enqueueFocus(el, options) {\n  if (options === void 0) {\n    options = {};\n  }\n  const {\n    preventScroll = false,\n    cancelPrevious = true,\n    sync = false\n  } = options;\n  cancelPrevious && cancelAnimationFrame(rafId);\n  const exec = () => el == null ? void 0 : el.focus({\n    preventScroll\n  });\n  if (sync) {\n    exec();\n  } else {\n    rafId = requestAnimationFrame(exec);\n  }\n}\n\nvar index = typeof document !== 'undefined' ? useLayoutEffect : useEffect;\n\nfunction sortByDocumentPosition(a, b) {\n  const position = a.compareDocumentPosition(b);\n  if (position & Node.DOCUMENT_POSITION_FOLLOWING || position & Node.DOCUMENT_POSITION_CONTAINED_BY) {\n    return -1;\n  }\n  if (position & Node.DOCUMENT_POSITION_PRECEDING || position & Node.DOCUMENT_POSITION_CONTAINS) {\n    return 1;\n  }\n  return 0;\n}\nfunction areMapsEqual(map1, map2) {\n  if (map1.size !== map2.size) {\n    return false;\n  }\n  for (const [key, value] of map1.entries()) {\n    if (value !== map2.get(key)) {\n      return false;\n    }\n  }\n  return true;\n}\nconst FloatingListContext = /*#__PURE__*/React.createContext({\n  register: () => {},\n  unregister: () => {},\n  map: /*#__PURE__*/new Map(),\n  elementsRef: {\n    current: []\n  }\n});\n/**\n * Provides context for a list of items within the floating element.\n * @see https://floating-ui.com/docs/FloatingList\n */\nfunction FloatingList(_ref) {\n  let {\n    children,\n    elementsRef,\n    labelsRef\n  } = _ref;\n  const [map, setMap] = React.useState(() => new Map());\n  const register = React.useCallback(node => {\n    setMap(prevMap => new Map(prevMap).set(node, null));\n  }, []);\n  const unregister = React.useCallback(node => {\n    setMap(prevMap => {\n      const map = new Map(prevMap);\n      map.delete(node);\n      return map;\n    });\n  }, []);\n  index(() => {\n    const newMap = new Map(map);\n    const nodes = Array.from(newMap.keys()).sort(sortByDocumentPosition);\n    nodes.forEach((node, index) => {\n      newMap.set(node, index);\n    });\n    if (!areMapsEqual(map, newMap)) {\n      setMap(newMap);\n    }\n  }, [map]);\n  return /*#__PURE__*/React.createElement(FloatingListContext.Provider, {\n    value: React.useMemo(() => ({\n      register,\n      unregister,\n      map,\n      elementsRef,\n      labelsRef\n    }), [register, unregister, map, elementsRef, labelsRef])\n  }, children);\n}\nfunction useListItem(_temp) {\n  let {\n    label\n  } = _temp === void 0 ? {} : _temp;\n  const [index$1, setIndex] = React.useState(null);\n  const componentRef = React.useRef(null);\n  const {\n    register,\n    unregister,\n    map,\n    elementsRef,\n    labelsRef\n  } = React.useContext(FloatingListContext);\n  const ref = React.useCallback(node => {\n    componentRef.current = node;\n    if (index$1 !== null) {\n      elementsRef.current[index$1] = node;\n      if (labelsRef) {\n        var _node$textContent;\n        const isLabelDefined = label !== undefined;\n        labelsRef.current[index$1] = isLabelDefined ? label : (_node$textContent = node == null ? void 0 : node.textContent) != null ? _node$textContent : null;\n      }\n    }\n  }, [index$1, elementsRef, labelsRef, label]);\n  index(() => {\n    const node = componentRef.current;\n    if (node) {\n      register(node);\n      return () => {\n        unregister(node);\n      };\n    }\n  }, [register, unregister]);\n  index(() => {\n    const index = componentRef.current ? map.get(componentRef.current) : null;\n    if (index != null) {\n      setIndex(index);\n    }\n  }, [map]);\n  return React.useMemo(() => ({\n    ref,\n    index: index$1 == null ? -1 : index$1\n  }), [index$1, ref]);\n}\n\nfunction renderJsx(render, computedProps) {\n  if (typeof render === 'function') {\n    return render(computedProps);\n  } else if (render) {\n    return /*#__PURE__*/React.cloneElement(render, computedProps);\n  }\n  return /*#__PURE__*/React.createElement(\"div\", computedProps);\n}\nconst CompositeContext = /*#__PURE__*/React.createContext({\n  activeIndex: 0,\n  setActiveIndex: () => {}\n});\nconst horizontalKeys = [ARROW_LEFT, ARROW_RIGHT];\nconst verticalKeys = [ARROW_UP, ARROW_DOWN];\nconst allKeys = [...horizontalKeys, ...verticalKeys];\nconst Composite = /*#__PURE__*/React.forwardRef(function Composite(_ref, forwardedRef) {\n  let {\n    render,\n    orientation = 'both',\n    loop = true,\n    cols = 1,\n    disabledIndices,\n    ...props\n  } = _ref;\n  const [activeIndex, setActiveIndex] = React.useState(0);\n  const elementsRef = React.useRef([]);\n  const renderElementProps = render && typeof render !== 'function' ? render.props : {};\n  const contextValue = React.useMemo(() => ({\n    activeIndex,\n    setActiveIndex\n  }), [activeIndex]);\n  const isGrid = cols > 1;\n  function handleKeyDown(event) {\n    if (!allKeys.includes(event.key)) return;\n    const minIndex = getMinIndex(elementsRef, disabledIndices);\n    const maxIndex = getMaxIndex(elementsRef, disabledIndices);\n    const prevIndex = activeIndex;\n    let nextIndex = activeIndex;\n    if (isGrid) {\n      nextIndex = getGridNavigatedIndex(elementsRef, {\n        event,\n        orientation,\n        loop,\n        cols,\n        disabledIndices,\n        minIndex,\n        maxIndex,\n        prevIndex\n      });\n    }\n    const toEndKeys = {\n      horizontal: [ARROW_RIGHT],\n      vertical: [ARROW_DOWN],\n      both: [ARROW_RIGHT, ARROW_DOWN]\n    }[orientation];\n    const toStartKeys = {\n      horizontal: [ARROW_LEFT],\n      vertical: [ARROW_UP],\n      both: [ARROW_LEFT, ARROW_UP]\n    }[orientation];\n    const preventedKeys = isGrid ? allKeys : {\n      horizontal: horizontalKeys,\n      vertical: verticalKeys,\n      both: allKeys\n    }[orientation];\n    if (nextIndex === activeIndex && [...toEndKeys, ...toStartKeys].includes(event.key)) {\n      if (loop && nextIndex === maxIndex && toEndKeys.includes(event.key)) {\n        nextIndex = minIndex;\n      } else if (loop && nextIndex === minIndex && toStartKeys.includes(event.key)) {\n        nextIndex = maxIndex;\n      } else {\n        nextIndex = findNonDisabledIndex(elementsRef, {\n          startingIndex: nextIndex,\n          decrement: toStartKeys.includes(event.key),\n          disabledIndices\n        });\n      }\n    }\n    if (nextIndex !== activeIndex && !isIndexOutOfBounds(elementsRef, nextIndex)) {\n      event.stopPropagation();\n      if (preventedKeys.includes(event.key)) {\n        event.preventDefault();\n      }\n      setActiveIndex(nextIndex);\n\n      // Wait for FocusManager `returnFocus` to execute.\n      queueMicrotask(() => {\n        enqueueFocus(elementsRef.current[nextIndex]);\n      });\n    }\n  }\n  const computedProps = {\n    ...props,\n    ...renderElementProps,\n    ref: forwardedRef,\n    'aria-orientation': orientation === 'both' ? undefined : orientation,\n    onKeyDown(e) {\n      props.onKeyDown == null ? void 0 : props.onKeyDown(e);\n      renderElementProps.onKeyDown == null ? void 0 : renderElementProps.onKeyDown(e);\n      handleKeyDown(e);\n    }\n  };\n  return /*#__PURE__*/React.createElement(CompositeContext.Provider, {\n    value: contextValue\n  }, /*#__PURE__*/React.createElement(FloatingList, {\n    elementsRef: elementsRef\n  }, renderJsx(render, computedProps)));\n});\nconst CompositeItem = /*#__PURE__*/React.forwardRef(function CompositeItem(_ref2, forwardedRef) {\n  let {\n    render,\n    ...props\n  } = _ref2;\n  const renderElementProps = render && typeof render !== 'function' ? render.props : {};\n  const {\n    activeIndex,\n    setActiveIndex\n  } = React.useContext(CompositeContext);\n  const {\n    ref,\n    index\n  } = useListItem();\n  const mergedRef = useMergeRefs([ref, forwardedRef, renderElementProps.ref]);\n  const isActive = activeIndex === index;\n  const computedProps = {\n    ...props,\n    ...renderElementProps,\n    ref: mergedRef,\n    tabIndex: isActive ? 0 : -1,\n    'data-active': isActive ? '' : undefined,\n    onFocus(e) {\n      props.onFocus == null ? void 0 : props.onFocus(e);\n      renderElementProps.onFocus == null ? void 0 : renderElementProps.onFocus(e);\n      setActiveIndex(index);\n    }\n  };\n  return renderJsx(render, computedProps);\n});\n\nfunction _extends() {\n  _extends = Object.assign ? Object.assign.bind() : function (target) {\n    for (var i = 1; i < arguments.length; i++) {\n      var source = arguments[i];\n      for (var key in source) {\n        if (Object.prototype.hasOwnProperty.call(source, key)) {\n          target[key] = source[key];\n        }\n      }\n    }\n    return target;\n  };\n  return _extends.apply(this, arguments);\n}\n\nlet serverHandoffComplete = false;\nlet count = 0;\nconst genId = () => \"floating-ui-\" + count++;\nfunction useFloatingId() {\n  const [id, setId] = React.useState(() => serverHandoffComplete ? genId() : undefined);\n  index(() => {\n    if (id == null) {\n      setId(genId());\n    }\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, []);\n  React.useEffect(() => {\n    if (!serverHandoffComplete) {\n      serverHandoffComplete = true;\n    }\n  }, []);\n  return id;\n}\n\n// `toString()` prevents bundlers from trying to `import { useId } from 'react'`\nconst useReactId = React[/*#__PURE__*/'useId'.toString()];\n\n/**\n * Uses React 18's built-in `useId()` when available, or falls back to a\n * slightly less performant (requiring a double render) implementation for\n * earlier React versions.\n * @see https://floating-ui.com/docs/useId\n */\nconst useId = useReactId || useFloatingId;\n\n/**\n * Renders a pointing arrow triangle.\n * @see https://floating-ui.com/docs/FloatingArrow\n */\nconst FloatingArrow = /*#__PURE__*/React.forwardRef(function FloatingArrow(_ref, ref) {\n  let {\n    context: {\n      placement,\n      elements: {\n        floating\n      },\n      middlewareData: {\n        arrow\n      }\n    },\n    width = 14,\n    height = 7,\n    tipRadius = 0,\n    strokeWidth = 0,\n    staticOffset,\n    stroke,\n    d,\n    style: {\n      transform,\n      ...restStyle\n    } = {},\n    ...rest\n  } = _ref;\n  if (process.env.NODE_ENV !== \"production\") {\n    if (!ref) {\n      console.warn('Floating UI: The `ref` prop is required for the `FloatingArrow`', 'component.');\n    }\n  }\n  const clipPathId = useId();\n  if (!floating) {\n    return null;\n  }\n\n  // Strokes must be double the border width, this ensures the stroke's width\n  // works as you'd expect.\n  strokeWidth *= 2;\n  const halfStrokeWidth = strokeWidth / 2;\n  const svgX = width / 2 * (tipRadius / -8 + 1);\n  const svgY = height / 2 * tipRadius / 4;\n  const [side, alignment] = placement.split('-');\n  const isRTL = platform.isRTL(floating);\n  const isCustomShape = !!d;\n  const isVerticalSide = side === 'top' || side === 'bottom';\n  const yOffsetProp = staticOffset && alignment === 'end' ? 'bottom' : 'top';\n  let xOffsetProp = staticOffset && alignment === 'end' ? 'right' : 'left';\n  if (staticOffset && isRTL) {\n    xOffsetProp = alignment === 'end' ? 'left' : 'right';\n  }\n  const arrowX = (arrow == null ? void 0 : arrow.x) != null ? staticOffset || arrow.x : '';\n  const arrowY = (arrow == null ? void 0 : arrow.y) != null ? staticOffset || arrow.y : '';\n  const dValue = d || 'M0,0' + (\" H\" + width) + (\" L\" + (width - svgX) + \",\" + (height - svgY)) + (\" Q\" + width / 2 + \",\" + height + \" \" + svgX + \",\" + (height - svgY)) + ' Z';\n  const rotation = {\n    top: isCustomShape ? 'rotate(180deg)' : '',\n    left: isCustomShape ? 'rotate(90deg)' : 'rotate(-90deg)',\n    bottom: isCustomShape ? '' : 'rotate(180deg)',\n    right: isCustomShape ? 'rotate(-90deg)' : 'rotate(90deg)'\n  }[side];\n  return /*#__PURE__*/React.createElement(\"svg\", _extends({}, rest, {\n    \"aria-hidden\": true,\n    ref: ref,\n    width: isCustomShape ? width : width + strokeWidth,\n    height: width,\n    viewBox: \"0 0 \" + width + \" \" + (height > width ? height : width),\n    style: {\n      position: 'absolute',\n      pointerEvents: 'none',\n      [xOffsetProp]: arrowX,\n      [yOffsetProp]: arrowY,\n      [side]: isVerticalSide || isCustomShape ? '100%' : \"calc(100% - \" + strokeWidth / 2 + \"px)\",\n      transform: \"\" + rotation + (transform != null ? transform : ''),\n      ...restStyle\n    }\n  }), strokeWidth > 0 && /*#__PURE__*/React.createElement(\"path\", {\n    clipPath: \"url(#\" + clipPathId + \")\",\n    fill: \"none\",\n    stroke: stroke\n    // Account for the stroke on the fill path rendered below.\n    ,\n    strokeWidth: strokeWidth + (d ? 0 : 1),\n    d: dValue\n  }), /*#__PURE__*/React.createElement(\"path\", {\n    stroke: strokeWidth && !d ? rest.fill : 'none',\n    d: dValue\n  }), /*#__PURE__*/React.createElement(\"clipPath\", {\n    id: clipPathId\n  }, /*#__PURE__*/React.createElement(\"rect\", {\n    x: -halfStrokeWidth,\n    y: halfStrokeWidth * (isCustomShape ? -1 : 1),\n    width: width + strokeWidth,\n    height: width\n  })));\n});\n\nfunction createPubSub() {\n  const map = new Map();\n  return {\n    emit(event, data) {\n      var _map$get;\n      (_map$get = map.get(event)) == null ? void 0 : _map$get.forEach(handler => handler(data));\n    },\n    on(event, listener) {\n      map.set(event, [...(map.get(event) || []), listener]);\n    },\n    off(event, listener) {\n      var _map$get2;\n      map.set(event, ((_map$get2 = map.get(event)) == null ? void 0 : _map$get2.filter(l => l !== listener)) || []);\n    }\n  };\n}\n\nconst FloatingNodeContext = /*#__PURE__*/React.createContext(null);\nconst FloatingTreeContext = /*#__PURE__*/React.createContext(null);\nconst useFloatingParentNodeId = () => {\n  var _React$useContext;\n  return ((_React$useContext = React.useContext(FloatingNodeContext)) == null ? void 0 : _React$useContext.id) || null;\n};\nconst useFloatingTree = () => React.useContext(FloatingTreeContext);\n\n/**\n * Registers a node into the floating tree, returning its id.\n */\nfunction useFloatingNodeId(customParentId) {\n  const id = useId();\n  const tree = useFloatingTree();\n  const reactParentId = useFloatingParentNodeId();\n  const parentId = customParentId || reactParentId;\n  index(() => {\n    const node = {\n      id,\n      parentId\n    };\n    tree == null ? void 0 : tree.addNode(node);\n    return () => {\n      tree == null ? void 0 : tree.removeNode(node);\n    };\n  }, [tree, id, parentId]);\n  return id;\n}\n\n/**\n * Provides parent node context for nested floating elements.\n * @see https://floating-ui.com/docs/FloatingTree\n */\nfunction FloatingNode(_ref) {\n  let {\n    children,\n    id\n  } = _ref;\n  const parentId = useFloatingParentNodeId();\n  return /*#__PURE__*/React.createElement(FloatingNodeContext.Provider, {\n    value: React.useMemo(() => ({\n      id,\n      parentId\n    }), [id, parentId])\n  }, children);\n}\n\n/**\n * Provides context for nested floating elements when they are not children of\n * each other on the DOM (i.e. portalled to a common node, rather than their\n * respective parent).\n * @see https://floating-ui.com/docs/FloatingTree\n */\nfunction FloatingTree(_ref2) {\n  let {\n    children\n  } = _ref2;\n  const nodesRef = React.useRef([]);\n  const addNode = React.useCallback(node => {\n    nodesRef.current = [...nodesRef.current, node];\n  }, []);\n  const removeNode = React.useCallback(node => {\n    nodesRef.current = nodesRef.current.filter(n => n !== node);\n  }, []);\n  const events = React.useState(() => createPubSub())[0];\n  return /*#__PURE__*/React.createElement(FloatingTreeContext.Provider, {\n    value: React.useMemo(() => ({\n      nodesRef,\n      addNode,\n      removeNode,\n      events\n    }), [nodesRef, addNode, removeNode, events])\n  }, children);\n}\n\nfunction createAttribute(name) {\n  return \"data-floating-ui-\" + name;\n}\n\nfunction useLatestRef(value) {\n  const ref = useRef(value);\n  index(() => {\n    ref.current = value;\n  });\n  return ref;\n}\n\nconst safePolygonIdentifier = /*#__PURE__*/createAttribute('safe-polygon');\nfunction getDelay(value, prop, pointerType) {\n  if (pointerType && !isMouseLikePointerType(pointerType)) {\n    return 0;\n  }\n  if (typeof value === 'number') {\n    return value;\n  }\n  return value == null ? void 0 : value[prop];\n}\n/**\n * Opens the floating element while hovering over the reference element, like\n * CSS `:hover`.\n * @see https://floating-ui.com/docs/useHover\n */\nfunction useHover(context, props) {\n  if (props === void 0) {\n    props = {};\n  }\n  const {\n    open,\n    onOpenChange,\n    dataRef,\n    events,\n    elements: {\n      domReference,\n      floating\n    },\n    refs\n  } = context;\n  const {\n    enabled = true,\n    delay = 0,\n    handleClose = null,\n    mouseOnly = false,\n    restMs = 0,\n    move = true\n  } = props;\n  const tree = useFloatingTree();\n  const parentId = useFloatingParentNodeId();\n  const handleCloseRef = useLatestRef(handleClose);\n  const delayRef = useLatestRef(delay);\n  const pointerTypeRef = React.useRef();\n  const timeoutRef = React.useRef();\n  const handlerRef = React.useRef();\n  const restTimeoutRef = React.useRef();\n  const blockMouseMoveRef = React.useRef(true);\n  const performedPointerEventsMutationRef = React.useRef(false);\n  const unbindMouseMoveRef = React.useRef(() => {});\n  const isHoverOpen = React.useCallback(() => {\n    var _dataRef$current$open;\n    const type = (_dataRef$current$open = dataRef.current.openEvent) == null ? void 0 : _dataRef$current$open.type;\n    return (type == null ? void 0 : type.includes('mouse')) && type !== 'mousedown';\n  }, [dataRef]);\n\n  // When dismissing before opening, clear the delay timeouts to cancel it\n  // from showing.\n  React.useEffect(() => {\n    if (!enabled) {\n      return;\n    }\n    function onDismiss() {\n      clearTimeout(timeoutRef.current);\n      clearTimeout(restTimeoutRef.current);\n      blockMouseMoveRef.current = true;\n    }\n    events.on('dismiss', onDismiss);\n    return () => {\n      events.off('dismiss', onDismiss);\n    };\n  }, [enabled, events]);\n  React.useEffect(() => {\n    if (!enabled || !handleCloseRef.current || !open) {\n      return;\n    }\n    function onLeave(event) {\n      if (isHoverOpen()) {\n        onOpenChange(false, event);\n      }\n    }\n    const html = getDocument(floating).documentElement;\n    html.addEventListener('mouseleave', onLeave);\n    return () => {\n      html.removeEventListener('mouseleave', onLeave);\n    };\n  }, [floating, open, onOpenChange, enabled, handleCloseRef, dataRef, isHoverOpen]);\n  const closeWithDelay = React.useCallback(function (event, runElseBranch) {\n    if (runElseBranch === void 0) {\n      runElseBranch = true;\n    }\n    const closeDelay = getDelay(delayRef.current, 'close', pointerTypeRef.current);\n    if (closeDelay && !handlerRef.current) {\n      clearTimeout(timeoutRef.current);\n      timeoutRef.current = setTimeout(() => onOpenChange(false, event), closeDelay);\n    } else if (runElseBranch) {\n      clearTimeout(timeoutRef.current);\n      onOpenChange(false, event);\n    }\n  }, [delayRef, onOpenChange]);\n  const cleanupMouseMoveHandler = React.useCallback(() => {\n    unbindMouseMoveRef.current();\n    handlerRef.current = undefined;\n  }, []);\n  const clearPointerEvents = React.useCallback(() => {\n    if (performedPointerEventsMutationRef.current) {\n      const body = getDocument(refs.floating.current).body;\n      body.style.pointerEvents = '';\n      body.removeAttribute(safePolygonIdentifier);\n      performedPointerEventsMutationRef.current = false;\n    }\n  }, [refs]);\n\n  // Registering the mouse events on the reference directly to bypass React's\n  // delegation system. If the cursor was on a disabled element and then entered\n  // the reference (no gap), `mouseenter` doesn't fire in the delegation system.\n  React.useEffect(() => {\n    if (!enabled) {\n      return;\n    }\n    function isClickLikeOpenEvent() {\n      return dataRef.current.openEvent ? ['click', 'mousedown'].includes(dataRef.current.openEvent.type) : false;\n    }\n    function onMouseEnter(event) {\n      clearTimeout(timeoutRef.current);\n      blockMouseMoveRef.current = false;\n      if (mouseOnly && !isMouseLikePointerType(pointerTypeRef.current) || restMs > 0 && getDelay(delayRef.current, 'open') === 0) {\n        return;\n      }\n      const openDelay = getDelay(delayRef.current, 'open', pointerTypeRef.current);\n      if (openDelay) {\n        timeoutRef.current = setTimeout(() => {\n          onOpenChange(true, event);\n        }, openDelay);\n      } else {\n        onOpenChange(true, event);\n      }\n    }\n    function onMouseLeave(event) {\n      if (isClickLikeOpenEvent()) {\n        return;\n      }\n      unbindMouseMoveRef.current();\n      const doc = getDocument(floating);\n      clearTimeout(restTimeoutRef.current);\n      if (handleCloseRef.current) {\n        // Prevent clearing `onScrollMouseLeave` timeout.\n        if (!open) {\n          clearTimeout(timeoutRef.current);\n        }\n        handlerRef.current = handleCloseRef.current({\n          ...context,\n          tree,\n          x: event.clientX,\n          y: event.clientY,\n          onClose() {\n            clearPointerEvents();\n            cleanupMouseMoveHandler();\n            // Should the event expose that it was closed by `safePolygon`?\n            closeWithDelay(event);\n          }\n        });\n        const handler = handlerRef.current;\n        doc.addEventListener('mousemove', handler);\n        unbindMouseMoveRef.current = () => {\n          doc.removeEventListener('mousemove', handler);\n        };\n        return;\n      }\n\n      // Allow interactivity without `safePolygon` on touch devices. With a\n      // pointer, a short close delay is an alternative, so it should work\n      // consistently.\n      const shouldClose = pointerTypeRef.current === 'touch' ? !contains(floating, event.relatedTarget) : true;\n      if (shouldClose) {\n        closeWithDelay(event);\n      }\n    }\n\n    // Ensure the floating element closes after scrolling even if the pointer\n    // did not move.\n    // https://github.com/floating-ui/floating-ui/discussions/1692\n    function onScrollMouseLeave(event) {\n      if (isClickLikeOpenEvent()) {\n        return;\n      }\n      handleCloseRef.current == null ? void 0 : handleCloseRef.current({\n        ...context,\n        tree,\n        x: event.clientX,\n        y: event.clientY,\n        onClose() {\n          clearPointerEvents();\n          cleanupMouseMoveHandler();\n          closeWithDelay(event);\n        }\n      })(event);\n    }\n    if (isElement(domReference)) {\n      const ref = domReference;\n      open && ref.addEventListener('mouseleave', onScrollMouseLeave);\n      floating == null ? void 0 : floating.addEventListener('mouseleave', onScrollMouseLeave);\n      move && ref.addEventListener('mousemove', onMouseEnter, {\n        once: true\n      });\n      ref.addEventListener('mouseenter', onMouseEnter);\n      ref.addEventListener('mouseleave', onMouseLeave);\n      return () => {\n        open && ref.removeEventListener('mouseleave', onScrollMouseLeave);\n        floating == null ? void 0 : floating.removeEventListener('mouseleave', onScrollMouseLeave);\n        move && ref.removeEventListener('mousemove', onMouseEnter);\n        ref.removeEventListener('mouseenter', onMouseEnter);\n        ref.removeEventListener('mouseleave', onMouseLeave);\n      };\n    }\n  }, [domReference, floating, enabled, context, mouseOnly, restMs, move, closeWithDelay, cleanupMouseMoveHandler, clearPointerEvents, onOpenChange, open, tree, delayRef, handleCloseRef, dataRef]);\n\n  // Block pointer-events of every element other than the reference and floating\n  // while the floating element is open and has a `handleClose` handler. Also\n  // handles nested floating elements.\n  // https://github.com/floating-ui/floating-ui/issues/1722\n  index(() => {\n    var _handleCloseRef$curre;\n    if (!enabled) {\n      return;\n    }\n    if (open && (_handleCloseRef$curre = handleCloseRef.current) != null && _handleCloseRef$curre.__options.blockPointerEvents && isHoverOpen()) {\n      const body = getDocument(floating).body;\n      body.setAttribute(safePolygonIdentifier, '');\n      body.style.pointerEvents = 'none';\n      performedPointerEventsMutationRef.current = true;\n      if (isElement(domReference) && floating) {\n        var _tree$nodesRef$curren, _tree$nodesRef$curren2;\n        const ref = domReference;\n        const parentFloating = tree == null ? void 0 : (_tree$nodesRef$curren = tree.nodesRef.current.find(node => node.id === parentId)) == null ? void 0 : (_tree$nodesRef$curren2 = _tree$nodesRef$curren.context) == null ? void 0 : _tree$nodesRef$curren2.elements.floating;\n        if (parentFloating) {\n          parentFloating.style.pointerEvents = '';\n        }\n        ref.style.pointerEvents = 'auto';\n        floating.style.pointerEvents = 'auto';\n        return () => {\n          ref.style.pointerEvents = '';\n          floating.style.pointerEvents = '';\n        };\n      }\n    }\n  }, [enabled, open, parentId, floating, domReference, tree, handleCloseRef, dataRef, isHoverOpen]);\n  index(() => {\n    if (!open) {\n      pointerTypeRef.current = undefined;\n      cleanupMouseMoveHandler();\n      clearPointerEvents();\n    }\n  }, [open, cleanupMouseMoveHandler, clearPointerEvents]);\n  React.useEffect(() => {\n    return () => {\n      cleanupMouseMoveHandler();\n      clearTimeout(timeoutRef.current);\n      clearTimeout(restTimeoutRef.current);\n      clearPointerEvents();\n    };\n  }, [enabled, domReference, cleanupMouseMoveHandler, clearPointerEvents]);\n  return React.useMemo(() => {\n    if (!enabled) {\n      return {};\n    }\n    function setPointerRef(event) {\n      pointerTypeRef.current = event.pointerType;\n    }\n    return {\n      reference: {\n        onPointerDown: setPointerRef,\n        onPointerEnter: setPointerRef,\n        onMouseMove(event) {\n          if (open || restMs === 0) {\n            return;\n          }\n          clearTimeout(restTimeoutRef.current);\n          restTimeoutRef.current = setTimeout(() => {\n            if (!blockMouseMoveRef.current) {\n              onOpenChange(true, event.nativeEvent);\n            }\n          }, restMs);\n        }\n      },\n      floating: {\n        onMouseEnter() {\n          clearTimeout(timeoutRef.current);\n        },\n        onMouseLeave(event) {\n          events.emit('dismiss', {\n            type: 'mouseLeave',\n            data: {\n              returnFocus: false\n            }\n          });\n          closeWithDelay(event.nativeEvent, false);\n        }\n      }\n    };\n  }, [events, enabled, restMs, open, onOpenChange, closeWithDelay]);\n}\n\nconst FloatingDelayGroupContext = /*#__PURE__*/React.createContext({\n  delay: 0,\n  initialDelay: 0,\n  timeoutMs: 0,\n  currentId: null,\n  setCurrentId: () => {},\n  setState: () => {},\n  isInstantPhase: false\n});\nconst useDelayGroupContext = () => React.useContext(FloatingDelayGroupContext);\n/**\n * Provides context for a group of floating elements that should share a\n * `delay`.\n * @see https://floating-ui.com/docs/FloatingDelayGroup\n */\nconst FloatingDelayGroup = _ref => {\n  let {\n    children,\n    delay,\n    timeoutMs = 0\n  } = _ref;\n  const [state, setState] = React.useReducer((prev, next) => ({\n    ...prev,\n    ...next\n  }), {\n    delay,\n    timeoutMs,\n    initialDelay: delay,\n    currentId: null,\n    isInstantPhase: false\n  });\n  const initialCurrentIdRef = React.useRef(null);\n  const setCurrentId = React.useCallback(currentId => {\n    setState({\n      currentId\n    });\n  }, []);\n  index(() => {\n    if (state.currentId) {\n      if (initialCurrentIdRef.current === null) {\n        initialCurrentIdRef.current = state.currentId;\n      } else {\n        setState({\n          isInstantPhase: true\n        });\n      }\n    } else {\n      setState({\n        isInstantPhase: false\n      });\n      initialCurrentIdRef.current = null;\n    }\n  }, [state.currentId]);\n  return /*#__PURE__*/React.createElement(FloatingDelayGroupContext.Provider, {\n    value: React.useMemo(() => ({\n      ...state,\n      setState,\n      setCurrentId\n    }), [state, setState, setCurrentId])\n  }, children);\n};\nconst useDelayGroup = (_ref2, _ref3) => {\n  let {\n    open,\n    onOpenChange\n  } = _ref2;\n  let {\n    id\n  } = _ref3;\n  const {\n    currentId,\n    setCurrentId,\n    initialDelay,\n    setState,\n    timeoutMs\n  } = useDelayGroupContext();\n  index(() => {\n    if (currentId) {\n      setState({\n        delay: {\n          open: 1,\n          close: getDelay(initialDelay, 'close')\n        }\n      });\n      if (currentId !== id) {\n        onOpenChange(false);\n      }\n    }\n  }, [id, onOpenChange, setState, currentId, initialDelay]);\n  index(() => {\n    function unset() {\n      onOpenChange(false);\n      setState({\n        delay: initialDelay,\n        currentId: null\n      });\n    }\n    if (!open && currentId === id) {\n      if (timeoutMs) {\n        const timeout = window.setTimeout(unset, timeoutMs);\n        return () => {\n          clearTimeout(timeout);\n        };\n      } else {\n        unset();\n      }\n    }\n  }, [open, setState, currentId, id, onOpenChange, initialDelay, timeoutMs]);\n  index(() => {\n    if (open) {\n      setCurrentId(id);\n    }\n  }, [open, setCurrentId, id]);\n};\n\nfunction getAncestors(nodes, id) {\n  var _nodes$find;\n  let allAncestors = [];\n  let currentParentId = (_nodes$find = nodes.find(node => node.id === id)) == null ? void 0 : _nodes$find.parentId;\n  while (currentParentId) {\n    const currentNode = nodes.find(node => node.id === currentParentId);\n    currentParentId = currentNode == null ? void 0 : currentNode.parentId;\n    if (currentNode) {\n      allAncestors = allAncestors.concat(currentNode);\n    }\n  }\n  return allAncestors;\n}\n\nfunction getChildren(nodes, id) {\n  let allChildren = nodes.filter(node => {\n    var _node$context;\n    return node.parentId === id && ((_node$context = node.context) == null ? void 0 : _node$context.open);\n  });\n  let currentChildren = allChildren;\n  while (currentChildren.length) {\n    currentChildren = nodes.filter(node => {\n      var _currentChildren;\n      return (_currentChildren = currentChildren) == null ? void 0 : _currentChildren.some(n => {\n        var _node$context2;\n        return node.parentId === n.id && ((_node$context2 = node.context) == null ? void 0 : _node$context2.open);\n      });\n    });\n    allChildren = allChildren.concat(currentChildren);\n  }\n  return allChildren;\n}\nfunction getDeepestNode(nodes, id) {\n  let deepestNodeId;\n  let maxDepth = -1;\n  function findDeepest(nodeId, depth) {\n    if (depth > maxDepth) {\n      deepestNodeId = nodeId;\n      maxDepth = depth;\n    }\n    const children = getChildren(nodes, nodeId);\n    children.forEach(child => {\n      findDeepest(child.id, depth + 1);\n    });\n  }\n  findDeepest(id, 0);\n  return nodes.find(node => node.id === deepestNodeId);\n}\n\n// Modified to add conditional `aria-hidden` support:\n// https://github.com/theKashey/aria-hidden/blob/9220c8f4a4fd35f63bee5510a9f41a37264382d4/src/index.ts\nlet counterMap = /*#__PURE__*/new WeakMap();\nlet uncontrolledElementsSet = /*#__PURE__*/new WeakSet();\nlet markerMap = {};\nlet lockCount = 0;\nconst supportsInert = () => typeof HTMLElement !== 'undefined' && 'inert' in HTMLElement.prototype;\nconst unwrapHost = node => node && (node.host || unwrapHost(node.parentNode));\nconst correctElements = (parent, targets) => targets.map(target => {\n  if (parent.contains(target)) {\n    return target;\n  }\n  const correctedTarget = unwrapHost(target);\n  if (parent.contains(correctedTarget)) {\n    return correctedTarget;\n  }\n  return null;\n}).filter(x => x != null);\nfunction applyAttributeToOthers(uncorrectedAvoidElements, body, ariaHidden, inert) {\n  const markerName = 'data-floating-ui-inert';\n  const controlAttribute = inert ? 'inert' : ariaHidden ? 'aria-hidden' : null;\n  const avoidElements = correctElements(body, uncorrectedAvoidElements);\n  const elementsToKeep = new Set();\n  const elementsToStop = new Set(avoidElements);\n  const hiddenElements = [];\n  if (!markerMap[markerName]) {\n    markerMap[markerName] = new WeakMap();\n  }\n  const markerCounter = markerMap[markerName];\n  avoidElements.forEach(keep);\n  deep(body);\n  elementsToKeep.clear();\n  function keep(el) {\n    if (!el || elementsToKeep.has(el)) {\n      return;\n    }\n    elementsToKeep.add(el);\n    el.parentNode && keep(el.parentNode);\n  }\n  function deep(parent) {\n    if (!parent || elementsToStop.has(parent)) {\n      return;\n    }\n    Array.prototype.forEach.call(parent.children, node => {\n      if (elementsToKeep.has(node)) {\n        deep(node);\n      } else {\n        const attr = controlAttribute ? node.getAttribute(controlAttribute) : null;\n        const alreadyHidden = attr !== null && attr !== 'false';\n        const counterValue = (counterMap.get(node) || 0) + 1;\n        const markerValue = (markerCounter.get(node) || 0) + 1;\n        counterMap.set(node, counterValue);\n        markerCounter.set(node, markerValue);\n        hiddenElements.push(node);\n        if (counterValue === 1 && alreadyHidden) {\n          uncontrolledElementsSet.add(node);\n        }\n        if (markerValue === 1) {\n          node.setAttribute(markerName, '');\n        }\n        if (!alreadyHidden && controlAttribute) {\n          node.setAttribute(controlAttribute, 'true');\n        }\n      }\n    });\n  }\n  lockCount++;\n  return () => {\n    hiddenElements.forEach(element => {\n      const counterValue = (counterMap.get(element) || 0) - 1;\n      const markerValue = (markerCounter.get(element) || 0) - 1;\n      counterMap.set(element, counterValue);\n      markerCounter.set(element, markerValue);\n      if (!counterValue) {\n        if (!uncontrolledElementsSet.has(element) && controlAttribute) {\n          element.removeAttribute(controlAttribute);\n        }\n        uncontrolledElementsSet.delete(element);\n      }\n      if (!markerValue) {\n        element.removeAttribute(markerName);\n      }\n    });\n    lockCount--;\n    if (!lockCount) {\n      counterMap = new WeakMap();\n      counterMap = new WeakMap();\n      uncontrolledElementsSet = new WeakSet();\n      markerMap = {};\n    }\n  };\n}\nfunction markOthers(avoidElements, ariaHidden, inert) {\n  if (ariaHidden === void 0) {\n    ariaHidden = false;\n  }\n  if (inert === void 0) {\n    inert = false;\n  }\n  const body = getDocument(avoidElements[0]).body;\n  return applyAttributeToOthers(avoidElements.concat(Array.from(body.querySelectorAll('[aria-live]'))), body, ariaHidden, inert);\n}\n\nconst getTabbableOptions = () => ({\n  getShadowRoot: true,\n  displayCheck:\n  // JSDOM does not support the `tabbable` library. To solve this we can\n  // check if `ResizeObserver` is a real function (not polyfilled), which\n  // determines if the current environment is JSDOM-like.\n  typeof ResizeObserver === 'function' && ResizeObserver.toString().includes('[native code]') ? 'full' : 'none'\n});\nfunction getTabbableIn(container, direction) {\n  const allTabbable = tabbable(container, getTabbableOptions());\n  if (direction === 'prev') {\n    allTabbable.reverse();\n  }\n  const activeIndex = allTabbable.indexOf(activeElement(getDocument(container)));\n  const nextTabbableElements = allTabbable.slice(activeIndex + 1);\n  return nextTabbableElements[0];\n}\nfunction getNextTabbable() {\n  return getTabbableIn(document.body, 'next');\n}\nfunction getPreviousTabbable() {\n  return getTabbableIn(document.body, 'prev');\n}\nfunction isOutsideEvent(event, container) {\n  const containerElement = container || event.currentTarget;\n  const relatedTarget = event.relatedTarget;\n  return !relatedTarget || !contains(containerElement, relatedTarget);\n}\nfunction disableFocusInside(container) {\n  const tabbableElements = tabbable(container, getTabbableOptions());\n  tabbableElements.forEach(element => {\n    element.dataset.tabindex = element.getAttribute('tabindex') || '';\n    element.setAttribute('tabindex', '-1');\n  });\n}\nfunction enableFocusInside(container) {\n  const elements = container.querySelectorAll('[data-tabindex]');\n  elements.forEach(element => {\n    const tabindex = element.dataset.tabindex;\n    delete element.dataset.tabindex;\n    if (tabindex) {\n      element.setAttribute('tabindex', tabindex);\n    } else {\n      element.removeAttribute('tabindex');\n    }\n  });\n}\n\n// See Diego Haz's Sandbox for making this logic work well on Safari/iOS:\n// https://codesandbox.io/s/tabbable-portal-f4tng?file=/src/FocusTrap.tsx\n\nconst HIDDEN_STYLES = {\n  border: 0,\n  clip: 'rect(0 0 0 0)',\n  height: '1px',\n  margin: '-1px',\n  overflow: 'hidden',\n  padding: 0,\n  position: 'fixed',\n  whiteSpace: 'nowrap',\n  width: '1px',\n  top: 0,\n  left: 0\n};\nlet timeoutId;\nfunction setActiveElementOnTab(event) {\n  if (event.key === 'Tab') {\n    event.target;\n    clearTimeout(timeoutId);\n  }\n}\nconst FocusGuard = /*#__PURE__*/React.forwardRef(function FocusGuard(props, ref) {\n  const [role, setRole] = React.useState();\n  index(() => {\n    if (isSafari()) {\n      // Unlike other screen readers such as NVDA and JAWS, the virtual cursor\n      // on VoiceOver does trigger the onFocus event, so we can use the focus\n      // trap element. On Safari, only buttons trigger the onFocus event.\n      // NB: \"group\" role in the Sandbox no longer appears to work, must be a\n      // button role.\n      setRole('button');\n    }\n    document.addEventListener('keydown', setActiveElementOnTab);\n    return () => {\n      document.removeEventListener('keydown', setActiveElementOnTab);\n    };\n  }, []);\n  const restProps = {\n    ref,\n    tabIndex: 0,\n    // Role is only for VoiceOver\n    role,\n    'aria-hidden': role ? undefined : true,\n    [createAttribute('focus-guard')]: '',\n    style: HIDDEN_STYLES\n  };\n  return /*#__PURE__*/React.createElement(\"span\", _extends({}, props, restProps));\n});\n\nconst PortalContext = /*#__PURE__*/React.createContext(null);\nfunction useFloatingPortalNode(_temp) {\n  let {\n    id,\n    root\n  } = _temp === void 0 ? {} : _temp;\n  const [portalNode, setPortalNode] = React.useState(null);\n  const uniqueId = useId();\n  const portalContext = usePortalContext();\n  const data = React.useMemo(() => ({\n    id,\n    root,\n    portalContext,\n    uniqueId\n  }), [id, root, portalContext, uniqueId]);\n  const dataRef = React.useRef();\n  index(() => {\n    return () => {\n      portalNode == null ? void 0 : portalNode.remove();\n    };\n  }, [portalNode, data]);\n  index(() => {\n    if (dataRef.current === data) return;\n    dataRef.current = data;\n    const {\n      id,\n      root,\n      portalContext,\n      uniqueId\n    } = data;\n    const existingIdRoot = id ? document.getElementById(id) : null;\n    const attr = createAttribute('portal');\n    if (existingIdRoot) {\n      const subRoot = document.createElement('div');\n      subRoot.id = uniqueId;\n      subRoot.setAttribute(attr, '');\n      existingIdRoot.appendChild(subRoot);\n      setPortalNode(subRoot);\n    } else {\n      let container = root || (portalContext == null ? void 0 : portalContext.portalNode);\n      if (container && !isElement(container)) container = container.current;\n      container = container || document.body;\n      let idWrapper = null;\n      if (id) {\n        idWrapper = document.createElement('div');\n        idWrapper.id = id;\n        container.appendChild(idWrapper);\n      }\n      const subRoot = document.createElement('div');\n      subRoot.id = uniqueId;\n      subRoot.setAttribute(attr, '');\n      container = idWrapper || container;\n      container.appendChild(subRoot);\n      setPortalNode(subRoot);\n    }\n  }, [data]);\n  return portalNode;\n}\n/**\n * Portals the floating element into a given container element — by default,\n * outside of the app root and into the body.\n * @see https://floating-ui.com/docs/FloatingPortal\n */\nfunction FloatingPortal(_ref) {\n  let {\n    children,\n    id,\n    root = null,\n    preserveTabOrder = true\n  } = _ref;\n  const portalNode = useFloatingPortalNode({\n    id,\n    root\n  });\n  const [focusManagerState, setFocusManagerState] = React.useState(null);\n  const beforeOutsideRef = React.useRef(null);\n  const afterOutsideRef = React.useRef(null);\n  const beforeInsideRef = React.useRef(null);\n  const afterInsideRef = React.useRef(null);\n  const shouldRenderGuards =\n  // The FocusManager and therefore floating element are currently open/\n  // rendered.\n  !!focusManagerState &&\n  // Guards are only for non-modal focus management.\n  !focusManagerState.modal &&\n  // Don't render if unmount is transitioning.\n  focusManagerState.open && preserveTabOrder && !!(root || portalNode);\n\n  // https://codesandbox.io/s/tabbable-portal-f4tng?file=/src/TabbablePortal.tsx\n  React.useEffect(() => {\n    if (!portalNode || !preserveTabOrder || focusManagerState != null && focusManagerState.modal) {\n      return;\n    }\n\n    // Make sure elements inside the portal element are tabbable only when the\n    // portal has already been focused, either by tabbing into a focus trap\n    // element outside or using the mouse.\n    function onFocus(event) {\n      if (portalNode && isOutsideEvent(event)) {\n        const focusing = event.type === 'focusin';\n        const manageFocus = focusing ? enableFocusInside : disableFocusInside;\n        manageFocus(portalNode);\n      }\n    }\n    // Listen to the event on the capture phase so they run before the focus\n    // trap elements onFocus prop is called.\n    portalNode.addEventListener('focusin', onFocus, true);\n    portalNode.addEventListener('focusout', onFocus, true);\n    return () => {\n      portalNode.removeEventListener('focusin', onFocus, true);\n      portalNode.removeEventListener('focusout', onFocus, true);\n    };\n  }, [portalNode, preserveTabOrder, focusManagerState == null ? void 0 : focusManagerState.modal]);\n  return /*#__PURE__*/React.createElement(PortalContext.Provider, {\n    value: React.useMemo(() => ({\n      preserveTabOrder,\n      beforeOutsideRef,\n      afterOutsideRef,\n      beforeInsideRef,\n      afterInsideRef,\n      portalNode,\n      setFocusManagerState\n    }), [preserveTabOrder, portalNode])\n  }, shouldRenderGuards && portalNode && /*#__PURE__*/React.createElement(FocusGuard, {\n    \"data-type\": \"outside\",\n    ref: beforeOutsideRef,\n    onFocus: event => {\n      if (isOutsideEvent(event, portalNode)) {\n        var _beforeInsideRef$curr;\n        (_beforeInsideRef$curr = beforeInsideRef.current) == null ? void 0 : _beforeInsideRef$curr.focus();\n      } else {\n        const prevTabbable = getPreviousTabbable() || (focusManagerState == null ? void 0 : focusManagerState.refs.domReference.current);\n        prevTabbable == null ? void 0 : prevTabbable.focus();\n      }\n    }\n  }), shouldRenderGuards && portalNode && /*#__PURE__*/React.createElement(\"span\", {\n    \"aria-owns\": portalNode.id,\n    style: HIDDEN_STYLES\n  }), portalNode && /*#__PURE__*/createPortal(children, portalNode), shouldRenderGuards && portalNode && /*#__PURE__*/React.createElement(FocusGuard, {\n    \"data-type\": \"outside\",\n    ref: afterOutsideRef,\n    onFocus: event => {\n      if (isOutsideEvent(event, portalNode)) {\n        var _afterInsideRef$curre;\n        (_afterInsideRef$curre = afterInsideRef.current) == null ? void 0 : _afterInsideRef$curre.focus();\n      } else {\n        const nextTabbable = getNextTabbable() || (focusManagerState == null ? void 0 : focusManagerState.refs.domReference.current);\n        nextTabbable == null ? void 0 : nextTabbable.focus();\n        (focusManagerState == null ? void 0 : focusManagerState.closeOnFocusOut) && (focusManagerState == null ? void 0 : focusManagerState.onOpenChange(false, event.nativeEvent));\n      }\n    }\n  }));\n}\nconst usePortalContext = () => React.useContext(PortalContext);\n\nconst VisuallyHiddenDismiss = /*#__PURE__*/React.forwardRef(function VisuallyHiddenDismiss(props, ref) {\n  return /*#__PURE__*/React.createElement(\"button\", _extends({}, props, {\n    type: \"button\",\n    ref: ref,\n    tabIndex: -1,\n    style: HIDDEN_STYLES\n  }));\n});\n/**\n * Provides focus management for the floating element.\n * @see https://floating-ui.com/docs/FloatingFocusManager\n */\nfunction FloatingFocusManager(props) {\n  const {\n    context,\n    children,\n    disabled = false,\n    order = ['content'],\n    guards: _guards = true,\n    initialFocus = 0,\n    returnFocus = true,\n    modal = true,\n    visuallyHiddenDismiss = false,\n    closeOnFocusOut = true\n  } = props;\n  const {\n    open,\n    refs,\n    nodeId,\n    onOpenChange,\n    events,\n    dataRef,\n    elements: {\n      domReference,\n      floating\n    }\n  } = context;\n\n  // Force the guards to be rendered if the `inert` attribute is not supported.\n  const guards = supportsInert() ? _guards : true;\n  const orderRef = useLatestRef(order);\n  const initialFocusRef = useLatestRef(initialFocus);\n  const returnFocusRef = useLatestRef(returnFocus);\n  const tree = useFloatingTree();\n  const portalContext = usePortalContext();\n\n  // Controlled by `useListNavigation`.\n  const ignoreInitialFocus = typeof initialFocus === 'number' && initialFocus < 0;\n  const startDismissButtonRef = React.useRef(null);\n  const endDismissButtonRef = React.useRef(null);\n  const preventReturnFocusRef = React.useRef(false);\n  const previouslyFocusedElementRef = React.useRef(null);\n  const isPointerDownRef = React.useRef(false);\n  const isInsidePortal = portalContext != null;\n\n  // If the reference is a combobox and is typeable (e.g. input/textarea),\n  // there are different focus semantics. The guards should not be rendered, but\n  // aria-hidden should be applied to all nodes still. Further, the visually\n  // hidden dismiss button should only appear at the end of the list, not the\n  // start.\n  const isUntrappedTypeableCombobox = domReference && domReference.getAttribute('role') === 'combobox' && isTypeableElement(domReference) && ignoreInitialFocus;\n  const getTabbableContent = React.useCallback(function (container) {\n    if (container === void 0) {\n      container = floating;\n    }\n    return container ? tabbable(container, getTabbableOptions()) : [];\n  }, [floating]);\n  const getTabbableElements = React.useCallback(container => {\n    const content = getTabbableContent(container);\n    return orderRef.current.map(type => {\n      if (domReference && type === 'reference') {\n        return domReference;\n      }\n      if (floating && type === 'floating') {\n        return floating;\n      }\n      return content;\n    }).filter(Boolean).flat();\n  }, [domReference, floating, orderRef, getTabbableContent]);\n  React.useEffect(() => {\n    if (disabled || !modal) return;\n    function onKeyDown(event) {\n      if (event.key === 'Tab') {\n        // The focus guards have nothing to focus, so we need to stop the event.\n        if (contains(floating, activeElement(getDocument(floating))) && getTabbableContent().length === 0 && !isUntrappedTypeableCombobox) {\n          stopEvent(event);\n        }\n        const els = getTabbableElements();\n        const target = getTarget(event);\n        if (orderRef.current[0] === 'reference' && target === domReference) {\n          stopEvent(event);\n          if (event.shiftKey) {\n            enqueueFocus(els[els.length - 1]);\n          } else {\n            enqueueFocus(els[1]);\n          }\n        }\n        if (orderRef.current[1] === 'floating' && target === floating && event.shiftKey) {\n          stopEvent(event);\n          enqueueFocus(els[0]);\n        }\n      }\n    }\n    const doc = getDocument(floating);\n    doc.addEventListener('keydown', onKeyDown);\n    return () => {\n      doc.removeEventListener('keydown', onKeyDown);\n    };\n  }, [disabled, domReference, floating, modal, orderRef, refs, isUntrappedTypeableCombobox, getTabbableContent, getTabbableElements]);\n  React.useEffect(() => {\n    if (disabled || !closeOnFocusOut) return;\n\n    // In Safari, buttons lose focus when pressing them.\n    function handlePointerDown() {\n      isPointerDownRef.current = true;\n      setTimeout(() => {\n        isPointerDownRef.current = false;\n      });\n    }\n    function handleFocusOutside(event) {\n      const relatedTarget = event.relatedTarget;\n      queueMicrotask(() => {\n        const movedToUnrelatedNode = !(contains(domReference, relatedTarget) || contains(floating, relatedTarget) || contains(relatedTarget, floating) || contains(portalContext == null ? void 0 : portalContext.portalNode, relatedTarget) || relatedTarget != null && relatedTarget.hasAttribute(createAttribute('focus-guard')) || tree && (getChildren(tree.nodesRef.current, nodeId).find(node => {\n          var _node$context, _node$context2;\n          return contains((_node$context = node.context) == null ? void 0 : _node$context.elements.floating, relatedTarget) || contains((_node$context2 = node.context) == null ? void 0 : _node$context2.elements.domReference, relatedTarget);\n        }) || getAncestors(tree.nodesRef.current, nodeId).find(node => {\n          var _node$context3, _node$context4;\n          return ((_node$context3 = node.context) == null ? void 0 : _node$context3.elements.floating) === relatedTarget || ((_node$context4 = node.context) == null ? void 0 : _node$context4.elements.domReference) === relatedTarget;\n        })));\n\n        // Focus did not move inside the floating tree, and there are no tabbable\n        // portal guards to handle closing.\n        if (relatedTarget && movedToUnrelatedNode && !isPointerDownRef.current &&\n        // Fix React 18 Strict Mode returnFocus due to double rendering.\n        relatedTarget !== previouslyFocusedElementRef.current) {\n          preventReturnFocusRef.current = true;\n          onOpenChange(false, event);\n        }\n      });\n    }\n    if (floating && isHTMLElement(domReference)) {\n      domReference.addEventListener('focusout', handleFocusOutside);\n      domReference.addEventListener('pointerdown', handlePointerDown);\n      !modal && floating.addEventListener('focusout', handleFocusOutside);\n      return () => {\n        domReference.removeEventListener('focusout', handleFocusOutside);\n        domReference.removeEventListener('pointerdown', handlePointerDown);\n        !modal && floating.removeEventListener('focusout', handleFocusOutside);\n      };\n    }\n  }, [disabled, domReference, floating, modal, nodeId, tree, portalContext, onOpenChange, closeOnFocusOut]);\n  React.useEffect(() => {\n    var _portalContext$portal;\n    if (disabled) return;\n\n    // Don't hide portals nested within the parent portal.\n    const portalNodes = Array.from((portalContext == null ? void 0 : (_portalContext$portal = portalContext.portalNode) == null ? void 0 : _portalContext$portal.querySelectorAll(\"[\" + createAttribute('portal') + \"]\")) || []);\n    if (floating) {\n      const insideElements = [floating, ...portalNodes, startDismissButtonRef.current, endDismissButtonRef.current, orderRef.current.includes('reference') || isUntrappedTypeableCombobox ? domReference : null].filter(x => x != null);\n      const cleanup = modal ? markOthers(insideElements, guards, !guards) : markOthers(insideElements);\n      return () => {\n        cleanup();\n      };\n    }\n  }, [disabled, domReference, floating, modal, orderRef, portalContext, isUntrappedTypeableCombobox, guards]);\n  index(() => {\n    if (disabled || !floating) return;\n    const doc = getDocument(floating);\n    const previouslyFocusedElement = activeElement(doc);\n\n    // Wait for any layout effect state setters to execute to set `tabIndex`.\n    queueMicrotask(() => {\n      const focusableElements = getTabbableElements(floating);\n      const initialFocusValue = initialFocusRef.current;\n      const elToFocus = (typeof initialFocusValue === 'number' ? focusableElements[initialFocusValue] : initialFocusValue.current) || floating;\n      const focusAlreadyInsideFloatingEl = contains(floating, previouslyFocusedElement);\n      if (!ignoreInitialFocus && !focusAlreadyInsideFloatingEl && open) {\n        enqueueFocus(elToFocus, {\n          preventScroll: elToFocus === floating\n        });\n      }\n    });\n  }, [disabled, open, floating, ignoreInitialFocus, getTabbableElements, initialFocusRef]);\n  index(() => {\n    if (disabled || !floating) return;\n    let preventReturnFocusScroll = false;\n    const doc = getDocument(floating);\n    const previouslyFocusedElement = activeElement(doc);\n    const contextData = dataRef.current;\n    previouslyFocusedElementRef.current = previouslyFocusedElement;\n\n    // Dismissing via outside press should always ignore `returnFocus` to\n    // prevent unwanted scrolling.\n    function onDismiss(payload) {\n      if (payload.type === 'escapeKey' && refs.domReference.current) {\n        previouslyFocusedElementRef.current = refs.domReference.current;\n      }\n      if (['referencePress', 'escapeKey'].includes(payload.type)) {\n        return;\n      }\n      const returnFocus = payload.data.returnFocus;\n      if (typeof returnFocus === 'object') {\n        preventReturnFocusRef.current = false;\n        preventReturnFocusScroll = returnFocus.preventScroll;\n      } else {\n        preventReturnFocusRef.current = !returnFocus;\n      }\n    }\n    events.on('dismiss', onDismiss);\n    return () => {\n      events.off('dismiss', onDismiss);\n      const activeEl = activeElement(doc);\n      const shouldFocusReference = contains(floating, activeEl) || tree && getChildren(tree.nodesRef.current, nodeId).some(node => {\n        var _node$context5;\n        return contains((_node$context5 = node.context) == null ? void 0 : _node$context5.elements.floating, activeEl);\n      }) || contextData.openEvent && ['click', 'mousedown'].includes(contextData.openEvent.type);\n      if (shouldFocusReference && refs.domReference.current) {\n        previouslyFocusedElementRef.current = refs.domReference.current;\n      }\n      if (\n      // eslint-disable-next-line react-hooks/exhaustive-deps\n      returnFocusRef.current && isHTMLElement(previouslyFocusedElementRef.current) && !preventReturnFocusRef.current) {\n        enqueueFocus(previouslyFocusedElementRef.current, {\n          // When dismissing nested floating elements, by the time the rAF has\n          // executed, the menus will all have been unmounted. When they try\n          // to get focused, the calls get ignored — leaving the root\n          // reference focused as desired.\n          cancelPrevious: false,\n          preventScroll: preventReturnFocusScroll\n        });\n      }\n    };\n  }, [disabled, floating, returnFocusRef, dataRef, refs, events, tree, nodeId]);\n\n  // Synchronize the `context` & `modal` value to the FloatingPortal context.\n  // It will decide whether or not it needs to render its own guards.\n  index(() => {\n    if (disabled || !portalContext) return;\n    portalContext.setFocusManagerState({\n      modal,\n      closeOnFocusOut,\n      open,\n      onOpenChange,\n      refs\n    });\n    return () => {\n      portalContext.setFocusManagerState(null);\n    };\n  }, [disabled, portalContext, modal, open, onOpenChange, refs, closeOnFocusOut]);\n  index(() => {\n    if (disabled) return;\n    if (floating && typeof MutationObserver === 'function' && !ignoreInitialFocus) {\n      const handleMutation = () => {\n        const tabIndex = floating.getAttribute('tabindex');\n        if (orderRef.current.includes('floating') || activeElement(getDocument(floating)) !== refs.domReference.current && getTabbableContent().length === 0) {\n          if (tabIndex !== '0') {\n            floating.setAttribute('tabindex', '0');\n          }\n        } else if (tabIndex !== '-1') {\n          floating.setAttribute('tabindex', '-1');\n        }\n      };\n      handleMutation();\n      const observer = new MutationObserver(handleMutation);\n      observer.observe(floating, {\n        childList: true,\n        subtree: true,\n        attributes: true\n      });\n      return () => {\n        observer.disconnect();\n      };\n    }\n  }, [disabled, floating, refs, orderRef, getTabbableContent, ignoreInitialFocus]);\n  function renderDismissButton(location) {\n    if (disabled || !visuallyHiddenDismiss || !modal) {\n      return null;\n    }\n    return /*#__PURE__*/React.createElement(VisuallyHiddenDismiss, {\n      ref: location === 'start' ? startDismissButtonRef : endDismissButtonRef,\n      onClick: event => onOpenChange(false, event.nativeEvent)\n    }, typeof visuallyHiddenDismiss === 'string' ? visuallyHiddenDismiss : 'Dismiss');\n  }\n  const shouldRenderGuards = !disabled && guards && !isUntrappedTypeableCombobox && (isInsidePortal || modal);\n  return /*#__PURE__*/React.createElement(React.Fragment, null, shouldRenderGuards && /*#__PURE__*/React.createElement(FocusGuard, {\n    \"data-type\": \"inside\",\n    ref: portalContext == null ? void 0 : portalContext.beforeInsideRef,\n    onFocus: event => {\n      if (modal) {\n        const els = getTabbableElements();\n        enqueueFocus(order[0] === 'reference' ? els[0] : els[els.length - 1]);\n      } else if (portalContext != null && portalContext.preserveTabOrder && portalContext.portalNode) {\n        preventReturnFocusRef.current = false;\n        if (isOutsideEvent(event, portalContext.portalNode)) {\n          const nextTabbable = getNextTabbable() || domReference;\n          nextTabbable == null ? void 0 : nextTabbable.focus();\n        } else {\n          var _portalContext$before;\n          (_portalContext$before = portalContext.beforeOutsideRef.current) == null ? void 0 : _portalContext$before.focus();\n        }\n      }\n    }\n  }), !isUntrappedTypeableCombobox && renderDismissButton('start'), children, renderDismissButton('end'), shouldRenderGuards && /*#__PURE__*/React.createElement(FocusGuard, {\n    \"data-type\": \"inside\",\n    ref: portalContext == null ? void 0 : portalContext.afterInsideRef,\n    onFocus: event => {\n      if (modal) {\n        enqueueFocus(getTabbableElements()[0]);\n      } else if (portalContext != null && portalContext.preserveTabOrder && portalContext.portalNode) {\n        if (closeOnFocusOut) {\n          preventReturnFocusRef.current = true;\n        }\n        if (isOutsideEvent(event, portalContext.portalNode)) {\n          const prevTabbable = getPreviousTabbable() || domReference;\n          prevTabbable == null ? void 0 : prevTabbable.focus();\n        } else {\n          var _portalContext$afterO;\n          (_portalContext$afterO = portalContext.afterOutsideRef.current) == null ? void 0 : _portalContext$afterO.focus();\n        }\n      }\n    }\n  }));\n}\n\nconst activeLocks = /*#__PURE__*/new Set();\n\n/**\n * Provides base styling for a fixed overlay element to dim content or block\n * pointer events behind a floating element.\n * It's a regular `<div>`, so it can be styled via any CSS solution you prefer.\n * @see https://floating-ui.com/docs/FloatingOverlay\n */\nconst FloatingOverlay = /*#__PURE__*/React.forwardRef(function FloatingOverlay(_ref, ref) {\n  let {\n    lockScroll = false,\n    ...rest\n  } = _ref;\n  const lockId = useId();\n  index(() => {\n    if (!lockScroll) return;\n    activeLocks.add(lockId);\n    const isIOS = /iP(hone|ad|od)|iOS/.test(getPlatform());\n    const bodyStyle = document.body.style;\n    // RTL <body> scrollbar\n    const scrollbarX = Math.round(document.documentElement.getBoundingClientRect().left) + document.documentElement.scrollLeft;\n    const paddingProp = scrollbarX ? 'paddingLeft' : 'paddingRight';\n    const scrollbarWidth = window.innerWidth - document.documentElement.clientWidth;\n    const scrollX = bodyStyle.left ? parseFloat(bodyStyle.left) : window.pageXOffset;\n    const scrollY = bodyStyle.top ? parseFloat(bodyStyle.top) : window.pageYOffset;\n    bodyStyle.overflow = 'hidden';\n    if (scrollbarWidth) {\n      bodyStyle[paddingProp] = scrollbarWidth + \"px\";\n    }\n\n    // Only iOS doesn't respect `overflow: hidden` on document.body, and this\n    // technique has fewer side effects.\n    if (isIOS) {\n      var _window$visualViewpor, _window$visualViewpor2;\n      // iOS 12 does not support `visualViewport`.\n      const offsetLeft = ((_window$visualViewpor = window.visualViewport) == null ? void 0 : _window$visualViewpor.offsetLeft) || 0;\n      const offsetTop = ((_window$visualViewpor2 = window.visualViewport) == null ? void 0 : _window$visualViewpor2.offsetTop) || 0;\n      Object.assign(bodyStyle, {\n        position: 'fixed',\n        top: -(scrollY - Math.floor(offsetTop)) + \"px\",\n        left: -(scrollX - Math.floor(offsetLeft)) + \"px\",\n        right: '0'\n      });\n    }\n    return () => {\n      activeLocks.delete(lockId);\n      if (activeLocks.size === 0) {\n        Object.assign(bodyStyle, {\n          overflow: '',\n          [paddingProp]: ''\n        });\n        if (isIOS) {\n          Object.assign(bodyStyle, {\n            position: '',\n            top: '',\n            left: '',\n            right: ''\n          });\n          window.scrollTo(scrollX, scrollY);\n        }\n      }\n    };\n  }, [lockId, lockScroll]);\n  return /*#__PURE__*/React.createElement(\"div\", _extends({\n    ref: ref\n  }, rest, {\n    style: {\n      position: 'fixed',\n      overflow: 'auto',\n      top: 0,\n      right: 0,\n      bottom: 0,\n      left: 0,\n      ...rest.style\n    }\n  }));\n});\n\nfunction isButtonTarget(event) {\n  return isHTMLElement(event.target) && event.target.tagName === 'BUTTON';\n}\nfunction isSpaceIgnored(element) {\n  return isTypeableElement(element);\n}\n/**\n * Opens or closes the floating element when clicking the reference element.\n * @see https://floating-ui.com/docs/useClick\n */\nfunction useClick(context, props) {\n  if (props === void 0) {\n    props = {};\n  }\n  const {\n    open,\n    onOpenChange,\n    dataRef,\n    elements: {\n      domReference\n    }\n  } = context;\n  const {\n    enabled = true,\n    event: eventOption = 'click',\n    toggle = true,\n    ignoreMouse = false,\n    keyboardHandlers = true\n  } = props;\n  const pointerTypeRef = React.useRef();\n  const didKeyDownRef = React.useRef(false);\n  return React.useMemo(() => {\n    if (!enabled) return {};\n    return {\n      reference: {\n        onPointerDown(event) {\n          pointerTypeRef.current = event.pointerType;\n        },\n        onMouseDown(event) {\n          // Ignore all buttons except for the \"main\" button.\n          // https://developer.mozilla.org/en-US/docs/Web/API/MouseEvent/button\n          if (event.button !== 0) {\n            return;\n          }\n          if (isMouseLikePointerType(pointerTypeRef.current, true) && ignoreMouse) {\n            return;\n          }\n          if (eventOption === 'click') {\n            return;\n          }\n          if (open && toggle && (dataRef.current.openEvent ? dataRef.current.openEvent.type === 'mousedown' : true)) {\n            onOpenChange(false, event.nativeEvent);\n          } else {\n            // Prevent stealing focus from the floating element\n            event.preventDefault();\n            onOpenChange(true, event.nativeEvent);\n          }\n        },\n        onClick(event) {\n          if (eventOption === 'mousedown' && pointerTypeRef.current) {\n            pointerTypeRef.current = undefined;\n            return;\n          }\n          if (isMouseLikePointerType(pointerTypeRef.current, true) && ignoreMouse) {\n            return;\n          }\n          if (open && toggle && (dataRef.current.openEvent ? dataRef.current.openEvent.type === 'click' : true)) {\n            onOpenChange(false, event.nativeEvent);\n          } else {\n            onOpenChange(true, event.nativeEvent);\n          }\n        },\n        onKeyDown(event) {\n          pointerTypeRef.current = undefined;\n          if (event.defaultPrevented || !keyboardHandlers || isButtonTarget(event)) {\n            return;\n          }\n          if (event.key === ' ' && !isSpaceIgnored(domReference)) {\n            // Prevent scrolling\n            event.preventDefault();\n            didKeyDownRef.current = true;\n          }\n          if (event.key === 'Enter') {\n            if (open && toggle) {\n              onOpenChange(false, event.nativeEvent);\n            } else {\n              onOpenChange(true, event.nativeEvent);\n            }\n          }\n        },\n        onKeyUp(event) {\n          if (event.defaultPrevented || !keyboardHandlers || isButtonTarget(event) || isSpaceIgnored(domReference)) {\n            return;\n          }\n          if (event.key === ' ' && didKeyDownRef.current) {\n            didKeyDownRef.current = false;\n            if (open && toggle) {\n              onOpenChange(false, event.nativeEvent);\n            } else {\n              onOpenChange(true, event.nativeEvent);\n            }\n          }\n        }\n      }\n    };\n  }, [enabled, dataRef, eventOption, ignoreMouse, keyboardHandlers, domReference, toggle, open, onOpenChange]);\n}\n\n// `toString()` prevents bundlers from trying to `import { useInsertionEffect } from 'react'`\nconst useInsertionEffect = React[/*#__PURE__*/'useInsertionEffect'.toString()];\nconst useSafeInsertionEffect = useInsertionEffect || (fn => fn());\nfunction useEffectEvent(callback) {\n  const ref = React.useRef(() => {\n    if (process.env.NODE_ENV !== \"production\") {\n      throw new Error('Cannot call an event handler while rendering.');\n    }\n  });\n  useSafeInsertionEffect(() => {\n    ref.current = callback;\n  });\n  return React.useCallback(function () {\n    for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n      args[_key] = arguments[_key];\n    }\n    return ref.current == null ? void 0 : ref.current(...args);\n  }, []);\n}\n\nfunction createVirtualElement(domRef, data) {\n  let offsetX = null;\n  let offsetY = null;\n  let isAutoUpdateEvent = false;\n  return {\n    contextElement: domRef.current || undefined,\n    getBoundingClientRect() {\n      var _domRef$current, _data$dataRef$current;\n      const domRect = ((_domRef$current = domRef.current) == null ? void 0 : _domRef$current.getBoundingClientRect()) || {\n        width: 0,\n        height: 0,\n        x: 0,\n        y: 0\n      };\n      const isXAxis = data.axis === 'x' || data.axis === 'both';\n      const isYAxis = data.axis === 'y' || data.axis === 'both';\n      const canTrackCursorOnAutoUpdate = ['mouseenter', 'mousemove'].includes(((_data$dataRef$current = data.dataRef.current.openEvent) == null ? void 0 : _data$dataRef$current.type) || '') && data.pointerType !== 'touch';\n      let width = domRect.width;\n      let height = domRect.height;\n      let x = domRect.x;\n      let y = domRect.y;\n      if (offsetX == null && data.x && isXAxis) {\n        offsetX = domRect.x - data.x;\n      }\n      if (offsetY == null && data.y && isYAxis) {\n        offsetY = domRect.y - data.y;\n      }\n      x -= offsetX || 0;\n      y -= offsetY || 0;\n      width = 0;\n      height = 0;\n      if (!isAutoUpdateEvent || canTrackCursorOnAutoUpdate) {\n        width = data.axis === 'y' ? domRect.width : 0;\n        height = data.axis === 'x' ? domRect.height : 0;\n        x = isXAxis && data.x != null ? data.x : x;\n        y = isYAxis && data.y != null ? data.y : y;\n      } else if (isAutoUpdateEvent && !canTrackCursorOnAutoUpdate) {\n        height = data.axis === 'x' ? domRect.height : height;\n        width = data.axis === 'y' ? domRect.width : width;\n      }\n      isAutoUpdateEvent = true;\n      return {\n        width,\n        height,\n        x,\n        y,\n        top: y,\n        right: x + width,\n        bottom: y + height,\n        left: x\n      };\n    }\n  };\n}\nfunction isMouseBasedEvent(event) {\n  return event != null && event.clientX != null;\n}\n/**\n * Positions the floating element relative to a client point (in the viewport),\n * such as the mouse position. By default, it follows the mouse cursor.\n * @see https://floating-ui.com/docs/useClientPoint\n */\nfunction useClientPoint(context, props) {\n  if (props === void 0) {\n    props = {};\n  }\n  const {\n    open,\n    refs,\n    dataRef,\n    elements: {\n      floating\n    }\n  } = context;\n  const {\n    enabled = true,\n    axis = 'both',\n    x = null,\n    y = null\n  } = props;\n  const initialRef = React.useRef(false);\n  const cleanupListenerRef = React.useRef(null);\n  const [pointerType, setPointerType] = React.useState();\n  const [reactive, setReactive] = React.useState([]);\n  const setReference = useEffectEvent((x, y) => {\n    if (initialRef.current) return;\n\n    // Prevent setting if the open event was not a mouse-like one\n    // (e.g. focus to open, then hover over the reference element).\n    // Only apply if the event exists.\n    if (dataRef.current.openEvent && !isMouseBasedEvent(dataRef.current.openEvent)) {\n      return;\n    }\n    refs.setPositionReference(createVirtualElement(refs.domReference, {\n      x,\n      y,\n      axis,\n      dataRef,\n      pointerType\n    }));\n  });\n  const handleReferenceEnterOrMove = useEffectEvent(event => {\n    if (x != null || y != null) return;\n    if (!open) {\n      setReference(event.clientX, event.clientY);\n    } else if (!cleanupListenerRef.current) {\n      // If there's no cleanup, there's no listener, but we want to ensure\n      // we add the listener if the cursor landed on the floating element and\n      // then back on the reference (i.e. it's interactive).\n      setReactive([]);\n    }\n  });\n\n  // If the pointer is a mouse-like pointer, we want to continue following the\n  // mouse even if the floating element is transitioning out. On touch\n  // devices, this is undesirable because the floating element will move to\n  // the dismissal touch point.\n  const openCheck = isMouseLikePointerType(pointerType) ? floating : open;\n  const addListener = React.useCallback(() => {\n    // Explicitly specified `x`/`y` coordinates shouldn't add a listener.\n    if (!openCheck || !enabled || x != null || y != null) return;\n    const win = getWindow(refs.floating.current);\n    function handleMouseMove(event) {\n      const target = getTarget(event);\n      if (!contains(refs.floating.current, target)) {\n        setReference(event.clientX, event.clientY);\n      } else {\n        win.removeEventListener('mousemove', handleMouseMove);\n        cleanupListenerRef.current = null;\n      }\n    }\n    if (!dataRef.current.openEvent || isMouseBasedEvent(dataRef.current.openEvent)) {\n      win.addEventListener('mousemove', handleMouseMove);\n      const cleanup = () => {\n        win.removeEventListener('mousemove', handleMouseMove);\n        cleanupListenerRef.current = null;\n      };\n      cleanupListenerRef.current = cleanup;\n      return cleanup;\n    }\n    refs.setPositionReference(refs.domReference.current);\n  }, [dataRef, enabled, openCheck, refs, setReference, x, y]);\n  React.useEffect(() => {\n    return addListener();\n  }, [addListener, reactive]);\n  React.useEffect(() => {\n    if (enabled && !floating) {\n      initialRef.current = false;\n    }\n  }, [enabled, floating]);\n  React.useEffect(() => {\n    if (!enabled && open) {\n      initialRef.current = true;\n    }\n  }, [enabled, open]);\n  index(() => {\n    if (enabled && (x != null || y != null)) {\n      initialRef.current = false;\n      setReference(x, y);\n    }\n  }, [enabled, x, y, setReference]);\n  return React.useMemo(() => {\n    if (!enabled) return {};\n    function setPointerTypeRef(_ref) {\n      let {\n        pointerType\n      } = _ref;\n      setPointerType(pointerType);\n    }\n    return {\n      reference: {\n        onPointerDown: setPointerTypeRef,\n        onPointerEnter: setPointerTypeRef,\n        onMouseMove: handleReferenceEnterOrMove,\n        onMouseEnter: handleReferenceEnterOrMove\n      }\n    };\n  }, [enabled, handleReferenceEnterOrMove]);\n}\n\nconst bubbleHandlerKeys = {\n  pointerdown: 'onPointerDown',\n  mousedown: 'onMouseDown',\n  click: 'onClick'\n};\nconst captureHandlerKeys = {\n  pointerdown: 'onPointerDownCapture',\n  mousedown: 'onMouseDownCapture',\n  click: 'onClickCapture'\n};\nconst normalizeBubblesProp = bubbles => {\n  var _bubbles$escapeKey, _bubbles$outsidePress;\n  return {\n    escapeKeyBubbles: typeof bubbles === 'boolean' ? bubbles : (_bubbles$escapeKey = bubbles == null ? void 0 : bubbles.escapeKey) != null ? _bubbles$escapeKey : false,\n    outsidePressBubbles: typeof bubbles === 'boolean' ? bubbles : (_bubbles$outsidePress = bubbles == null ? void 0 : bubbles.outsidePress) != null ? _bubbles$outsidePress : true\n  };\n};\n/**\n * Closes the floating element when a dismissal is requested — by default, when\n * the user presses the `escape` key or outside of the floating element.\n * @see https://floating-ui.com/docs/useDismiss\n */\nfunction useDismiss(context, props) {\n  if (props === void 0) {\n    props = {};\n  }\n  const {\n    open,\n    onOpenChange,\n    events,\n    nodeId,\n    elements: {\n      reference,\n      domReference,\n      floating\n    },\n    dataRef\n  } = context;\n  const {\n    enabled = true,\n    escapeKey = true,\n    outsidePress: unstable_outsidePress = true,\n    outsidePressEvent = 'pointerdown',\n    referencePress = false,\n    referencePressEvent = 'pointerdown',\n    ancestorScroll = false,\n    bubbles\n  } = props;\n  const tree = useFloatingTree();\n  const nested = useFloatingParentNodeId() != null;\n  const outsidePressFn = useEffectEvent(typeof unstable_outsidePress === 'function' ? unstable_outsidePress : () => false);\n  const outsidePress = typeof unstable_outsidePress === 'function' ? outsidePressFn : unstable_outsidePress;\n  const insideReactTreeRef = React.useRef(false);\n  const {\n    escapeKeyBubbles,\n    outsidePressBubbles\n  } = normalizeBubblesProp(bubbles);\n  const closeOnEscapeKeyDown = useEffectEvent(event => {\n    if (!open || !enabled || !escapeKey || event.key !== 'Escape') {\n      return;\n    }\n    const children = tree ? getChildren(tree.nodesRef.current, nodeId) : [];\n    if (!escapeKeyBubbles) {\n      event.stopPropagation();\n      if (children.length > 0) {\n        let shouldDismiss = true;\n        children.forEach(child => {\n          var _child$context;\n          if ((_child$context = child.context) != null && _child$context.open && !child.context.dataRef.current.__escapeKeyBubbles) {\n            shouldDismiss = false;\n            return;\n          }\n        });\n        if (!shouldDismiss) {\n          return;\n        }\n      }\n    }\n    events.emit('dismiss', {\n      type: 'escapeKey',\n      data: {\n        returnFocus: {\n          preventScroll: false\n        }\n      }\n    });\n    onOpenChange(false, isReactEvent(event) ? event.nativeEvent : event);\n  });\n  const closeOnPressOutside = useEffectEvent(event => {\n    // Given developers can stop the propagation of the synthetic event,\n    // we can only be confident with a positive value.\n    const insideReactTree = insideReactTreeRef.current;\n    insideReactTreeRef.current = false;\n    if (insideReactTree) {\n      return;\n    }\n    if (typeof outsidePress === 'function' && !outsidePress(event)) {\n      return;\n    }\n    const target = getTarget(event);\n    const inertSelector = \"[\" + createAttribute('inert') + \"]\";\n    const markers = getDocument(floating).querySelectorAll(inertSelector);\n    let targetRootAncestor = isElement(target) ? target : null;\n    while (targetRootAncestor && !isLastTraversableNode(targetRootAncestor)) {\n      const nextParent = getParentNode(targetRootAncestor);\n      if (nextParent === getDocument(floating).body || !isElement(nextParent)) {\n        break;\n      } else {\n        targetRootAncestor = nextParent;\n      }\n    }\n\n    // Check if the click occurred on a third-party element injected after the\n    // floating element rendered.\n    if (markers.length && isElement(target) && !isRootElement(target) &&\n    // Clicked on a direct ancestor (e.g. FloatingOverlay).\n    !contains(target, floating) &&\n    // If the target root element contains none of the markers, then the\n    // element was injected after the floating element rendered.\n    Array.from(markers).every(marker => !contains(targetRootAncestor, marker))) {\n      return;\n    }\n\n    // Check if the click occurred on the scrollbar\n    if (isHTMLElement(target) && floating) {\n      // In Firefox, `target.scrollWidth > target.clientWidth` for inline\n      // elements.\n      const canScrollX = target.clientWidth > 0 && target.scrollWidth > target.clientWidth;\n      const canScrollY = target.clientHeight > 0 && target.scrollHeight > target.clientHeight;\n      let xCond = canScrollY && event.offsetX > target.clientWidth;\n\n      // In some browsers it is possible to change the <body> (or window)\n      // scrollbar to the left side, but is very rare and is difficult to\n      // check for. Plus, for modal dialogs with backdrops, it is more\n      // important that the backdrop is checked but not so much the window.\n      if (canScrollY) {\n        const isRTL = getComputedStyle(target).direction === 'rtl';\n        if (isRTL) {\n          xCond = event.offsetX <= target.offsetWidth - target.clientWidth;\n        }\n      }\n      if (xCond || canScrollX && event.offsetY > target.clientHeight) {\n        return;\n      }\n    }\n    const targetIsInsideChildren = tree && getChildren(tree.nodesRef.current, nodeId).some(node => {\n      var _node$context;\n      return isEventTargetWithin(event, (_node$context = node.context) == null ? void 0 : _node$context.elements.floating);\n    });\n    if (isEventTargetWithin(event, floating) || isEventTargetWithin(event, domReference) || targetIsInsideChildren) {\n      return;\n    }\n    const children = tree ? getChildren(tree.nodesRef.current, nodeId) : [];\n    if (children.length > 0) {\n      let shouldDismiss = true;\n      children.forEach(child => {\n        var _child$context2;\n        if ((_child$context2 = child.context) != null && _child$context2.open && !child.context.dataRef.current.__outsidePressBubbles) {\n          shouldDismiss = false;\n          return;\n        }\n      });\n      if (!shouldDismiss) {\n        return;\n      }\n    }\n    events.emit('dismiss', {\n      type: 'outsidePress',\n      data: {\n        returnFocus: nested ? {\n          preventScroll: true\n        } : isVirtualClick(event) || isVirtualPointerEvent(event)\n      }\n    });\n    onOpenChange(false, event);\n  });\n  React.useEffect(() => {\n    if (!open || !enabled) {\n      return;\n    }\n    dataRef.current.__escapeKeyBubbles = escapeKeyBubbles;\n    dataRef.current.__outsidePressBubbles = outsidePressBubbles;\n    function onScroll(event) {\n      onOpenChange(false, event);\n    }\n    const doc = getDocument(floating);\n    escapeKey && doc.addEventListener('keydown', closeOnEscapeKeyDown);\n    outsidePress && doc.addEventListener(outsidePressEvent, closeOnPressOutside);\n    let ancestors = [];\n    if (ancestorScroll) {\n      if (isElement(domReference)) {\n        ancestors = getOverflowAncestors(domReference);\n      }\n      if (isElement(floating)) {\n        ancestors = ancestors.concat(getOverflowAncestors(floating));\n      }\n      if (!isElement(reference) && reference && reference.contextElement) {\n        ancestors = ancestors.concat(getOverflowAncestors(reference.contextElement));\n      }\n    }\n\n    // Ignore the visual viewport for scrolling dismissal (allow pinch-zoom)\n    ancestors = ancestors.filter(ancestor => {\n      var _doc$defaultView;\n      return ancestor !== ((_doc$defaultView = doc.defaultView) == null ? void 0 : _doc$defaultView.visualViewport);\n    });\n    ancestors.forEach(ancestor => {\n      ancestor.addEventListener('scroll', onScroll, {\n        passive: true\n      });\n    });\n    return () => {\n      escapeKey && doc.removeEventListener('keydown', closeOnEscapeKeyDown);\n      outsidePress && doc.removeEventListener(outsidePressEvent, closeOnPressOutside);\n      ancestors.forEach(ancestor => {\n        ancestor.removeEventListener('scroll', onScroll);\n      });\n    };\n  }, [dataRef, floating, domReference, reference, escapeKey, outsidePress, outsidePressEvent, open, onOpenChange, ancestorScroll, enabled, escapeKeyBubbles, outsidePressBubbles, closeOnEscapeKeyDown, closeOnPressOutside]);\n  React.useEffect(() => {\n    insideReactTreeRef.current = false;\n  }, [outsidePress, outsidePressEvent]);\n  return React.useMemo(() => {\n    if (!enabled) {\n      return {};\n    }\n    return {\n      reference: {\n        onKeyDown: closeOnEscapeKeyDown,\n        [bubbleHandlerKeys[referencePressEvent]]: event => {\n          if (referencePress) {\n            events.emit('dismiss', {\n              type: 'referencePress',\n              data: {\n                returnFocus: false\n              }\n            });\n            onOpenChange(false, event.nativeEvent);\n          }\n        }\n      },\n      floating: {\n        onKeyDown: closeOnEscapeKeyDown,\n        [captureHandlerKeys[outsidePressEvent]]: () => {\n          insideReactTreeRef.current = true;\n        }\n      }\n    };\n  }, [enabled, events, referencePress, outsidePressEvent, referencePressEvent, onOpenChange, closeOnEscapeKeyDown]);\n}\n\nlet devMessageSet;\nif (process.env.NODE_ENV !== \"production\") {\n  devMessageSet = /*#__PURE__*/new Set();\n}\n\n/**\n * Provides data to position a floating element and context to add interactions.\n * @see https://floating-ui.com/docs/react\n */\nfunction useFloating(options) {\n  var _options$elements2;\n  if (options === void 0) {\n    options = {};\n  }\n  const {\n    open = false,\n    onOpenChange: unstable_onOpenChange,\n    nodeId\n  } = options;\n  if (process.env.NODE_ENV !== \"production\") {\n    var _options$elements;\n    const err = 'Floating UI: Cannot pass a virtual element to the ' + '`elements.reference` option, as it must be a real DOM element. ' + 'Use `refs.setPositionReference` instead.';\n    if ((_options$elements = options.elements) != null && _options$elements.reference && !isElement(options.elements.reference)) {\n      var _devMessageSet;\n      if (!((_devMessageSet = devMessageSet) != null && _devMessageSet.has(err))) {\n        var _devMessageSet2;\n        (_devMessageSet2 = devMessageSet) == null ? void 0 : _devMessageSet2.add(err);\n        console.error(err);\n      }\n    }\n  }\n  const [_domReference, setDomReference] = React.useState(null);\n  const domReference = ((_options$elements2 = options.elements) == null ? void 0 : _options$elements2.reference) || _domReference;\n  const position = useFloating$1(options);\n  const tree = useFloatingTree();\n  const onOpenChange = useEffectEvent((open, event) => {\n    if (open) {\n      dataRef.current.openEvent = event;\n    }\n    unstable_onOpenChange == null ? void 0 : unstable_onOpenChange(open, event);\n  });\n  const domReferenceRef = React.useRef(null);\n  const dataRef = React.useRef({});\n  const events = React.useState(() => createPubSub())[0];\n  const floatingId = useId();\n  const setPositionReference = React.useCallback(node => {\n    const positionReference = isElement(node) ? {\n      getBoundingClientRect: () => node.getBoundingClientRect(),\n      contextElement: node\n    } : node;\n    position.refs.setReference(positionReference);\n  }, [position.refs]);\n  const setReference = React.useCallback(node => {\n    if (isElement(node) || node === null) {\n      domReferenceRef.current = node;\n      setDomReference(node);\n    }\n\n    // Backwards-compatibility for passing a virtual element to `reference`\n    // after it has set the DOM reference.\n    if (isElement(position.refs.reference.current) || position.refs.reference.current === null ||\n    // Don't allow setting virtual elements using the old technique back to\n    // `null` to support `positionReference` + an unstable `reference`\n    // callback ref.\n    node !== null && !isElement(node)) {\n      position.refs.setReference(node);\n    }\n  }, [position.refs]);\n  const refs = React.useMemo(() => ({\n    ...position.refs,\n    setReference,\n    setPositionReference,\n    domReference: domReferenceRef\n  }), [position.refs, setReference, setPositionReference]);\n  const elements = React.useMemo(() => ({\n    ...position.elements,\n    domReference: domReference\n  }), [position.elements, domReference]);\n  const context = React.useMemo(() => ({\n    ...position,\n    refs,\n    elements,\n    dataRef,\n    nodeId,\n    floatingId,\n    events,\n    open,\n    onOpenChange\n  }), [position, nodeId, floatingId, events, open, onOpenChange, refs, elements]);\n  index(() => {\n    const node = tree == null ? void 0 : tree.nodesRef.current.find(node => node.id === nodeId);\n    if (node) {\n      node.context = context;\n    }\n  });\n  return React.useMemo(() => ({\n    ...position,\n    context,\n    refs,\n    elements\n  }), [position, refs, elements, context]);\n}\n\n/**\n * Opens the floating element while the reference element has focus, like CSS\n * `:focus`.\n * @see https://floating-ui.com/docs/useFocus\n */\nfunction useFocus(context, props) {\n  if (props === void 0) {\n    props = {};\n  }\n  const {\n    open,\n    onOpenChange,\n    dataRef,\n    events,\n    refs,\n    elements: {\n      floating,\n      domReference\n    }\n  } = context;\n  const {\n    enabled = true,\n    keyboardOnly = true\n  } = props;\n  const pointerTypeRef = React.useRef('');\n  const blockFocusRef = React.useRef(false);\n  const timeoutRef = React.useRef();\n  React.useEffect(() => {\n    if (!enabled) {\n      return;\n    }\n    const doc = getDocument(floating);\n    const win = doc.defaultView || window;\n\n    // If the reference was focused and the user left the tab/window, and the\n    // floating element was not open, the focus should be blocked when they\n    // return to the tab/window.\n    function onBlur() {\n      if (!open && isHTMLElement(domReference) && domReference === activeElement(getDocument(domReference))) {\n        blockFocusRef.current = true;\n      }\n    }\n    win.addEventListener('blur', onBlur);\n    return () => {\n      win.removeEventListener('blur', onBlur);\n    };\n  }, [floating, domReference, open, enabled]);\n  React.useEffect(() => {\n    if (!enabled) {\n      return;\n    }\n    function onDismiss(payload) {\n      if (payload.type === 'referencePress' || payload.type === 'escapeKey') {\n        blockFocusRef.current = true;\n      }\n    }\n    events.on('dismiss', onDismiss);\n    return () => {\n      events.off('dismiss', onDismiss);\n    };\n  }, [events, enabled]);\n  React.useEffect(() => {\n    return () => {\n      clearTimeout(timeoutRef.current);\n    };\n  }, []);\n  return React.useMemo(() => {\n    if (!enabled) {\n      return {};\n    }\n    return {\n      reference: {\n        onPointerDown(_ref) {\n          let {\n            pointerType\n          } = _ref;\n          pointerTypeRef.current = pointerType;\n          blockFocusRef.current = !!(pointerType && keyboardOnly);\n        },\n        onMouseLeave() {\n          blockFocusRef.current = false;\n        },\n        onFocus(event) {\n          var _dataRef$current$open;\n          if (blockFocusRef.current) {\n            return;\n          }\n\n          // Dismiss with click should ignore the subsequent `focus` trigger,\n          // but only if the click originated inside the reference element.\n          if (event.type === 'focus' && ((_dataRef$current$open = dataRef.current.openEvent) == null ? void 0 : _dataRef$current$open.type) === 'mousedown' && isEventTargetWithin(dataRef.current.openEvent, domReference)) {\n            return;\n          }\n          onOpenChange(true, event.nativeEvent);\n        },\n        onBlur(event) {\n          blockFocusRef.current = false;\n          const relatedTarget = event.relatedTarget;\n\n          // Hit the non-modal focus management portal guard. Focus will be\n          // moved into the floating element immediately after.\n          const movedToFocusGuard = isElement(relatedTarget) && relatedTarget.hasAttribute(createAttribute('focus-guard')) && relatedTarget.getAttribute('data-type') === 'outside';\n\n          // Wait for the window blur listener to fire.\n          timeoutRef.current = setTimeout(() => {\n            // When focusing the reference element (e.g. regular click), then\n            // clicking into the floating element, prevent it from hiding.\n            // Note: it must be focusable, e.g. `tabindex=\"-1\"`.\n            if (contains(refs.floating.current, relatedTarget) || contains(domReference, relatedTarget) || movedToFocusGuard) {\n              return;\n            }\n            onOpenChange(false, event.nativeEvent);\n          });\n        }\n      }\n    };\n  }, [enabled, keyboardOnly, domReference, refs, dataRef, onOpenChange]);\n}\n\nfunction mergeProps(userProps, propsList, elementKey) {\n  const map = new Map();\n  return {\n    ...(elementKey === 'floating' && {\n      tabIndex: -1\n    }),\n    ...userProps,\n    ...propsList.map(value => value ? value[elementKey] : null).concat(userProps).reduce((acc, props) => {\n      if (!props) {\n        return acc;\n      }\n      Object.entries(props).forEach(_ref => {\n        let [key, value] = _ref;\n        if (key.indexOf('on') === 0) {\n          if (!map.has(key)) {\n            map.set(key, []);\n          }\n          if (typeof value === 'function') {\n            var _map$get;\n            (_map$get = map.get(key)) == null ? void 0 : _map$get.push(value);\n            acc[key] = function () {\n              var _map$get2;\n              for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n                args[_key] = arguments[_key];\n              }\n              return (_map$get2 = map.get(key)) == null ? void 0 : _map$get2.map(fn => fn(...args)).find(val => val !== undefined);\n            };\n          }\n        } else {\n          acc[key] = value;\n        }\n      });\n      return acc;\n    }, {})\n  };\n}\n\n/**\n * Merges an array of interaction hooks' props into prop getters, allowing\n * event handler functions to be composed together without overwriting one\n * another.\n * @see https://floating-ui.com/docs/react#interaction-hooks\n */\nfunction useInteractions(propsList) {\n  if (propsList === void 0) {\n    propsList = [];\n  }\n  // The dependencies are a dynamic array, so we can't use the linter's\n  // suggestion to add it to the deps array.\n  const deps = propsList;\n  const getReferenceProps = React.useCallback(userProps => mergeProps(userProps, propsList, 'reference'),\n  // eslint-disable-next-line react-hooks/exhaustive-deps\n  deps);\n  const getFloatingProps = React.useCallback(userProps => mergeProps(userProps, propsList, 'floating'),\n  // eslint-disable-next-line react-hooks/exhaustive-deps\n  deps);\n  const getItemProps = React.useCallback(userProps => mergeProps(userProps, propsList, 'item'),\n  // Granularly check for `item` changes, because the `getItemProps` getter\n  // should be as referentially stable as possible since it may be passed as\n  // a prop to many components. All `item` key values must therefore be\n  // memoized.\n  // eslint-disable-next-line react-hooks/exhaustive-deps\n  propsList.map(key => key == null ? void 0 : key.item));\n  return React.useMemo(() => ({\n    getReferenceProps,\n    getFloatingProps,\n    getItemProps\n  }), [getReferenceProps, getFloatingProps, getItemProps]);\n}\n\nlet isPreventScrollSupported = false;\nfunction doSwitch(orientation, vertical, horizontal) {\n  switch (orientation) {\n    case 'vertical':\n      return vertical;\n    case 'horizontal':\n      return horizontal;\n    default:\n      return vertical || horizontal;\n  }\n}\nfunction isMainOrientationKey(key, orientation) {\n  const vertical = key === ARROW_UP || key === ARROW_DOWN;\n  const horizontal = key === ARROW_LEFT || key === ARROW_RIGHT;\n  return doSwitch(orientation, vertical, horizontal);\n}\nfunction isMainOrientationToEndKey(key, orientation, rtl) {\n  const vertical = key === ARROW_DOWN;\n  const horizontal = rtl ? key === ARROW_LEFT : key === ARROW_RIGHT;\n  return doSwitch(orientation, vertical, horizontal) || key === 'Enter' || key == ' ' || key === '';\n}\nfunction isCrossOrientationOpenKey(key, orientation, rtl) {\n  const vertical = rtl ? key === ARROW_LEFT : key === ARROW_RIGHT;\n  const horizontal = key === ARROW_DOWN;\n  return doSwitch(orientation, vertical, horizontal);\n}\nfunction isCrossOrientationCloseKey(key, orientation, rtl) {\n  const vertical = rtl ? key === ARROW_RIGHT : key === ARROW_LEFT;\n  const horizontal = key === ARROW_UP;\n  return doSwitch(orientation, vertical, horizontal);\n}\n/**\n * Adds arrow key-based navigation of a list of items, either using real DOM\n * focus or virtual focus.\n * @see https://floating-ui.com/docs/useListNavigation\n */\nfunction useListNavigation(context, props) {\n  const {\n    open,\n    onOpenChange,\n    refs,\n    elements: {\n      domReference,\n      floating\n    }\n  } = context;\n  const {\n    listRef,\n    activeIndex,\n    onNavigate: unstable_onNavigate = () => {},\n    enabled = true,\n    selectedIndex = null,\n    allowEscape = false,\n    loop = false,\n    nested = false,\n    rtl = false,\n    virtual = false,\n    focusItemOnOpen = 'auto',\n    focusItemOnHover = true,\n    openOnArrowKeyDown = true,\n    disabledIndices = undefined,\n    orientation = 'vertical',\n    cols = 1,\n    scrollItemIntoView = true,\n    virtualItemRef\n  } = props;\n  if (process.env.NODE_ENV !== \"production\") {\n    if (allowEscape) {\n      if (!loop) {\n        console.warn(['Floating UI: `useListNavigation` looping must be enabled to allow', 'escaping.'].join(' '));\n      }\n      if (!virtual) {\n        console.warn(['Floating UI: `useListNavigation` must be virtual to allow', 'escaping.'].join(' '));\n      }\n    }\n    if (orientation === 'vertical' && cols > 1) {\n      console.warn(['Floating UI: In grid list navigation mode (`cols` > 1), the', '`orientation` should be either \"horizontal\" or \"both\".'].join(' '));\n    }\n  }\n  const parentId = useFloatingParentNodeId();\n  const tree = useFloatingTree();\n  const onNavigate = useEffectEvent(unstable_onNavigate);\n  const focusItemOnOpenRef = React.useRef(focusItemOnOpen);\n  const indexRef = React.useRef(selectedIndex != null ? selectedIndex : -1);\n  const keyRef = React.useRef(null);\n  const isPointerModalityRef = React.useRef(true);\n  const previousOnNavigateRef = React.useRef(onNavigate);\n  const previousMountedRef = React.useRef(!!floating);\n  const forceSyncFocus = React.useRef(false);\n  const forceScrollIntoViewRef = React.useRef(false);\n  const disabledIndicesRef = useLatestRef(disabledIndices);\n  const latestOpenRef = useLatestRef(open);\n  const scrollItemIntoViewRef = useLatestRef(scrollItemIntoView);\n  const [activeId, setActiveId] = React.useState();\n  const [virtualId, setVirtualId] = React.useState();\n  const focusItem = useEffectEvent(function (listRef, indexRef, forceScrollIntoView) {\n    if (forceScrollIntoView === void 0) {\n      forceScrollIntoView = false;\n    }\n    const item = listRef.current[indexRef.current];\n    if (!item) return;\n    if (virtual) {\n      setActiveId(item.id);\n      tree == null ? void 0 : tree.events.emit('virtualfocus', item);\n      if (virtualItemRef) {\n        virtualItemRef.current = item;\n      }\n    } else {\n      enqueueFocus(item, {\n        preventScroll: true,\n        // Mac Safari does not move the virtual cursor unless the focus call\n        // is sync. However, for the very first focus call, we need to wait\n        // for the position to be ready in order to prevent unwanted\n        // scrolling. This means the virtual cursor will not move to the first\n        // item when first opening the floating element, but will on\n        // subsequent calls. `preventScroll` is supported in modern Safari,\n        // so we can use that instead.\n        // iOS Safari must be async or the first item will not be focused.\n        sync: isMac() && isSafari() ? isPreventScrollSupported || forceSyncFocus.current : false\n      });\n    }\n    requestAnimationFrame(() => {\n      const scrollIntoViewOptions = scrollItemIntoViewRef.current;\n      const shouldScrollIntoView = scrollIntoViewOptions && item && (forceScrollIntoView || !isPointerModalityRef.current);\n      if (shouldScrollIntoView) {\n        // JSDOM doesn't support `.scrollIntoView()` but it's widely supported\n        // by all browsers.\n        item.scrollIntoView == null ? void 0 : item.scrollIntoView(typeof scrollIntoViewOptions === 'boolean' ? {\n          block: 'nearest',\n          inline: 'nearest'\n        } : scrollIntoViewOptions);\n      }\n    });\n  });\n  index(() => {\n    document.createElement('div').focus({\n      get preventScroll() {\n        isPreventScrollSupported = true;\n        return false;\n      }\n    });\n  }, []);\n\n  // Sync `selectedIndex` to be the `activeIndex` upon opening the floating\n  // element. Also, reset `activeIndex` upon closing the floating element.\n  index(() => {\n    if (!enabled) {\n      return;\n    }\n    if (open && floating) {\n      if (focusItemOnOpenRef.current && selectedIndex != null) {\n        // Regardless of the pointer modality, we want to ensure the selected\n        // item comes into view when the floating element is opened.\n        forceScrollIntoViewRef.current = true;\n        onNavigate(selectedIndex);\n      }\n    } else if (previousMountedRef.current) {\n      // Since the user can specify `onNavigate` conditionally\n      // (onNavigate: open ? setActiveIndex : setSelectedIndex),\n      // we store and call the previous function.\n      indexRef.current = -1;\n      previousOnNavigateRef.current(null);\n    }\n  }, [enabled, open, floating, selectedIndex, onNavigate]);\n\n  // Sync `activeIndex` to be the focused item while the floating element is\n  // open.\n  index(() => {\n    if (!enabled) {\n      return;\n    }\n    if (open && floating) {\n      if (activeIndex == null) {\n        forceSyncFocus.current = false;\n        if (selectedIndex != null) {\n          return;\n        }\n\n        // Reset while the floating element was open (e.g. the list changed).\n        if (previousMountedRef.current) {\n          indexRef.current = -1;\n          focusItem(listRef, indexRef);\n        }\n\n        // Initial sync.\n        if (!previousMountedRef.current && focusItemOnOpenRef.current && (keyRef.current != null || focusItemOnOpenRef.current === true && keyRef.current == null)) {\n          let runs = 0;\n          const waitForListPopulated = () => {\n            if (listRef.current[0] == null) {\n              // Avoid letting the browser paint if possible on the first try,\n              // otherwise use rAF. Don't try more than twice, since something\n              // is wrong otherwise.\n              if (runs < 2) {\n                const scheduler = runs ? requestAnimationFrame : queueMicrotask;\n                scheduler(waitForListPopulated);\n              }\n              runs++;\n            } else {\n              indexRef.current = keyRef.current == null || isMainOrientationToEndKey(keyRef.current, orientation, rtl) || nested ? getMinIndex(listRef, disabledIndicesRef.current) : getMaxIndex(listRef, disabledIndicesRef.current);\n              keyRef.current = null;\n              onNavigate(indexRef.current);\n            }\n          };\n          waitForListPopulated();\n        }\n      } else if (!isIndexOutOfBounds(listRef, activeIndex)) {\n        indexRef.current = activeIndex;\n        focusItem(listRef, indexRef, forceScrollIntoViewRef.current);\n        forceScrollIntoViewRef.current = false;\n      }\n    }\n  }, [enabled, open, floating, activeIndex, selectedIndex, nested, listRef, orientation, rtl, onNavigate, focusItem, disabledIndicesRef]);\n\n  // Ensure the parent floating element has focus when a nested child closes\n  // to allow arrow key navigation to work after the pointer leaves the child.\n  index(() => {\n    var _nodes$find, _nodes$find$context;\n    if (!enabled || floating || !tree || virtual || !previousMountedRef.current) {\n      return;\n    }\n    const nodes = tree.nodesRef.current;\n    const parent = (_nodes$find = nodes.find(node => node.id === parentId)) == null ? void 0 : (_nodes$find$context = _nodes$find.context) == null ? void 0 : _nodes$find$context.elements.floating;\n    const activeEl = activeElement(getDocument(floating));\n    const treeContainsActiveEl = nodes.some(node => node.context && contains(node.context.elements.floating, activeEl));\n    if (parent && !treeContainsActiveEl && isPointerModalityRef.current) {\n      parent.focus({\n        preventScroll: true\n      });\n    }\n  }, [enabled, floating, tree, parentId, virtual]);\n  index(() => {\n    if (!enabled || !tree || !virtual || parentId) return;\n    function handleVirtualFocus(item) {\n      setVirtualId(item.id);\n      if (virtualItemRef) {\n        virtualItemRef.current = item;\n      }\n    }\n    tree.events.on('virtualfocus', handleVirtualFocus);\n    return () => {\n      tree.events.off('virtualfocus', handleVirtualFocus);\n    };\n  }, [enabled, tree, virtual, parentId, virtualItemRef]);\n  index(() => {\n    previousOnNavigateRef.current = onNavigate;\n    previousMountedRef.current = !!floating;\n  });\n  index(() => {\n    if (!open) {\n      keyRef.current = null;\n    }\n  }, [open]);\n  const hasActiveIndex = activeIndex != null;\n  const item = React.useMemo(() => {\n    function syncCurrentTarget(currentTarget) {\n      if (!open) return;\n      const index = listRef.current.indexOf(currentTarget);\n      if (index !== -1) {\n        onNavigate(index);\n      }\n    }\n    const props = {\n      onFocus(_ref) {\n        let {\n          currentTarget\n        } = _ref;\n        syncCurrentTarget(currentTarget);\n      },\n      onClick: _ref2 => {\n        let {\n          currentTarget\n        } = _ref2;\n        return currentTarget.focus({\n          preventScroll: true\n        });\n      },\n      // Safari\n      ...(focusItemOnHover && {\n        onMouseMove(_ref3) {\n          let {\n            currentTarget\n          } = _ref3;\n          syncCurrentTarget(currentTarget);\n        },\n        onPointerLeave(_ref4) {\n          let {\n            pointerType\n          } = _ref4;\n          if (!isPointerModalityRef.current || pointerType === 'touch') {\n            return;\n          }\n          indexRef.current = -1;\n          focusItem(listRef, indexRef);\n          onNavigate(null);\n          if (!virtual) {\n            enqueueFocus(refs.floating.current, {\n              preventScroll: true\n            });\n          }\n        }\n      })\n    };\n    return props;\n  }, [open, refs, focusItem, focusItemOnHover, listRef, onNavigate, virtual]);\n  return React.useMemo(() => {\n    if (!enabled) {\n      return {};\n    }\n    const disabledIndices = disabledIndicesRef.current;\n    function onKeyDown(event) {\n      isPointerModalityRef.current = false;\n      forceSyncFocus.current = true;\n\n      // If the floating element is animating out, ignore navigation. Otherwise,\n      // the `activeIndex` gets set to 0 despite not being open so the next time\n      // the user ArrowDowns, the first item won't be focused.\n      if (!latestOpenRef.current && event.currentTarget === refs.floating.current) {\n        return;\n      }\n      if (nested && isCrossOrientationCloseKey(event.key, orientation, rtl)) {\n        stopEvent(event);\n        onOpenChange(false, event.nativeEvent);\n        if (isHTMLElement(domReference) && !virtual) {\n          domReference.focus();\n        }\n        return;\n      }\n      const currentIndex = indexRef.current;\n      const minIndex = getMinIndex(listRef, disabledIndices);\n      const maxIndex = getMaxIndex(listRef, disabledIndices);\n      if (event.key === 'Home') {\n        stopEvent(event);\n        indexRef.current = minIndex;\n        onNavigate(indexRef.current);\n      }\n      if (event.key === 'End') {\n        stopEvent(event);\n        indexRef.current = maxIndex;\n        onNavigate(indexRef.current);\n      }\n\n      // Grid navigation.\n      if (cols > 1) {\n        indexRef.current = getGridNavigatedIndex(listRef, {\n          event,\n          orientation,\n          loop,\n          cols,\n          disabledIndices,\n          minIndex,\n          maxIndex,\n          prevIndex: indexRef.current,\n          stopEvent: true\n        });\n        onNavigate(indexRef.current);\n        if (orientation === 'both') {\n          return;\n        }\n      }\n      if (isMainOrientationKey(event.key, orientation)) {\n        stopEvent(event);\n\n        // Reset the index if no item is focused.\n        if (open && !virtual && activeElement(event.currentTarget.ownerDocument) === event.currentTarget) {\n          indexRef.current = isMainOrientationToEndKey(event.key, orientation, rtl) ? minIndex : maxIndex;\n          onNavigate(indexRef.current);\n          return;\n        }\n        if (isMainOrientationToEndKey(event.key, orientation, rtl)) {\n          if (loop) {\n            indexRef.current = currentIndex >= maxIndex ? allowEscape && currentIndex !== listRef.current.length ? -1 : minIndex : findNonDisabledIndex(listRef, {\n              startingIndex: currentIndex,\n              disabledIndices\n            });\n          } else {\n            indexRef.current = Math.min(maxIndex, findNonDisabledIndex(listRef, {\n              startingIndex: currentIndex,\n              disabledIndices\n            }));\n          }\n        } else {\n          if (loop) {\n            indexRef.current = currentIndex <= minIndex ? allowEscape && currentIndex !== -1 ? listRef.current.length : maxIndex : findNonDisabledIndex(listRef, {\n              startingIndex: currentIndex,\n              decrement: true,\n              disabledIndices\n            });\n          } else {\n            indexRef.current = Math.max(minIndex, findNonDisabledIndex(listRef, {\n              startingIndex: currentIndex,\n              decrement: true,\n              disabledIndices\n            }));\n          }\n        }\n        if (isIndexOutOfBounds(listRef, indexRef.current)) {\n          onNavigate(null);\n        } else {\n          onNavigate(indexRef.current);\n        }\n      }\n    }\n    function checkVirtualMouse(event) {\n      if (focusItemOnOpen === 'auto' && isVirtualClick(event.nativeEvent)) {\n        focusItemOnOpenRef.current = true;\n      }\n    }\n    function checkVirtualPointer(event) {\n      // `pointerdown` fires first, reset the state then perform the checks.\n      focusItemOnOpenRef.current = focusItemOnOpen;\n      if (focusItemOnOpen === 'auto' && isVirtualPointerEvent(event.nativeEvent)) {\n        focusItemOnOpenRef.current = true;\n      }\n    }\n    const ariaActiveDescendantProp = virtual && open && hasActiveIndex && {\n      'aria-activedescendant': virtualId || activeId\n    };\n    const activeItem = listRef.current.find(item => (item == null ? void 0 : item.id) === activeId);\n    return {\n      reference: {\n        ...ariaActiveDescendantProp,\n        onKeyDown(event) {\n          isPointerModalityRef.current = false;\n          const isArrowKey = event.key.indexOf('Arrow') === 0;\n          const isCrossOpenKey = isCrossOrientationOpenKey(event.key, orientation, rtl);\n          const isCrossCloseKey = isCrossOrientationCloseKey(event.key, orientation, rtl);\n          const isMainKey = isMainOrientationKey(event.key, orientation);\n          const isNavigationKey = (nested ? isCrossOpenKey : isMainKey) || event.key === 'Enter' || event.key.trim() === '';\n          if (virtual && open) {\n            const rootNode = tree == null ? void 0 : tree.nodesRef.current.find(node => node.parentId == null);\n            const deepestNode = tree && rootNode ? getDeepestNode(tree.nodesRef.current, rootNode.id) : null;\n            if (isArrowKey && deepestNode && virtualItemRef) {\n              const eventObject = new KeyboardEvent('keydown', {\n                key: event.key,\n                bubbles: true\n              });\n              if (isCrossOpenKey || isCrossCloseKey) {\n                var _deepestNode$context, _deepestNode$context2;\n                const isCurrentTarget = ((_deepestNode$context = deepestNode.context) == null ? void 0 : _deepestNode$context.elements.domReference) === event.currentTarget;\n                const dispatchItem = isCrossCloseKey && !isCurrentTarget ? (_deepestNode$context2 = deepestNode.context) == null ? void 0 : _deepestNode$context2.elements.domReference : isCrossOpenKey ? activeItem : null;\n                if (dispatchItem) {\n                  stopEvent(event);\n                  dispatchItem.dispatchEvent(eventObject);\n                  setVirtualId(undefined);\n                }\n              }\n              if (isMainKey && deepestNode.context) {\n                if (deepestNode.context.open && deepestNode.parentId && event.currentTarget !== deepestNode.context.elements.domReference) {\n                  var _deepestNode$context$;\n                  stopEvent(event);\n                  (_deepestNode$context$ = deepestNode.context.elements.domReference) == null ? void 0 : _deepestNode$context$.dispatchEvent(eventObject);\n                  return;\n                }\n              }\n            }\n            return onKeyDown(event);\n          }\n\n          // If a floating element should not open on arrow key down, avoid\n          // setting `activeIndex` while it's closed.\n          if (!open && !openOnArrowKeyDown && isArrowKey) {\n            return;\n          }\n          if (isNavigationKey) {\n            keyRef.current = nested && isMainKey ? null : event.key;\n          }\n          if (nested) {\n            if (isCrossOpenKey) {\n              stopEvent(event);\n              if (open) {\n                indexRef.current = getMinIndex(listRef, disabledIndices);\n                onNavigate(indexRef.current);\n              } else {\n                onOpenChange(true, event.nativeEvent);\n              }\n            }\n            return;\n          }\n          if (isMainKey) {\n            if (selectedIndex != null) {\n              indexRef.current = selectedIndex;\n            }\n            stopEvent(event);\n            if (!open && openOnArrowKeyDown) {\n              onOpenChange(true, event.nativeEvent);\n            } else {\n              onKeyDown(event);\n            }\n            if (open) {\n              onNavigate(indexRef.current);\n            }\n          }\n        },\n        onFocus() {\n          if (open) {\n            onNavigate(null);\n          }\n        },\n        onPointerDown: checkVirtualPointer,\n        onMouseDown: checkVirtualMouse,\n        onClick: checkVirtualMouse\n      },\n      floating: {\n        'aria-orientation': orientation === 'both' ? undefined : orientation,\n        ...ariaActiveDescendantProp,\n        onKeyDown,\n        onPointerMove() {\n          isPointerModalityRef.current = true;\n        }\n      },\n      item\n    };\n  }, [domReference, refs, activeId, virtualId, disabledIndicesRef, latestOpenRef, listRef, enabled, orientation, rtl, virtual, open, hasActiveIndex, nested, selectedIndex, openOnArrowKeyDown, allowEscape, cols, loop, focusItemOnOpen, onNavigate, onOpenChange, item, tree, virtualItemRef]);\n}\n\n/**\n * Adds base screen reader props to the reference and floating elements for a\n * given floating element `role`.\n * @see https://floating-ui.com/docs/useRole\n */\nfunction useRole(context, props) {\n  if (props === void 0) {\n    props = {};\n  }\n  const {\n    open,\n    floatingId\n  } = context;\n  const {\n    enabled = true,\n    role = 'dialog'\n  } = props;\n  const referenceId = useId();\n  return React.useMemo(() => {\n    const floatingProps = {\n      id: floatingId,\n      role\n    };\n    if (!enabled) {\n      return {};\n    }\n    if (role === 'tooltip') {\n      return {\n        reference: {\n          'aria-describedby': open ? floatingId : undefined\n        },\n        floating: floatingProps\n      };\n    }\n    return {\n      reference: {\n        'aria-expanded': open ? 'true' : 'false',\n        'aria-haspopup': role === 'alertdialog' ? 'dialog' : role,\n        'aria-controls': open ? floatingId : undefined,\n        ...(role === 'listbox' && {\n          role: 'combobox'\n        }),\n        ...(role === 'menu' && {\n          id: referenceId\n        })\n      },\n      floating: {\n        ...floatingProps,\n        ...(role === 'menu' && {\n          'aria-labelledby': referenceId\n        })\n      }\n    };\n  }, [enabled, role, open, floatingId, referenceId]);\n}\n\n// Converts a JS style key like `backgroundColor` to a CSS transition-property\n// like `background-color`.\nconst camelCaseToKebabCase = str => str.replace(/[A-Z]+(?![a-z])|[A-Z]/g, ($, ofs) => (ofs ? '-' : '') + $.toLowerCase());\nfunction execWithArgsOrReturn(valueOrFn, args) {\n  return typeof valueOrFn === 'function' ? valueOrFn(args) : valueOrFn;\n}\nfunction useDelayUnmount(open, durationMs) {\n  const [isMounted, setIsMounted] = React.useState(open);\n  if (open && !isMounted) {\n    setIsMounted(true);\n  }\n  React.useEffect(() => {\n    if (!open) {\n      const timeout = setTimeout(() => setIsMounted(false), durationMs);\n      return () => clearTimeout(timeout);\n    }\n  }, [open, durationMs]);\n  return isMounted;\n}\n/**\n * Provides a status string to apply CSS transitions to a floating element,\n * correctly handling placement-aware transitions.\n * @see https://floating-ui.com/docs/useTransition#usetransitionstatus\n */\nfunction useTransitionStatus(context, props) {\n  if (props === void 0) {\n    props = {};\n  }\n  const {\n    open,\n    elements: {\n      floating\n    }\n  } = context;\n  const {\n    duration = 250\n  } = props;\n  const isNumberDuration = typeof duration === 'number';\n  const closeDuration = (isNumberDuration ? duration : duration.close) || 0;\n  const [initiated, setInitiated] = React.useState(false);\n  const [status, setStatus] = React.useState('unmounted');\n  const isMounted = useDelayUnmount(open, closeDuration);\n\n  // `initiated` check prevents this `setState` call from breaking\n  // <FloatingPortal />. This call is necessary to ensure subsequent opens\n  // after the initial one allows the correct side animation to play when the\n  // placement has changed.\n  index(() => {\n    if (initiated && !isMounted) {\n      setStatus('unmounted');\n    }\n  }, [initiated, isMounted]);\n  index(() => {\n    if (!floating) return;\n    if (open) {\n      setStatus('initial');\n      const frame = requestAnimationFrame(() => {\n        setStatus('open');\n      });\n      return () => {\n        cancelAnimationFrame(frame);\n      };\n    } else {\n      setInitiated(true);\n      setStatus('close');\n    }\n  }, [open, floating]);\n  return {\n    isMounted,\n    status\n  };\n}\n/**\n * Provides styles to apply CSS transitions to a floating element, correctly\n * handling placement-aware transitions. Wrapper around `useTransitionStatus`.\n * @see https://floating-ui.com/docs/useTransition#usetransitionstyles\n */\nfunction useTransitionStyles(context, props) {\n  if (props === void 0) {\n    props = {};\n  }\n  const {\n    initial: unstable_initial = {\n      opacity: 0\n    },\n    open: unstable_open,\n    close: unstable_close,\n    common: unstable_common,\n    duration = 250\n  } = props;\n  const placement = context.placement;\n  const side = placement.split('-')[0];\n  const fnArgs = React.useMemo(() => ({\n    side,\n    placement\n  }), [side, placement]);\n  const isNumberDuration = typeof duration === 'number';\n  const openDuration = (isNumberDuration ? duration : duration.open) || 0;\n  const closeDuration = (isNumberDuration ? duration : duration.close) || 0;\n  const [styles, setStyles] = React.useState(() => ({\n    ...execWithArgsOrReturn(unstable_common, fnArgs),\n    ...execWithArgsOrReturn(unstable_initial, fnArgs)\n  }));\n  const {\n    isMounted,\n    status\n  } = useTransitionStatus(context, {\n    duration\n  });\n  const initialRef = useLatestRef(unstable_initial);\n  const openRef = useLatestRef(unstable_open);\n  const closeRef = useLatestRef(unstable_close);\n  const commonRef = useLatestRef(unstable_common);\n  index(() => {\n    const initialStyles = execWithArgsOrReturn(initialRef.current, fnArgs);\n    const closeStyles = execWithArgsOrReturn(closeRef.current, fnArgs);\n    const commonStyles = execWithArgsOrReturn(commonRef.current, fnArgs);\n    const openStyles = execWithArgsOrReturn(openRef.current, fnArgs) || Object.keys(initialStyles).reduce((acc, key) => {\n      acc[key] = '';\n      return acc;\n    }, {});\n    if (status === 'initial') {\n      setStyles(styles => ({\n        transitionProperty: styles.transitionProperty,\n        ...commonStyles,\n        ...initialStyles\n      }));\n    }\n    if (status === 'open') {\n      setStyles({\n        transitionProperty: Object.keys(openStyles).map(camelCaseToKebabCase).join(','),\n        transitionDuration: openDuration + \"ms\",\n        ...commonStyles,\n        ...openStyles\n      });\n    }\n    if (status === 'close') {\n      const styles = closeStyles || initialStyles;\n      setStyles({\n        transitionProperty: Object.keys(styles).map(camelCaseToKebabCase).join(','),\n        transitionDuration: closeDuration + \"ms\",\n        ...commonStyles,\n        ...styles\n      });\n    }\n  }, [closeDuration, closeRef, initialRef, openRef, commonRef, openDuration, status, fnArgs]);\n  return {\n    isMounted,\n    styles\n  };\n}\n\n/**\n * Provides a matching callback that can be used to focus an item as the user\n * types, often used in tandem with `useListNavigation()`.\n * @see https://floating-ui.com/docs/useTypeahead\n */\nfunction useTypeahead(context, props) {\n  var _ref;\n  const {\n    open,\n    dataRef\n  } = context;\n  const {\n    listRef,\n    activeIndex,\n    onMatch: unstable_onMatch,\n    onTypingChange: unstable_onTypingChange,\n    enabled = true,\n    findMatch = null,\n    resetMs = 750,\n    ignoreKeys = [],\n    selectedIndex = null\n  } = props;\n  const timeoutIdRef = React.useRef();\n  const stringRef = React.useRef('');\n  const prevIndexRef = React.useRef((_ref = selectedIndex != null ? selectedIndex : activeIndex) != null ? _ref : -1);\n  const matchIndexRef = React.useRef(null);\n  const onMatch = useEffectEvent(unstable_onMatch);\n  const onTypingChange = useEffectEvent(unstable_onTypingChange);\n  const findMatchRef = useLatestRef(findMatch);\n  const ignoreKeysRef = useLatestRef(ignoreKeys);\n  index(() => {\n    if (open) {\n      clearTimeout(timeoutIdRef.current);\n      matchIndexRef.current = null;\n      stringRef.current = '';\n    }\n  }, [open]);\n  index(() => {\n    // Sync arrow key navigation but not typeahead navigation.\n    if (open && stringRef.current === '') {\n      var _ref2;\n      prevIndexRef.current = (_ref2 = selectedIndex != null ? selectedIndex : activeIndex) != null ? _ref2 : -1;\n    }\n  }, [open, selectedIndex, activeIndex]);\n  return React.useMemo(() => {\n    if (!enabled) {\n      return {};\n    }\n    function setTypingChange(value) {\n      if (value) {\n        if (!dataRef.current.typing) {\n          dataRef.current.typing = value;\n          onTypingChange(value);\n        }\n      } else {\n        if (dataRef.current.typing) {\n          dataRef.current.typing = value;\n          onTypingChange(value);\n        }\n      }\n    }\n    function getMatchingIndex(list, orderedList, string) {\n      const str = findMatchRef.current ? findMatchRef.current(orderedList, string) : orderedList.find(text => (text == null ? void 0 : text.toLocaleLowerCase().indexOf(string.toLocaleLowerCase())) === 0);\n      return str ? list.indexOf(str) : -1;\n    }\n    function onKeyDown(event) {\n      const listContent = listRef.current;\n      if (stringRef.current.length > 0 && stringRef.current[0] !== ' ') {\n        if (getMatchingIndex(listContent, listContent, stringRef.current) === -1) {\n          setTypingChange(false);\n        } else if (event.key === ' ') {\n          stopEvent(event);\n        }\n      }\n      if (listContent == null || ignoreKeysRef.current.includes(event.key) ||\n      // Character key.\n      event.key.length !== 1 ||\n      // Modifier key.\n      event.ctrlKey || event.metaKey || event.altKey) {\n        return;\n      }\n      if (open && event.key !== ' ') {\n        stopEvent(event);\n        setTypingChange(true);\n      }\n\n      // Bail out if the list contains a word like \"llama\" or \"aaron\". TODO:\n      // allow it in this case, too.\n      const allowRapidSuccessionOfFirstLetter = listContent.every(text => {\n        var _text$, _text$2;\n        return text ? ((_text$ = text[0]) == null ? void 0 : _text$.toLocaleLowerCase()) !== ((_text$2 = text[1]) == null ? void 0 : _text$2.toLocaleLowerCase()) : true;\n      });\n\n      // Allows the user to cycle through items that start with the same letter\n      // in rapid succession.\n      if (allowRapidSuccessionOfFirstLetter && stringRef.current === event.key) {\n        stringRef.current = '';\n        prevIndexRef.current = matchIndexRef.current;\n      }\n      stringRef.current += event.key;\n      clearTimeout(timeoutIdRef.current);\n      timeoutIdRef.current = setTimeout(() => {\n        stringRef.current = '';\n        prevIndexRef.current = matchIndexRef.current;\n        setTypingChange(false);\n      }, resetMs);\n      const prevIndex = prevIndexRef.current;\n      const index = getMatchingIndex(listContent, [...listContent.slice((prevIndex || 0) + 1), ...listContent.slice(0, (prevIndex || 0) + 1)], stringRef.current);\n      if (index !== -1) {\n        onMatch(index);\n        matchIndexRef.current = index;\n      } else if (event.key !== ' ') {\n        stringRef.current = '';\n        setTypingChange(false);\n      }\n    }\n    return {\n      reference: {\n        onKeyDown\n      },\n      floating: {\n        onKeyDown,\n        onKeyUp(event) {\n          if (event.key === ' ') {\n            setTypingChange(false);\n          }\n        }\n      }\n    };\n  }, [enabled, open, dataRef, listRef, resetMs, ignoreKeysRef, findMatchRef, onMatch, onTypingChange]);\n}\n\nfunction getArgsWithCustomFloatingHeight(state, height) {\n  return {\n    ...state,\n    rects: {\n      ...state.rects,\n      floating: {\n        ...state.rects.floating,\n        height\n      }\n    }\n  };\n}\n/**\n * Positions the floating element such that an inner element inside\n * of it is anchored to the reference element.\n * @see https://floating-ui.com/docs/inner\n */\nconst inner = props => ({\n  name: 'inner',\n  options: props,\n  async fn(state) {\n    const {\n      listRef,\n      overflowRef,\n      onFallbackChange,\n      offset: innerOffset = 0,\n      index = 0,\n      minItemsVisible = 4,\n      referenceOverflowThreshold = 0,\n      scrollRef,\n      ...detectOverflowOptions\n    } = props;\n    const {\n      rects,\n      elements: {\n        floating\n      }\n    } = state;\n    const item = listRef.current[index];\n    if (process.env.NODE_ENV !== \"production\") {\n      if (!state.placement.startsWith('bottom')) {\n        console.warn(['Floating UI: `placement` side must be \"bottom\" when using the', '`inner` middleware.'].join(' '));\n      }\n    }\n    if (!item) {\n      return {};\n    }\n    const nextArgs = {\n      ...state,\n      ...(await offset(-item.offsetTop - floating.clientTop - rects.reference.height / 2 - item.offsetHeight / 2 - innerOffset).fn(state))\n    };\n    const el = (scrollRef == null ? void 0 : scrollRef.current) || floating;\n    const overflow = await detectOverflow(getArgsWithCustomFloatingHeight(nextArgs, el.scrollHeight), detectOverflowOptions);\n    const refOverflow = await detectOverflow(nextArgs, {\n      ...detectOverflowOptions,\n      elementContext: 'reference'\n    });\n    const diffY = Math.max(0, overflow.top);\n    const nextY = nextArgs.y + diffY;\n    const maxHeight = Math.max(0, el.scrollHeight - diffY - Math.max(0, overflow.bottom));\n    el.style.maxHeight = maxHeight + \"px\";\n    el.scrollTop = diffY;\n\n    // There is not enough space, fallback to standard anchored positioning\n    if (onFallbackChange) {\n      if (el.offsetHeight < item.offsetHeight * Math.min(minItemsVisible, listRef.current.length - 1) - 1 || refOverflow.top >= -referenceOverflowThreshold || refOverflow.bottom >= -referenceOverflowThreshold) {\n        flushSync(() => onFallbackChange(true));\n      } else {\n        flushSync(() => onFallbackChange(false));\n      }\n    }\n    if (overflowRef) {\n      overflowRef.current = await detectOverflow(getArgsWithCustomFloatingHeight({\n        ...nextArgs,\n        y: nextY\n      }, el.offsetHeight), detectOverflowOptions);\n    }\n    return {\n      y: nextY\n    };\n  }\n});\n/**\n * Changes the `inner` middleware's `offset` upon a `wheel` event to\n * expand the floating element's height, revealing more list items.\n * @see https://floating-ui.com/docs/inner\n */\nfunction useInnerOffset(context, props) {\n  const {\n    open,\n    elements\n  } = context;\n  const {\n    enabled = true,\n    overflowRef,\n    scrollRef,\n    onChange: unstable_onChange\n  } = props;\n  const onChange = useEffectEvent(unstable_onChange);\n  const controlledScrollingRef = React.useRef(false);\n  const prevScrollTopRef = React.useRef(null);\n  const initialOverflowRef = React.useRef(null);\n  React.useEffect(() => {\n    if (!enabled) {\n      return;\n    }\n    function onWheel(e) {\n      if (e.ctrlKey || !el || overflowRef.current == null) {\n        return;\n      }\n      const dY = e.deltaY;\n      const isAtTop = overflowRef.current.top >= -0.5;\n      const isAtBottom = overflowRef.current.bottom >= -0.5;\n      const remainingScroll = el.scrollHeight - el.clientHeight;\n      const sign = dY < 0 ? -1 : 1;\n      const method = dY < 0 ? 'max' : 'min';\n      if (el.scrollHeight <= el.clientHeight) {\n        return;\n      }\n      if (!isAtTop && dY > 0 || !isAtBottom && dY < 0) {\n        e.preventDefault();\n        flushSync(() => {\n          onChange(d => d + Math[method](dY, remainingScroll * sign));\n        });\n      } else if (/firefox/i.test(getUserAgent())) {\n        // Needed to propagate scrolling during momentum scrolling phase once\n        // it gets limited by the boundary. UX improvement, not critical.\n        el.scrollTop += dY;\n      }\n    }\n    const el = (scrollRef == null ? void 0 : scrollRef.current) || elements.floating;\n    if (open && el) {\n      el.addEventListener('wheel', onWheel);\n\n      // Wait for the position to be ready.\n      requestAnimationFrame(() => {\n        prevScrollTopRef.current = el.scrollTop;\n        if (overflowRef.current != null) {\n          initialOverflowRef.current = {\n            ...overflowRef.current\n          };\n        }\n      });\n      return () => {\n        prevScrollTopRef.current = null;\n        initialOverflowRef.current = null;\n        el.removeEventListener('wheel', onWheel);\n      };\n    }\n  }, [enabled, open, elements.floating, overflowRef, scrollRef, onChange]);\n  return React.useMemo(() => {\n    if (!enabled) {\n      return {};\n    }\n    return {\n      floating: {\n        onKeyDown() {\n          controlledScrollingRef.current = true;\n        },\n        onWheel() {\n          controlledScrollingRef.current = false;\n        },\n        onPointerMove() {\n          controlledScrollingRef.current = false;\n        },\n        onScroll() {\n          const el = (scrollRef == null ? void 0 : scrollRef.current) || elements.floating;\n          if (!overflowRef.current || !el || !controlledScrollingRef.current) {\n            return;\n          }\n          if (prevScrollTopRef.current !== null) {\n            const scrollDiff = el.scrollTop - prevScrollTopRef.current;\n            if (overflowRef.current.bottom < -0.5 && scrollDiff < -1 || overflowRef.current.top < -0.5 && scrollDiff > 1) {\n              flushSync(() => onChange(d => d + scrollDiff));\n            }\n          }\n\n          // [Firefox] Wait for the height change to have been applied.\n          requestAnimationFrame(() => {\n            prevScrollTopRef.current = el.scrollTop;\n          });\n        }\n      }\n    };\n  }, [enabled, overflowRef, elements.floating, scrollRef, onChange]);\n}\n\nfunction isPointInPolygon(point, polygon) {\n  const [x, y] = point;\n  let isInside = false;\n  const length = polygon.length;\n  for (let i = 0, j = length - 1; i < length; j = i++) {\n    const [xi, yi] = polygon[i] || [0, 0];\n    const [xj, yj] = polygon[j] || [0, 0];\n    const intersect = yi >= y !== yj >= y && x <= (xj - xi) * (y - yi) / (yj - yi) + xi;\n    if (intersect) {\n      isInside = !isInside;\n    }\n  }\n  return isInside;\n}\nfunction isInside(point, rect) {\n  return point[0] >= rect.x && point[0] <= rect.x + rect.width && point[1] >= rect.y && point[1] <= rect.y + rect.height;\n}\n/**\n * Generates a safe polygon area that the user can traverse without closing the\n * floating element once leaving the reference element.\n * @see https://floating-ui.com/docs/useHover#safePolygon\n */\nfunction safePolygon(options) {\n  if (options === void 0) {\n    options = {};\n  }\n  const {\n    buffer = 0.5,\n    blockPointerEvents = false,\n    requireIntent = true\n  } = options;\n  let timeoutId;\n  let hasLanded = false;\n  let lastX = null;\n  let lastY = null;\n  let lastCursorTime = performance.now();\n  function getCursorSpeed(x, y) {\n    const currentTime = performance.now();\n    const elapsedTime = currentTime - lastCursorTime;\n    if (lastX === null || lastY === null || elapsedTime === 0) {\n      lastX = x;\n      lastY = y;\n      lastCursorTime = currentTime;\n      return null;\n    }\n    const deltaX = x - lastX;\n    const deltaY = y - lastY;\n    const distance = Math.sqrt(deltaX * deltaX + deltaY * deltaY);\n    const speed = distance / elapsedTime; // px / ms\n\n    lastX = x;\n    lastY = y;\n    lastCursorTime = currentTime;\n    return speed;\n  }\n  const fn = _ref => {\n    let {\n      x,\n      y,\n      placement,\n      elements,\n      onClose,\n      nodeId,\n      tree\n    } = _ref;\n    return function onMouseMove(event) {\n      function close() {\n        clearTimeout(timeoutId);\n        onClose();\n      }\n      clearTimeout(timeoutId);\n      if (!elements.domReference || !elements.floating || placement == null || x == null || y == null) {\n        return;\n      }\n      const {\n        clientX,\n        clientY\n      } = event;\n      const clientPoint = [clientX, clientY];\n      const target = getTarget(event);\n      const isLeave = event.type === 'mouseleave';\n      const isOverFloatingEl = contains(elements.floating, target);\n      const isOverReferenceEl = contains(elements.domReference, target);\n      const refRect = elements.domReference.getBoundingClientRect();\n      const rect = elements.floating.getBoundingClientRect();\n      const side = placement.split('-')[0];\n      const cursorLeaveFromRight = x > rect.right - rect.width / 2;\n      const cursorLeaveFromBottom = y > rect.bottom - rect.height / 2;\n      const isOverReferenceRect = isInside(clientPoint, refRect);\n      const isFloatingWider = rect.width > refRect.width;\n      const isFloatingTaller = rect.height > refRect.height;\n      const left = (isFloatingWider ? refRect : rect).left;\n      const right = (isFloatingWider ? refRect : rect).right;\n      const top = (isFloatingTaller ? refRect : rect).top;\n      const bottom = (isFloatingTaller ? refRect : rect).bottom;\n      if (isOverFloatingEl) {\n        hasLanded = true;\n        if (!isLeave) {\n          return;\n        }\n      }\n      if (isOverReferenceEl) {\n        hasLanded = false;\n      }\n      if (isOverReferenceEl && !isLeave) {\n        hasLanded = true;\n        return;\n      }\n\n      // Prevent overlapping floating element from being stuck in an open-close\n      // loop: https://github.com/floating-ui/floating-ui/issues/1910\n      if (isLeave && isElement(event.relatedTarget) && contains(elements.floating, event.relatedTarget)) {\n        return;\n      }\n\n      // If any nested child is open, abort.\n      if (tree && getChildren(tree.nodesRef.current, nodeId).some(_ref2 => {\n        let {\n          context\n        } = _ref2;\n        return context == null ? void 0 : context.open;\n      })) {\n        return;\n      }\n\n      // If the pointer is leaving from the opposite side, the \"buffer\" logic\n      // creates a point where the floating element remains open, but should be\n      // ignored.\n      // A constant of 1 handles floating point rounding errors.\n      if (side === 'top' && y >= refRect.bottom - 1 || side === 'bottom' && y <= refRect.top + 1 || side === 'left' && x >= refRect.right - 1 || side === 'right' && x <= refRect.left + 1) {\n        return close();\n      }\n\n      // Ignore when the cursor is within the rectangular trough between the\n      // two elements. Since the triangle is created from the cursor point,\n      // which can start beyond the ref element's edge, traversing back and\n      // forth from the ref to the floating element can cause it to close. This\n      // ensures it always remains open in that case.\n      let rectPoly = [];\n      switch (side) {\n        case 'top':\n          rectPoly = [[left, refRect.top + 1], [left, rect.bottom - 1], [right, rect.bottom - 1], [right, refRect.top + 1]];\n          break;\n        case 'bottom':\n          rectPoly = [[left, rect.top + 1], [left, refRect.bottom - 1], [right, refRect.bottom - 1], [right, rect.top + 1]];\n          break;\n        case 'left':\n          rectPoly = [[rect.right - 1, bottom], [rect.right - 1, top], [refRect.left + 1, top], [refRect.left + 1, bottom]];\n          break;\n        case 'right':\n          rectPoly = [[refRect.right - 1, bottom], [refRect.right - 1, top], [rect.left + 1, top], [rect.left + 1, bottom]];\n          break;\n      }\n      function getPolygon(_ref3) {\n        let [x, y] = _ref3;\n        switch (side) {\n          case 'top':\n            {\n              const cursorPointOne = [isFloatingWider ? x + buffer / 2 : cursorLeaveFromRight ? x + buffer * 4 : x - buffer * 4, y + buffer + 1];\n              const cursorPointTwo = [isFloatingWider ? x - buffer / 2 : cursorLeaveFromRight ? x + buffer * 4 : x - buffer * 4, y + buffer + 1];\n              const commonPoints = [[rect.left, cursorLeaveFromRight ? rect.bottom - buffer : isFloatingWider ? rect.bottom - buffer : rect.top], [rect.right, cursorLeaveFromRight ? isFloatingWider ? rect.bottom - buffer : rect.top : rect.bottom - buffer]];\n              return [cursorPointOne, cursorPointTwo, ...commonPoints];\n            }\n          case 'bottom':\n            {\n              const cursorPointOne = [isFloatingWider ? x + buffer / 2 : cursorLeaveFromRight ? x + buffer * 4 : x - buffer * 4, y - buffer];\n              const cursorPointTwo = [isFloatingWider ? x - buffer / 2 : cursorLeaveFromRight ? x + buffer * 4 : x - buffer * 4, y - buffer];\n              const commonPoints = [[rect.left, cursorLeaveFromRight ? rect.top + buffer : isFloatingWider ? rect.top + buffer : rect.bottom], [rect.right, cursorLeaveFromRight ? isFloatingWider ? rect.top + buffer : rect.bottom : rect.top + buffer]];\n              return [cursorPointOne, cursorPointTwo, ...commonPoints];\n            }\n          case 'left':\n            {\n              const cursorPointOne = [x + buffer + 1, isFloatingTaller ? y + buffer / 2 : cursorLeaveFromBottom ? y + buffer * 4 : y - buffer * 4];\n              const cursorPointTwo = [x + buffer + 1, isFloatingTaller ? y - buffer / 2 : cursorLeaveFromBottom ? y + buffer * 4 : y - buffer * 4];\n              const commonPoints = [[cursorLeaveFromBottom ? rect.right - buffer : isFloatingTaller ? rect.right - buffer : rect.left, rect.top], [cursorLeaveFromBottom ? isFloatingTaller ? rect.right - buffer : rect.left : rect.right - buffer, rect.bottom]];\n              return [...commonPoints, cursorPointOne, cursorPointTwo];\n            }\n          case 'right':\n            {\n              const cursorPointOne = [x - buffer, isFloatingTaller ? y + buffer / 2 : cursorLeaveFromBottom ? y + buffer * 4 : y - buffer * 4];\n              const cursorPointTwo = [x - buffer, isFloatingTaller ? y - buffer / 2 : cursorLeaveFromBottom ? y + buffer * 4 : y - buffer * 4];\n              const commonPoints = [[cursorLeaveFromBottom ? rect.left + buffer : isFloatingTaller ? rect.left + buffer : rect.right, rect.top], [cursorLeaveFromBottom ? isFloatingTaller ? rect.left + buffer : rect.right : rect.left + buffer, rect.bottom]];\n              return [cursorPointOne, cursorPointTwo, ...commonPoints];\n            }\n        }\n      }\n      if (isPointInPolygon([clientX, clientY], rectPoly)) {\n        return;\n      } else if (hasLanded && !isOverReferenceRect) {\n        return close();\n      }\n      if (!isLeave && requireIntent) {\n        const cursorSpeed = getCursorSpeed(event.clientX, event.clientY);\n        const cursorSpeedThreshold = 0.1;\n        if (cursorSpeed !== null && cursorSpeed < cursorSpeedThreshold) {\n          return close();\n        }\n      }\n      if (!isPointInPolygon([clientX, clientY], getPolygon([x, y]))) {\n        close();\n      } else if (!hasLanded && requireIntent) {\n        timeoutId = window.setTimeout(close, 40);\n      }\n    };\n  };\n  fn.__options = {\n    blockPointerEvents\n  };\n  return fn;\n}\n\nexport { Composite, CompositeItem, FloatingArrow, FloatingDelayGroup, FloatingFocusManager, FloatingList, FloatingNode, FloatingOverlay, FloatingPortal, FloatingTree, inner, safePolygon, useClick, useClientPoint, useDelayGroup, useDelayGroupContext, useDismiss, useFloating, useFloatingNodeId, useFloatingParentNodeId, useFloatingPortalNode, useFloatingTree, useFocus, useHover, useId, useInnerOffset, useInteractions, useListItem, useListNavigation, useMergeRefs, useRole, useTransitionStatus, useTransitionStyles, useTypeahead };\n", "const sides = ['top', 'right', 'bottom', 'left'];\nconst alignments = ['start', 'end'];\nconst placements = /*#__PURE__*/sides.reduce((acc, side) => acc.concat(side, side + \"-\" + alignments[0], side + \"-\" + alignments[1]), []);\nconst min = Math.min;\nconst max = Math.max;\nconst round = Math.round;\nconst floor = Math.floor;\nconst createCoords = v => ({\n  x: v,\n  y: v\n});\nconst oppositeSideMap = {\n  left: 'right',\n  right: 'left',\n  bottom: 'top',\n  top: 'bottom'\n};\nconst oppositeAlignmentMap = {\n  start: 'end',\n  end: 'start'\n};\nfunction clamp(start, value, end) {\n  return max(start, min(value, end));\n}\nfunction evaluate(value, param) {\n  return typeof value === 'function' ? value(param) : value;\n}\nfunction getSide(placement) {\n  return placement.split('-')[0];\n}\nfunction getAlignment(placement) {\n  return placement.split('-')[1];\n}\nfunction getOppositeAxis(axis) {\n  return axis === 'x' ? 'y' : 'x';\n}\nfunction getAxisLength(axis) {\n  return axis === 'y' ? 'height' : 'width';\n}\nfunction getSideAxis(placement) {\n  return ['top', 'bottom'].includes(getSide(placement)) ? 'y' : 'x';\n}\nfunction getAlignmentAxis(placement) {\n  return getOppositeAxis(getSideAxis(placement));\n}\nfunction getAlignmentSides(placement, rects, rtl) {\n  if (rtl === void 0) {\n    rtl = false;\n  }\n  const alignment = getAlignment(placement);\n  const alignmentAxis = getAlignmentAxis(placement);\n  const length = getAxisLength(alignmentAxis);\n  let mainAlignmentSide = alignmentAxis === 'x' ? alignment === (rtl ? 'end' : 'start') ? 'right' : 'left' : alignment === 'start' ? 'bottom' : 'top';\n  if (rects.reference[length] > rects.floating[length]) {\n    mainAlignmentSide = getOppositePlacement(mainAlignmentSide);\n  }\n  return [mainAlignmentSide, getOppositePlacement(mainAlignmentSide)];\n}\nfunction getExpandedPlacements(placement) {\n  const oppositePlacement = getOppositePlacement(placement);\n  return [getOppositeAlignmentPlacement(placement), oppositePlacement, getOppositeAlignmentPlacement(oppositePlacement)];\n}\nfunction getOppositeAlignmentPlacement(placement) {\n  return placement.replace(/start|end/g, alignment => oppositeAlignmentMap[alignment]);\n}\nfunction getSideList(side, isStart, rtl) {\n  const lr = ['left', 'right'];\n  const rl = ['right', 'left'];\n  const tb = ['top', 'bottom'];\n  const bt = ['bottom', 'top'];\n  switch (side) {\n    case 'top':\n    case 'bottom':\n      if (rtl) return isStart ? rl : lr;\n      return isStart ? lr : rl;\n    case 'left':\n    case 'right':\n      return isStart ? tb : bt;\n    default:\n      return [];\n  }\n}\nfunction getOppositeAxisPlacements(placement, flipAlignment, direction, rtl) {\n  const alignment = getAlignment(placement);\n  let list = getSideList(getSide(placement), direction === 'start', rtl);\n  if (alignment) {\n    list = list.map(side => side + \"-\" + alignment);\n    if (flipAlignment) {\n      list = list.concat(list.map(getOppositeAlignmentPlacement));\n    }\n  }\n  return list;\n}\nfunction getOppositePlacement(placement) {\n  return placement.replace(/left|right|bottom|top/g, side => oppositeSideMap[side]);\n}\nfunction expandPaddingObject(padding) {\n  return {\n    top: 0,\n    right: 0,\n    bottom: 0,\n    left: 0,\n    ...padding\n  };\n}\nfunction getPaddingObject(padding) {\n  return typeof padding !== 'number' ? expandPaddingObject(padding) : {\n    top: padding,\n    right: padding,\n    bottom: padding,\n    left: padding\n  };\n}\nfunction rectToClientRect(rect) {\n  return {\n    ...rect,\n    top: rect.y,\n    left: rect.x,\n    right: rect.x + rect.width,\n    bottom: rect.y + rect.height\n  };\n}\n\nexport { alignments, clamp, createCoords, evaluate, expandPaddingObject, floor, getAlignment, getAlignmentAxis, getAlignmentSides, getAxisLength, getExpandedPlacements, getOppositeAlignmentPlacement, getOppositeAxis, getOppositeAxisPlacements, getOppositePlacement, getPaddingObject, getSide, getSideAxis, max, min, placements, rectToClientRect, round, sides };\n", "function getNodeName(node) {\n  if (isNode(node)) {\n    return (node.nodeName || '').toLowerCase();\n  }\n  // Mocked nodes in testing environments may not be instances of Node. By\n  // returning `#document` an infinite loop won't occur.\n  // https://github.com/floating-ui/floating-ui/issues/2317\n  return '#document';\n}\nfunction getWindow(node) {\n  var _node$ownerDocument;\n  return (node == null ? void 0 : (_node$ownerDocument = node.ownerDocument) == null ? void 0 : _node$ownerDocument.defaultView) || window;\n}\nfunction getDocumentElement(node) {\n  var _ref;\n  return (_ref = (isNode(node) ? node.ownerDocument : node.document) || window.document) == null ? void 0 : _ref.documentElement;\n}\nfunction isNode(value) {\n  return value instanceof Node || value instanceof getWindow(value).Node;\n}\nfunction isElement(value) {\n  return value instanceof Element || value instanceof getWindow(value).Element;\n}\nfunction isHTMLElement(value) {\n  return value instanceof HTMLElement || value instanceof getWindow(value).HTMLElement;\n}\nfunction isShadowRoot(value) {\n  // Browsers without `ShadowRoot` support.\n  if (typeof ShadowRoot === 'undefined') {\n    return false;\n  }\n  return value instanceof ShadowRoot || value instanceof getWindow(value).ShadowRoot;\n}\nfunction isOverflowElement(element) {\n  const {\n    overflow,\n    overflowX,\n    overflowY,\n    display\n  } = getComputedStyle(element);\n  return /auto|scroll|overlay|hidden|clip/.test(overflow + overflowY + overflowX) && !['inline', 'contents'].includes(display);\n}\nfunction isTableElement(element) {\n  return ['table', 'td', 'th'].includes(getNodeName(element));\n}\nfunction isContainingBlock(element) {\n  const webkit = isWebKit();\n  const css = getComputedStyle(element);\n\n  // https://developer.mozilla.org/en-US/docs/Web/CSS/Containing_block#identifying_the_containing_block\n  return css.transform !== 'none' || css.perspective !== 'none' || (css.containerType ? css.containerType !== 'normal' : false) || !webkit && (css.backdropFilter ? css.backdropFilter !== 'none' : false) || !webkit && (css.filter ? css.filter !== 'none' : false) || ['transform', 'perspective', 'filter'].some(value => (css.willChange || '').includes(value)) || ['paint', 'layout', 'strict', 'content'].some(value => (css.contain || '').includes(value));\n}\nfunction getContainingBlock(element) {\n  let currentNode = getParentNode(element);\n  while (isHTMLElement(currentNode) && !isLastTraversableNode(currentNode)) {\n    if (isContainingBlock(currentNode)) {\n      return currentNode;\n    } else {\n      currentNode = getParentNode(currentNode);\n    }\n  }\n  return null;\n}\nfunction isWebKit() {\n  if (typeof CSS === 'undefined' || !CSS.supports) return false;\n  return CSS.supports('-webkit-backdrop-filter', 'none');\n}\nfunction isLastTraversableNode(node) {\n  return ['html', 'body', '#document'].includes(getNodeName(node));\n}\nfunction getComputedStyle(element) {\n  return getWindow(element).getComputedStyle(element);\n}\nfunction getNodeScroll(element) {\n  if (isElement(element)) {\n    return {\n      scrollLeft: element.scrollLeft,\n      scrollTop: element.scrollTop\n    };\n  }\n  return {\n    scrollLeft: element.pageXOffset,\n    scrollTop: element.pageYOffset\n  };\n}\nfunction getParentNode(node) {\n  if (getNodeName(node) === 'html') {\n    return node;\n  }\n  const result =\n  // Step into the shadow DOM of the parent of a slotted node.\n  node.assignedSlot ||\n  // DOM Element detected.\n  node.parentNode ||\n  // ShadowRoot detected.\n  isShadowRoot(node) && node.host ||\n  // Fallback.\n  getDocumentElement(node);\n  return isShadowRoot(result) ? result.host : result;\n}\nfunction getNearestOverflowAncestor(node) {\n  const parentNode = getParentNode(node);\n  if (isLastTraversableNode(parentNode)) {\n    return node.ownerDocument ? node.ownerDocument.body : node.body;\n  }\n  if (isHTMLElement(parentNode) && isOverflowElement(parentNode)) {\n    return parentNode;\n  }\n  return getNearestOverflowAncestor(parentNode);\n}\nfunction getOverflowAncestors(node, list, traverseIframes) {\n  var _node$ownerDocument2;\n  if (list === void 0) {\n    list = [];\n  }\n  if (traverseIframes === void 0) {\n    traverseIframes = true;\n  }\n  const scrollableAncestor = getNearestOverflowAncestor(node);\n  const isBody = scrollableAncestor === ((_node$ownerDocument2 = node.ownerDocument) == null ? void 0 : _node$ownerDocument2.body);\n  const win = getWindow(scrollableAncestor);\n  if (isBody) {\n    return list.concat(win, win.visualViewport || [], isOverflowElement(scrollableAncestor) ? scrollableAncestor : [], win.frameElement && traverseIframes ? getOverflowAncestors(win.frameElement) : []);\n  }\n  return list.concat(scrollableAncestor, getOverflowAncestors(scrollableAncestor, [], traverseIframes));\n}\n\nexport { getComputedStyle, getContainingBlock, getDocumentElement, getNearestOverflowAncestor, getNodeName, getNodeScroll, getOverflowAncestors, getParentNode, getWindow, isContainingBlock, isElement, isHTMLElement, isLastTraversableNode, isNode, isOverflowElement, isShadowRoot, isTableElement, isWebKit };\n", "import { isShadowRoot, isHTMLElement } from '@floating-ui/utils/dom';\n\nfunction activeElement(doc) {\n  let activeElement = doc.activeElement;\n  while (((_activeElement = activeElement) == null ? void 0 : (_activeElement$shadow = _activeElement.shadowRoot) == null ? void 0 : _activeElement$shadow.activeElement) != null) {\n    var _activeElement, _activeElement$shadow;\n    activeElement = activeElement.shadowRoot.activeElement;\n  }\n  return activeElement;\n}\nfunction contains(parent, child) {\n  if (!parent || !child) {\n    return false;\n  }\n  const rootNode = child.getRootNode && child.getRootNode();\n\n  // First, attempt with faster native method\n  if (parent.contains(child)) {\n    return true;\n  }\n\n  // then fallback to custom implementation with Shadow DOM support\n  if (rootNode && isShadowRoot(rootNode)) {\n    let next = child;\n    while (next) {\n      if (parent === next) {\n        return true;\n      }\n      // @ts-ignore\n      next = next.parentNode || next.host;\n    }\n  }\n\n  // Give up, the result is false\n  return false;\n}\n// Avoid Chrome DevTools blue warning.\nfunction getPlatform() {\n  const uaData = navigator.userAgentData;\n  if (uaData != null && uaData.platform) {\n    return uaData.platform;\n  }\n  return navigator.platform;\n}\nfunction getUserAgent() {\n  const uaData = navigator.userAgentData;\n  if (uaData && Array.isArray(uaData.brands)) {\n    return uaData.brands.map(_ref => {\n      let {\n        brand,\n        version\n      } = _ref;\n      return brand + \"/\" + version;\n    }).join(' ');\n  }\n  return navigator.userAgent;\n}\n\n// License: https://github.com/adobe/react-spectrum/blob/b35d5c02fe900badccd0cf1a8f23bb593419f238/packages/@react-aria/utils/src/isVirtualEvent.ts\nfunction isVirtualClick(event) {\n  if (event.mozInputSource === 0 && event.isTrusted) {\n    return true;\n  }\n  const androidRe = /Android/i;\n  if ((androidRe.test(getPlatform()) || androidRe.test(getUserAgent())) && event.pointerType) {\n    return event.type === 'click' && event.buttons === 1;\n  }\n  return event.detail === 0 && !event.pointerType;\n}\nfunction isVirtualPointerEvent(event) {\n  return event.width === 0 && event.height === 0 || event.width === 1 && event.height === 1 && event.pressure === 0 && event.detail === 0 && event.pointerType !== 'mouse' ||\n  // iOS VoiceOver returns 0.333• for width/height.\n  event.width < 1 && event.height < 1 && event.pressure === 0 && event.detail === 0;\n}\nfunction isSafari() {\n  // Chrome DevTools does not complain about navigator.vendor\n  return /apple/i.test(navigator.vendor);\n}\nfunction isMac() {\n  return getPlatform().toLowerCase().startsWith('mac') && !navigator.maxTouchPoints;\n}\nfunction isMouseLikePointerType(pointerType, strict) {\n  // On some Linux machines with Chromium, mouse inputs return a `pointerType`\n  // of \"pen\": https://github.com/floating-ui/floating-ui/issues/2015\n  const values = ['mouse', 'pen'];\n  if (!strict) {\n    values.push('', undefined);\n  }\n  return values.includes(pointerType);\n}\nfunction isReactEvent(event) {\n  return 'nativeEvent' in event;\n}\nfunction isRootElement(element) {\n  return element.matches('html,body');\n}\nfunction getDocument(node) {\n  return (node == null ? void 0 : node.ownerDocument) || document;\n}\nfunction isEventTargetWithin(event, node) {\n  if (node == null) {\n    return false;\n  }\n  if ('composedPath' in event) {\n    return event.composedPath().includes(node);\n  }\n\n  // TS thinks `event` is of type never as it assumes all browsers support composedPath, but browsers without shadow dom don't\n  const e = event;\n  return e.target != null && node.contains(e.target);\n}\nfunction getTarget(event) {\n  if ('composedPath' in event) {\n    return event.composedPath()[0];\n  }\n\n  // TS thinks `event` is of type never as it assumes all browsers support\n  // `composedPath()`, but browsers without shadow DOM don't.\n  return event.target;\n}\nconst TYPEABLE_SELECTOR = \"input:not([type='hidden']):not([disabled]),\" + \"[contenteditable]:not([contenteditable='false']),textarea:not([disabled])\";\nfunction isTypeableElement(element) {\n  return isHTMLElement(element) && element.matches(TYPEABLE_SELECTOR);\n}\nfunction stopEvent(event) {\n  event.preventDefault();\n  event.stopPropagation();\n}\n\nexport { TYPEABLE_SELECTOR, activeElement, contains, getDocument, getPlatform, getTarget, getUserAgent, isEventTargetWithin, isMac, isMouseLikePointerType, isReactEvent, isRootElement, isSafari, isTypeableElement, isVirtualClick, isVirtualPointerEvent, stopEvent };\n", "/**\n * Custom positioning reference element.\n * @see https://floating-ui.com/docs/virtual-elements\n */\n\nconst sides = ['top', 'right', 'bottom', 'left'];\nconst alignments = ['start', 'end'];\nconst placements = /*#__PURE__*/sides.reduce((acc, side) => acc.concat(side, side + \"-\" + alignments[0], side + \"-\" + alignments[1]), []);\nconst min = Math.min;\nconst max = Math.max;\nconst round = Math.round;\nconst floor = Math.floor;\nconst createCoords = v => ({\n  x: v,\n  y: v\n});\nconst oppositeSideMap = {\n  left: 'right',\n  right: 'left',\n  bottom: 'top',\n  top: 'bottom'\n};\nconst oppositeAlignmentMap = {\n  start: 'end',\n  end: 'start'\n};\nfunction clamp(start, value, end) {\n  return max(start, min(value, end));\n}\nfunction evaluate(value, param) {\n  return typeof value === 'function' ? value(param) : value;\n}\nfunction getSide(placement) {\n  return placement.split('-')[0];\n}\nfunction getAlignment(placement) {\n  return placement.split('-')[1];\n}\nfunction getOppositeAxis(axis) {\n  return axis === 'x' ? 'y' : 'x';\n}\nfunction getAxisLength(axis) {\n  return axis === 'y' ? 'height' : 'width';\n}\nfunction getSideAxis(placement) {\n  return ['top', 'bottom'].includes(getSide(placement)) ? 'y' : 'x';\n}\nfunction getAlignmentAxis(placement) {\n  return getOppositeAxis(getSideAxis(placement));\n}\nfunction getAlignmentSides(placement, rects, rtl) {\n  if (rtl === void 0) {\n    rtl = false;\n  }\n  const alignment = getAlignment(placement);\n  const alignmentAxis = getAlignmentAxis(placement);\n  const length = getAxisLength(alignmentAxis);\n  let mainAlignmentSide = alignmentAxis === 'x' ? alignment === (rtl ? 'end' : 'start') ? 'right' : 'left' : alignment === 'start' ? 'bottom' : 'top';\n  if (rects.reference[length] > rects.floating[length]) {\n    mainAlignmentSide = getOppositePlacement(mainAlignmentSide);\n  }\n  return [mainAlignmentSide, getOppositePlacement(mainAlignmentSide)];\n}\nfunction getExpandedPlacements(placement) {\n  const oppositePlacement = getOppositePlacement(placement);\n  return [getOppositeAlignmentPlacement(placement), oppositePlacement, getOppositeAlignmentPlacement(oppositePlacement)];\n}\nfunction getOppositeAlignmentPlacement(placement) {\n  return placement.replace(/start|end/g, alignment => oppositeAlignmentMap[alignment]);\n}\nfunction getSideList(side, isStart, rtl) {\n  const lr = ['left', 'right'];\n  const rl = ['right', 'left'];\n  const tb = ['top', 'bottom'];\n  const bt = ['bottom', 'top'];\n  switch (side) {\n    case 'top':\n    case 'bottom':\n      if (rtl) return isStart ? rl : lr;\n      return isStart ? lr : rl;\n    case 'left':\n    case 'right':\n      return isStart ? tb : bt;\n    default:\n      return [];\n  }\n}\nfunction getOppositeAxisPlacements(placement, flipAlignment, direction, rtl) {\n  const alignment = getAlignment(placement);\n  let list = getSideList(getSide(placement), direction === 'start', rtl);\n  if (alignment) {\n    list = list.map(side => side + \"-\" + alignment);\n    if (flipAlignment) {\n      list = list.concat(list.map(getOppositeAlignmentPlacement));\n    }\n  }\n  return list;\n}\nfunction getOppositePlacement(placement) {\n  return placement.replace(/left|right|bottom|top/g, side => oppositeSideMap[side]);\n}\nfunction expandPaddingObject(padding) {\n  return {\n    top: 0,\n    right: 0,\n    bottom: 0,\n    left: 0,\n    ...padding\n  };\n}\nfunction getPaddingObject(padding) {\n  return typeof padding !== 'number' ? expandPaddingObject(padding) : {\n    top: padding,\n    right: padding,\n    bottom: padding,\n    left: padding\n  };\n}\nfunction rectToClientRect(rect) {\n  return {\n    ...rect,\n    top: rect.y,\n    left: rect.x,\n    right: rect.x + rect.width,\n    bottom: rect.y + rect.height\n  };\n}\n\nexport { alignments, clamp, createCoords, evaluate, expandPaddingObject, floor, getAlignment, getAlignmentAxis, getAlignmentSides, getAxisLength, getExpandedPlacements, getOppositeAlignmentPlacement, getOppositeAxis, getOppositeAxisPlacements, getOppositePlacement, getPaddingObject, getSide, getSideAxis, max, min, placements, rectToClientRect, round, sides };\n", "import { getSideAxis, getAlignmentAxis, getAxisLength, getSide, getAlignment, evaluate, getPaddingObject, rectToClientRect, min, clamp, placements, getAlignmentSides, getOppositeAlignmentPlacement, getOppositePlacement, getExpandedPlacements, getOppositeAxisPlacements, sides, max, getOppositeAxis } from '@floating-ui/utils';\nexport { rectToClientRect } from '@floating-ui/utils';\n\nfunction computeCoordsFromPlacement(_ref, placement, rtl) {\n  let {\n    reference,\n    floating\n  } = _ref;\n  const sideAxis = getSideAxis(placement);\n  const alignmentAxis = getAlignmentAxis(placement);\n  const alignLength = getAxisLength(alignmentAxis);\n  const side = getSide(placement);\n  const isVertical = sideAxis === 'y';\n  const commonX = reference.x + reference.width / 2 - floating.width / 2;\n  const commonY = reference.y + reference.height / 2 - floating.height / 2;\n  const commonAlign = reference[alignLength] / 2 - floating[alignLength] / 2;\n  let coords;\n  switch (side) {\n    case 'top':\n      coords = {\n        x: commonX,\n        y: reference.y - floating.height\n      };\n      break;\n    case 'bottom':\n      coords = {\n        x: commonX,\n        y: reference.y + reference.height\n      };\n      break;\n    case 'right':\n      coords = {\n        x: reference.x + reference.width,\n        y: commonY\n      };\n      break;\n    case 'left':\n      coords = {\n        x: reference.x - floating.width,\n        y: commonY\n      };\n      break;\n    default:\n      coords = {\n        x: reference.x,\n        y: reference.y\n      };\n  }\n  switch (getAlignment(placement)) {\n    case 'start':\n      coords[alignmentAxis] -= commonAlign * (rtl && isVertical ? -1 : 1);\n      break;\n    case 'end':\n      coords[alignmentAxis] += commonAlign * (rtl && isVertical ? -1 : 1);\n      break;\n  }\n  return coords;\n}\n\n/**\n * Computes the `x` and `y` coordinates that will place the floating element\n * next to a given reference element.\n *\n * This export does not have any `platform` interface logic. You will need to\n * write one for the platform you are using Floating UI with.\n */\nconst computePosition = async (reference, floating, config) => {\n  const {\n    placement = 'bottom',\n    strategy = 'absolute',\n    middleware = [],\n    platform\n  } = config;\n  const validMiddleware = middleware.filter(Boolean);\n  const rtl = await (platform.isRTL == null ? void 0 : platform.isRTL(floating));\n  let rects = await platform.getElementRects({\n    reference,\n    floating,\n    strategy\n  });\n  let {\n    x,\n    y\n  } = computeCoordsFromPlacement(rects, placement, rtl);\n  let statefulPlacement = placement;\n  let middlewareData = {};\n  let resetCount = 0;\n  for (let i = 0; i < validMiddleware.length; i++) {\n    const {\n      name,\n      fn\n    } = validMiddleware[i];\n    const {\n      x: nextX,\n      y: nextY,\n      data,\n      reset\n    } = await fn({\n      x,\n      y,\n      initialPlacement: placement,\n      placement: statefulPlacement,\n      strategy,\n      middlewareData,\n      rects,\n      platform,\n      elements: {\n        reference,\n        floating\n      }\n    });\n    x = nextX != null ? nextX : x;\n    y = nextY != null ? nextY : y;\n    middlewareData = {\n      ...middlewareData,\n      [name]: {\n        ...middlewareData[name],\n        ...data\n      }\n    };\n    if (reset && resetCount <= 50) {\n      resetCount++;\n      if (typeof reset === 'object') {\n        if (reset.placement) {\n          statefulPlacement = reset.placement;\n        }\n        if (reset.rects) {\n          rects = reset.rects === true ? await platform.getElementRects({\n            reference,\n            floating,\n            strategy\n          }) : reset.rects;\n        }\n        ({\n          x,\n          y\n        } = computeCoordsFromPlacement(rects, statefulPlacement, rtl));\n      }\n      i = -1;\n      continue;\n    }\n  }\n  return {\n    x,\n    y,\n    placement: statefulPlacement,\n    strategy,\n    middlewareData\n  };\n};\n\n/**\n * Resolves with an object of overflow side offsets that determine how much the\n * element is overflowing a given clipping boundary on each side.\n * - positive = overflowing the boundary by that number of pixels\n * - negative = how many pixels left before it will overflow\n * - 0 = lies flush with the boundary\n * @see https://floating-ui.com/docs/detectOverflow\n */\nasync function detectOverflow(state, options) {\n  var _await$platform$isEle;\n  if (options === void 0) {\n    options = {};\n  }\n  const {\n    x,\n    y,\n    platform,\n    rects,\n    elements,\n    strategy\n  } = state;\n  const {\n    boundary = 'clippingAncestors',\n    rootBoundary = 'viewport',\n    elementContext = 'floating',\n    altBoundary = false,\n    padding = 0\n  } = evaluate(options, state);\n  const paddingObject = getPaddingObject(padding);\n  const altContext = elementContext === 'floating' ? 'reference' : 'floating';\n  const element = elements[altBoundary ? altContext : elementContext];\n  const clippingClientRect = rectToClientRect(await platform.getClippingRect({\n    element: ((_await$platform$isEle = await (platform.isElement == null ? void 0 : platform.isElement(element))) != null ? _await$platform$isEle : true) ? element : element.contextElement || (await (platform.getDocumentElement == null ? void 0 : platform.getDocumentElement(elements.floating))),\n    boundary,\n    rootBoundary,\n    strategy\n  }));\n  const rect = elementContext === 'floating' ? {\n    ...rects.floating,\n    x,\n    y\n  } : rects.reference;\n  const offsetParent = await (platform.getOffsetParent == null ? void 0 : platform.getOffsetParent(elements.floating));\n  const offsetScale = (await (platform.isElement == null ? void 0 : platform.isElement(offsetParent))) ? (await (platform.getScale == null ? void 0 : platform.getScale(offsetParent))) || {\n    x: 1,\n    y: 1\n  } : {\n    x: 1,\n    y: 1\n  };\n  const elementClientRect = rectToClientRect(platform.convertOffsetParentRelativeRectToViewportRelativeRect ? await platform.convertOffsetParentRelativeRectToViewportRelativeRect({\n    rect,\n    offsetParent,\n    strategy\n  }) : rect);\n  return {\n    top: (clippingClientRect.top - elementClientRect.top + paddingObject.top) / offsetScale.y,\n    bottom: (elementClientRect.bottom - clippingClientRect.bottom + paddingObject.bottom) / offsetScale.y,\n    left: (clippingClientRect.left - elementClientRect.left + paddingObject.left) / offsetScale.x,\n    right: (elementClientRect.right - clippingClientRect.right + paddingObject.right) / offsetScale.x\n  };\n}\n\n/**\n * Provides data to position an inner element of the floating element so that it\n * appears centered to the reference element.\n * @see https://floating-ui.com/docs/arrow\n */\nconst arrow = options => ({\n  name: 'arrow',\n  options,\n  async fn(state) {\n    const {\n      x,\n      y,\n      placement,\n      rects,\n      platform,\n      elements,\n      middlewareData\n    } = state;\n    // Since `element` is required, we don't Partial<> the type.\n    const {\n      element,\n      padding = 0\n    } = evaluate(options, state) || {};\n    if (element == null) {\n      return {};\n    }\n    const paddingObject = getPaddingObject(padding);\n    const coords = {\n      x,\n      y\n    };\n    const axis = getAlignmentAxis(placement);\n    const length = getAxisLength(axis);\n    const arrowDimensions = await platform.getDimensions(element);\n    const isYAxis = axis === 'y';\n    const minProp = isYAxis ? 'top' : 'left';\n    const maxProp = isYAxis ? 'bottom' : 'right';\n    const clientProp = isYAxis ? 'clientHeight' : 'clientWidth';\n    const endDiff = rects.reference[length] + rects.reference[axis] - coords[axis] - rects.floating[length];\n    const startDiff = coords[axis] - rects.reference[axis];\n    const arrowOffsetParent = await (platform.getOffsetParent == null ? void 0 : platform.getOffsetParent(element));\n    let clientSize = arrowOffsetParent ? arrowOffsetParent[clientProp] : 0;\n\n    // DOM platform can return `window` as the `offsetParent`.\n    if (!clientSize || !(await (platform.isElement == null ? void 0 : platform.isElement(arrowOffsetParent)))) {\n      clientSize = elements.floating[clientProp] || rects.floating[length];\n    }\n    const centerToReference = endDiff / 2 - startDiff / 2;\n\n    // If the padding is large enough that it causes the arrow to no longer be\n    // centered, modify the padding so that it is centered.\n    const largestPossiblePadding = clientSize / 2 - arrowDimensions[length] / 2 - 1;\n    const minPadding = min(paddingObject[minProp], largestPossiblePadding);\n    const maxPadding = min(paddingObject[maxProp], largestPossiblePadding);\n\n    // Make sure the arrow doesn't overflow the floating element if the center\n    // point is outside the floating element's bounds.\n    const min$1 = minPadding;\n    const max = clientSize - arrowDimensions[length] - maxPadding;\n    const center = clientSize / 2 - arrowDimensions[length] / 2 + centerToReference;\n    const offset = clamp(min$1, center, max);\n\n    // If the reference is small enough that the arrow's padding causes it to\n    // to point to nothing for an aligned placement, adjust the offset of the\n    // floating element itself. To ensure `shift()` continues to take action,\n    // a single reset is performed when this is true.\n    const shouldAddOffset = !middlewareData.arrow && getAlignment(placement) != null && center != offset && rects.reference[length] / 2 - (center < min$1 ? minPadding : maxPadding) - arrowDimensions[length] / 2 < 0;\n    const alignmentOffset = shouldAddOffset ? center < min$1 ? center - min$1 : center - max : 0;\n    return {\n      [axis]: coords[axis] + alignmentOffset,\n      data: {\n        [axis]: offset,\n        centerOffset: center - offset - alignmentOffset,\n        ...(shouldAddOffset && {\n          alignmentOffset\n        })\n      },\n      reset: shouldAddOffset\n    };\n  }\n});\n\nfunction getPlacementList(alignment, autoAlignment, allowedPlacements) {\n  const allowedPlacementsSortedByAlignment = alignment ? [...allowedPlacements.filter(placement => getAlignment(placement) === alignment), ...allowedPlacements.filter(placement => getAlignment(placement) !== alignment)] : allowedPlacements.filter(placement => getSide(placement) === placement);\n  return allowedPlacementsSortedByAlignment.filter(placement => {\n    if (alignment) {\n      return getAlignment(placement) === alignment || (autoAlignment ? getOppositeAlignmentPlacement(placement) !== placement : false);\n    }\n    return true;\n  });\n}\n/**\n * Optimizes the visibility of the floating element by choosing the placement\n * that has the most space available automatically, without needing to specify a\n * preferred placement. Alternative to `flip`.\n * @see https://floating-ui.com/docs/autoPlacement\n */\nconst autoPlacement = function (options) {\n  if (options === void 0) {\n    options = {};\n  }\n  return {\n    name: 'autoPlacement',\n    options,\n    async fn(state) {\n      var _middlewareData$autoP, _middlewareData$autoP2, _placementsThatFitOnE;\n      const {\n        rects,\n        middlewareData,\n        placement,\n        platform,\n        elements\n      } = state;\n      const {\n        crossAxis = false,\n        alignment,\n        allowedPlacements = placements,\n        autoAlignment = true,\n        ...detectOverflowOptions\n      } = evaluate(options, state);\n      const placements$1 = alignment !== undefined || allowedPlacements === placements ? getPlacementList(alignment || null, autoAlignment, allowedPlacements) : allowedPlacements;\n      const overflow = await detectOverflow(state, detectOverflowOptions);\n      const currentIndex = ((_middlewareData$autoP = middlewareData.autoPlacement) == null ? void 0 : _middlewareData$autoP.index) || 0;\n      const currentPlacement = placements$1[currentIndex];\n      if (currentPlacement == null) {\n        return {};\n      }\n      const alignmentSides = getAlignmentSides(currentPlacement, rects, await (platform.isRTL == null ? void 0 : platform.isRTL(elements.floating)));\n\n      // Make `computeCoords` start from the right place.\n      if (placement !== currentPlacement) {\n        return {\n          reset: {\n            placement: placements$1[0]\n          }\n        };\n      }\n      const currentOverflows = [overflow[getSide(currentPlacement)], overflow[alignmentSides[0]], overflow[alignmentSides[1]]];\n      const allOverflows = [...(((_middlewareData$autoP2 = middlewareData.autoPlacement) == null ? void 0 : _middlewareData$autoP2.overflows) || []), {\n        placement: currentPlacement,\n        overflows: currentOverflows\n      }];\n      const nextPlacement = placements$1[currentIndex + 1];\n\n      // There are more placements to check.\n      if (nextPlacement) {\n        return {\n          data: {\n            index: currentIndex + 1,\n            overflows: allOverflows\n          },\n          reset: {\n            placement: nextPlacement\n          }\n        };\n      }\n      const placementsSortedByMostSpace = allOverflows.map(d => {\n        const alignment = getAlignment(d.placement);\n        return [d.placement, alignment && crossAxis ?\n        // Check along the mainAxis and main crossAxis side.\n        d.overflows.slice(0, 2).reduce((acc, v) => acc + v, 0) :\n        // Check only the mainAxis.\n        d.overflows[0], d.overflows];\n      }).sort((a, b) => a[1] - b[1]);\n      const placementsThatFitOnEachSide = placementsSortedByMostSpace.filter(d => d[2].slice(0,\n      // Aligned placements should not check their opposite crossAxis\n      // side.\n      getAlignment(d[0]) ? 2 : 3).every(v => v <= 0));\n      const resetPlacement = ((_placementsThatFitOnE = placementsThatFitOnEachSide[0]) == null ? void 0 : _placementsThatFitOnE[0]) || placementsSortedByMostSpace[0][0];\n      if (resetPlacement !== placement) {\n        return {\n          data: {\n            index: currentIndex + 1,\n            overflows: allOverflows\n          },\n          reset: {\n            placement: resetPlacement\n          }\n        };\n      }\n      return {};\n    }\n  };\n};\n\n/**\n * Optimizes the visibility of the floating element by flipping the `placement`\n * in order to keep it in view when the preferred placement(s) will overflow the\n * clipping boundary. Alternative to `autoPlacement`.\n * @see https://floating-ui.com/docs/flip\n */\nconst flip = function (options) {\n  if (options === void 0) {\n    options = {};\n  }\n  return {\n    name: 'flip',\n    options,\n    async fn(state) {\n      var _middlewareData$arrow, _middlewareData$flip;\n      const {\n        placement,\n        middlewareData,\n        rects,\n        initialPlacement,\n        platform,\n        elements\n      } = state;\n      const {\n        mainAxis: checkMainAxis = true,\n        crossAxis: checkCrossAxis = true,\n        fallbackPlacements: specifiedFallbackPlacements,\n        fallbackStrategy = 'bestFit',\n        fallbackAxisSideDirection = 'none',\n        flipAlignment = true,\n        ...detectOverflowOptions\n      } = evaluate(options, state);\n\n      // If a reset by the arrow was caused due to an alignment offset being\n      // added, we should skip any logic now since `flip()` has already done its\n      // work.\n      // https://github.com/floating-ui/floating-ui/issues/2549#issuecomment-1719601643\n      if ((_middlewareData$arrow = middlewareData.arrow) != null && _middlewareData$arrow.alignmentOffset) {\n        return {};\n      }\n      const side = getSide(placement);\n      const isBasePlacement = getSide(initialPlacement) === initialPlacement;\n      const rtl = await (platform.isRTL == null ? void 0 : platform.isRTL(elements.floating));\n      const fallbackPlacements = specifiedFallbackPlacements || (isBasePlacement || !flipAlignment ? [getOppositePlacement(initialPlacement)] : getExpandedPlacements(initialPlacement));\n      if (!specifiedFallbackPlacements && fallbackAxisSideDirection !== 'none') {\n        fallbackPlacements.push(...getOppositeAxisPlacements(initialPlacement, flipAlignment, fallbackAxisSideDirection, rtl));\n      }\n      const placements = [initialPlacement, ...fallbackPlacements];\n      const overflow = await detectOverflow(state, detectOverflowOptions);\n      const overflows = [];\n      let overflowsData = ((_middlewareData$flip = middlewareData.flip) == null ? void 0 : _middlewareData$flip.overflows) || [];\n      if (checkMainAxis) {\n        overflows.push(overflow[side]);\n      }\n      if (checkCrossAxis) {\n        const sides = getAlignmentSides(placement, rects, rtl);\n        overflows.push(overflow[sides[0]], overflow[sides[1]]);\n      }\n      overflowsData = [...overflowsData, {\n        placement,\n        overflows\n      }];\n\n      // One or more sides is overflowing.\n      if (!overflows.every(side => side <= 0)) {\n        var _middlewareData$flip2, _overflowsData$filter;\n        const nextIndex = (((_middlewareData$flip2 = middlewareData.flip) == null ? void 0 : _middlewareData$flip2.index) || 0) + 1;\n        const nextPlacement = placements[nextIndex];\n        if (nextPlacement) {\n          // Try next placement and re-run the lifecycle.\n          return {\n            data: {\n              index: nextIndex,\n              overflows: overflowsData\n            },\n            reset: {\n              placement: nextPlacement\n            }\n          };\n        }\n\n        // First, find the candidates that fit on the mainAxis side of overflow,\n        // then find the placement that fits the best on the main crossAxis side.\n        let resetPlacement = (_overflowsData$filter = overflowsData.filter(d => d.overflows[0] <= 0).sort((a, b) => a.overflows[1] - b.overflows[1])[0]) == null ? void 0 : _overflowsData$filter.placement;\n\n        // Otherwise fallback.\n        if (!resetPlacement) {\n          switch (fallbackStrategy) {\n            case 'bestFit':\n              {\n                var _overflowsData$map$so;\n                const placement = (_overflowsData$map$so = overflowsData.map(d => [d.placement, d.overflows.filter(overflow => overflow > 0).reduce((acc, overflow) => acc + overflow, 0)]).sort((a, b) => a[1] - b[1])[0]) == null ? void 0 : _overflowsData$map$so[0];\n                if (placement) {\n                  resetPlacement = placement;\n                }\n                break;\n              }\n            case 'initialPlacement':\n              resetPlacement = initialPlacement;\n              break;\n          }\n        }\n        if (placement !== resetPlacement) {\n          return {\n            reset: {\n              placement: resetPlacement\n            }\n          };\n        }\n      }\n      return {};\n    }\n  };\n};\n\nfunction getSideOffsets(overflow, rect) {\n  return {\n    top: overflow.top - rect.height,\n    right: overflow.right - rect.width,\n    bottom: overflow.bottom - rect.height,\n    left: overflow.left - rect.width\n  };\n}\nfunction isAnySideFullyClipped(overflow) {\n  return sides.some(side => overflow[side] >= 0);\n}\n/**\n * Provides data to hide the floating element in applicable situations, such as\n * when it is not in the same clipping context as the reference element.\n * @see https://floating-ui.com/docs/hide\n */\nconst hide = function (options) {\n  if (options === void 0) {\n    options = {};\n  }\n  return {\n    name: 'hide',\n    options,\n    async fn(state) {\n      const {\n        rects\n      } = state;\n      const {\n        strategy = 'referenceHidden',\n        ...detectOverflowOptions\n      } = evaluate(options, state);\n      switch (strategy) {\n        case 'referenceHidden':\n          {\n            const overflow = await detectOverflow(state, {\n              ...detectOverflowOptions,\n              elementContext: 'reference'\n            });\n            const offsets = getSideOffsets(overflow, rects.reference);\n            return {\n              data: {\n                referenceHiddenOffsets: offsets,\n                referenceHidden: isAnySideFullyClipped(offsets)\n              }\n            };\n          }\n        case 'escaped':\n          {\n            const overflow = await detectOverflow(state, {\n              ...detectOverflowOptions,\n              altBoundary: true\n            });\n            const offsets = getSideOffsets(overflow, rects.floating);\n            return {\n              data: {\n                escapedOffsets: offsets,\n                escaped: isAnySideFullyClipped(offsets)\n              }\n            };\n          }\n        default:\n          {\n            return {};\n          }\n      }\n    }\n  };\n};\n\nfunction getBoundingRect(rects) {\n  const minX = min(...rects.map(rect => rect.left));\n  const minY = min(...rects.map(rect => rect.top));\n  const maxX = max(...rects.map(rect => rect.right));\n  const maxY = max(...rects.map(rect => rect.bottom));\n  return {\n    x: minX,\n    y: minY,\n    width: maxX - minX,\n    height: maxY - minY\n  };\n}\nfunction getRectsByLine(rects) {\n  const sortedRects = rects.slice().sort((a, b) => a.y - b.y);\n  const groups = [];\n  let prevRect = null;\n  for (let i = 0; i < sortedRects.length; i++) {\n    const rect = sortedRects[i];\n    if (!prevRect || rect.y - prevRect.y > prevRect.height / 2) {\n      groups.push([rect]);\n    } else {\n      groups[groups.length - 1].push(rect);\n    }\n    prevRect = rect;\n  }\n  return groups.map(rect => rectToClientRect(getBoundingRect(rect)));\n}\n/**\n * Provides improved positioning for inline reference elements that can span\n * over multiple lines, such as hyperlinks or range selections.\n * @see https://floating-ui.com/docs/inline\n */\nconst inline = function (options) {\n  if (options === void 0) {\n    options = {};\n  }\n  return {\n    name: 'inline',\n    options,\n    async fn(state) {\n      const {\n        placement,\n        elements,\n        rects,\n        platform,\n        strategy\n      } = state;\n      // A MouseEvent's client{X,Y} coords can be up to 2 pixels off a\n      // ClientRect's bounds, despite the event listener being triggered. A\n      // padding of 2 seems to handle this issue.\n      const {\n        padding = 2,\n        x,\n        y\n      } = evaluate(options, state);\n      const nativeClientRects = Array.from((await (platform.getClientRects == null ? void 0 : platform.getClientRects(elements.reference))) || []);\n      const clientRects = getRectsByLine(nativeClientRects);\n      const fallback = rectToClientRect(getBoundingRect(nativeClientRects));\n      const paddingObject = getPaddingObject(padding);\n      function getBoundingClientRect() {\n        // There are two rects and they are disjoined.\n        if (clientRects.length === 2 && clientRects[0].left > clientRects[1].right && x != null && y != null) {\n          // Find the first rect in which the point is fully inside.\n          return clientRects.find(rect => x > rect.left - paddingObject.left && x < rect.right + paddingObject.right && y > rect.top - paddingObject.top && y < rect.bottom + paddingObject.bottom) || fallback;\n        }\n\n        // There are 2 or more connected rects.\n        if (clientRects.length >= 2) {\n          if (getSideAxis(placement) === 'y') {\n            const firstRect = clientRects[0];\n            const lastRect = clientRects[clientRects.length - 1];\n            const isTop = getSide(placement) === 'top';\n            const top = firstRect.top;\n            const bottom = lastRect.bottom;\n            const left = isTop ? firstRect.left : lastRect.left;\n            const right = isTop ? firstRect.right : lastRect.right;\n            const width = right - left;\n            const height = bottom - top;\n            return {\n              top,\n              bottom,\n              left,\n              right,\n              width,\n              height,\n              x: left,\n              y: top\n            };\n          }\n          const isLeftSide = getSide(placement) === 'left';\n          const maxRight = max(...clientRects.map(rect => rect.right));\n          const minLeft = min(...clientRects.map(rect => rect.left));\n          const measureRects = clientRects.filter(rect => isLeftSide ? rect.left === minLeft : rect.right === maxRight);\n          const top = measureRects[0].top;\n          const bottom = measureRects[measureRects.length - 1].bottom;\n          const left = minLeft;\n          const right = maxRight;\n          const width = right - left;\n          const height = bottom - top;\n          return {\n            top,\n            bottom,\n            left,\n            right,\n            width,\n            height,\n            x: left,\n            y: top\n          };\n        }\n        return fallback;\n      }\n      const resetRects = await platform.getElementRects({\n        reference: {\n          getBoundingClientRect\n        },\n        floating: elements.floating,\n        strategy\n      });\n      if (rects.reference.x !== resetRects.reference.x || rects.reference.y !== resetRects.reference.y || rects.reference.width !== resetRects.reference.width || rects.reference.height !== resetRects.reference.height) {\n        return {\n          reset: {\n            rects: resetRects\n          }\n        };\n      }\n      return {};\n    }\n  };\n};\n\n// For type backwards-compatibility, the `OffsetOptions` type was also\n// Derivable.\n\nasync function convertValueToCoords(state, options) {\n  const {\n    placement,\n    platform,\n    elements\n  } = state;\n  const rtl = await (platform.isRTL == null ? void 0 : platform.isRTL(elements.floating));\n  const side = getSide(placement);\n  const alignment = getAlignment(placement);\n  const isVertical = getSideAxis(placement) === 'y';\n  const mainAxisMulti = ['left', 'top'].includes(side) ? -1 : 1;\n  const crossAxisMulti = rtl && isVertical ? -1 : 1;\n  const rawValue = evaluate(options, state);\n\n  // eslint-disable-next-line prefer-const\n  let {\n    mainAxis,\n    crossAxis,\n    alignmentAxis\n  } = typeof rawValue === 'number' ? {\n    mainAxis: rawValue,\n    crossAxis: 0,\n    alignmentAxis: null\n  } : {\n    mainAxis: 0,\n    crossAxis: 0,\n    alignmentAxis: null,\n    ...rawValue\n  };\n  if (alignment && typeof alignmentAxis === 'number') {\n    crossAxis = alignment === 'end' ? alignmentAxis * -1 : alignmentAxis;\n  }\n  return isVertical ? {\n    x: crossAxis * crossAxisMulti,\n    y: mainAxis * mainAxisMulti\n  } : {\n    x: mainAxis * mainAxisMulti,\n    y: crossAxis * crossAxisMulti\n  };\n}\n\n/**\n * Modifies the placement by translating the floating element along the\n * specified axes.\n * A number (shorthand for `mainAxis` or distance), or an axes configuration\n * object may be passed.\n * @see https://floating-ui.com/docs/offset\n */\nconst offset = function (options) {\n  if (options === void 0) {\n    options = 0;\n  }\n  return {\n    name: 'offset',\n    options,\n    async fn(state) {\n      var _middlewareData$offse, _middlewareData$arrow;\n      const {\n        x,\n        y,\n        placement,\n        middlewareData\n      } = state;\n      const diffCoords = await convertValueToCoords(state, options);\n\n      // If the placement is the same and the arrow caused an alignment offset\n      // then we don't need to change the positioning coordinates.\n      if (placement === ((_middlewareData$offse = middlewareData.offset) == null ? void 0 : _middlewareData$offse.placement) && (_middlewareData$arrow = middlewareData.arrow) != null && _middlewareData$arrow.alignmentOffset) {\n        return {};\n      }\n      return {\n        x: x + diffCoords.x,\n        y: y + diffCoords.y,\n        data: {\n          ...diffCoords,\n          placement\n        }\n      };\n    }\n  };\n};\n\n/**\n * Optimizes the visibility of the floating element by shifting it in order to\n * keep it in view when it will overflow the clipping boundary.\n * @see https://floating-ui.com/docs/shift\n */\nconst shift = function (options) {\n  if (options === void 0) {\n    options = {};\n  }\n  return {\n    name: 'shift',\n    options,\n    async fn(state) {\n      const {\n        x,\n        y,\n        placement\n      } = state;\n      const {\n        mainAxis: checkMainAxis = true,\n        crossAxis: checkCrossAxis = false,\n        limiter = {\n          fn: _ref => {\n            let {\n              x,\n              y\n            } = _ref;\n            return {\n              x,\n              y\n            };\n          }\n        },\n        ...detectOverflowOptions\n      } = evaluate(options, state);\n      const coords = {\n        x,\n        y\n      };\n      const overflow = await detectOverflow(state, detectOverflowOptions);\n      const crossAxis = getSideAxis(getSide(placement));\n      const mainAxis = getOppositeAxis(crossAxis);\n      let mainAxisCoord = coords[mainAxis];\n      let crossAxisCoord = coords[crossAxis];\n      if (checkMainAxis) {\n        const minSide = mainAxis === 'y' ? 'top' : 'left';\n        const maxSide = mainAxis === 'y' ? 'bottom' : 'right';\n        const min = mainAxisCoord + overflow[minSide];\n        const max = mainAxisCoord - overflow[maxSide];\n        mainAxisCoord = clamp(min, mainAxisCoord, max);\n      }\n      if (checkCrossAxis) {\n        const minSide = crossAxis === 'y' ? 'top' : 'left';\n        const maxSide = crossAxis === 'y' ? 'bottom' : 'right';\n        const min = crossAxisCoord + overflow[minSide];\n        const max = crossAxisCoord - overflow[maxSide];\n        crossAxisCoord = clamp(min, crossAxisCoord, max);\n      }\n      const limitedCoords = limiter.fn({\n        ...state,\n        [mainAxis]: mainAxisCoord,\n        [crossAxis]: crossAxisCoord\n      });\n      return {\n        ...limitedCoords,\n        data: {\n          x: limitedCoords.x - x,\n          y: limitedCoords.y - y\n        }\n      };\n    }\n  };\n};\n/**\n * Built-in `limiter` that will stop `shift()` at a certain point.\n */\nconst limitShift = function (options) {\n  if (options === void 0) {\n    options = {};\n  }\n  return {\n    options,\n    fn(state) {\n      const {\n        x,\n        y,\n        placement,\n        rects,\n        middlewareData\n      } = state;\n      const {\n        offset = 0,\n        mainAxis: checkMainAxis = true,\n        crossAxis: checkCrossAxis = true\n      } = evaluate(options, state);\n      const coords = {\n        x,\n        y\n      };\n      const crossAxis = getSideAxis(placement);\n      const mainAxis = getOppositeAxis(crossAxis);\n      let mainAxisCoord = coords[mainAxis];\n      let crossAxisCoord = coords[crossAxis];\n      const rawOffset = evaluate(offset, state);\n      const computedOffset = typeof rawOffset === 'number' ? {\n        mainAxis: rawOffset,\n        crossAxis: 0\n      } : {\n        mainAxis: 0,\n        crossAxis: 0,\n        ...rawOffset\n      };\n      if (checkMainAxis) {\n        const len = mainAxis === 'y' ? 'height' : 'width';\n        const limitMin = rects.reference[mainAxis] - rects.floating[len] + computedOffset.mainAxis;\n        const limitMax = rects.reference[mainAxis] + rects.reference[len] - computedOffset.mainAxis;\n        if (mainAxisCoord < limitMin) {\n          mainAxisCoord = limitMin;\n        } else if (mainAxisCoord > limitMax) {\n          mainAxisCoord = limitMax;\n        }\n      }\n      if (checkCrossAxis) {\n        var _middlewareData$offse, _middlewareData$offse2;\n        const len = mainAxis === 'y' ? 'width' : 'height';\n        const isOriginSide = ['top', 'left'].includes(getSide(placement));\n        const limitMin = rects.reference[crossAxis] - rects.floating[len] + (isOriginSide ? ((_middlewareData$offse = middlewareData.offset) == null ? void 0 : _middlewareData$offse[crossAxis]) || 0 : 0) + (isOriginSide ? 0 : computedOffset.crossAxis);\n        const limitMax = rects.reference[crossAxis] + rects.reference[len] + (isOriginSide ? 0 : ((_middlewareData$offse2 = middlewareData.offset) == null ? void 0 : _middlewareData$offse2[crossAxis]) || 0) - (isOriginSide ? computedOffset.crossAxis : 0);\n        if (crossAxisCoord < limitMin) {\n          crossAxisCoord = limitMin;\n        } else if (crossAxisCoord > limitMax) {\n          crossAxisCoord = limitMax;\n        }\n      }\n      return {\n        [mainAxis]: mainAxisCoord,\n        [crossAxis]: crossAxisCoord\n      };\n    }\n  };\n};\n\n/**\n * Provides data that allows you to change the size of the floating element —\n * for instance, prevent it from overflowing the clipping boundary or match the\n * width of the reference element.\n * @see https://floating-ui.com/docs/size\n */\nconst size = function (options) {\n  if (options === void 0) {\n    options = {};\n  }\n  return {\n    name: 'size',\n    options,\n    async fn(state) {\n      const {\n        placement,\n        rects,\n        platform,\n        elements\n      } = state;\n      const {\n        apply = () => {},\n        ...detectOverflowOptions\n      } = evaluate(options, state);\n      const overflow = await detectOverflow(state, detectOverflowOptions);\n      const side = getSide(placement);\n      const alignment = getAlignment(placement);\n      const isYAxis = getSideAxis(placement) === 'y';\n      const {\n        width,\n        height\n      } = rects.floating;\n      let heightSide;\n      let widthSide;\n      if (side === 'top' || side === 'bottom') {\n        heightSide = side;\n        widthSide = alignment === ((await (platform.isRTL == null ? void 0 : platform.isRTL(elements.floating))) ? 'start' : 'end') ? 'left' : 'right';\n      } else {\n        widthSide = side;\n        heightSide = alignment === 'end' ? 'top' : 'bottom';\n      }\n      const overflowAvailableHeight = height - overflow[heightSide];\n      const overflowAvailableWidth = width - overflow[widthSide];\n      const noShift = !state.middlewareData.shift;\n      let availableHeight = overflowAvailableHeight;\n      let availableWidth = overflowAvailableWidth;\n      if (isYAxis) {\n        const maximumClippingWidth = width - overflow.left - overflow.right;\n        availableWidth = alignment || noShift ? min(overflowAvailableWidth, maximumClippingWidth) : maximumClippingWidth;\n      } else {\n        const maximumClippingHeight = height - overflow.top - overflow.bottom;\n        availableHeight = alignment || noShift ? min(overflowAvailableHeight, maximumClippingHeight) : maximumClippingHeight;\n      }\n      if (noShift && !alignment) {\n        const xMin = max(overflow.left, 0);\n        const xMax = max(overflow.right, 0);\n        const yMin = max(overflow.top, 0);\n        const yMax = max(overflow.bottom, 0);\n        if (isYAxis) {\n          availableWidth = width - 2 * (xMin !== 0 || xMax !== 0 ? xMin + xMax : max(overflow.left, overflow.right));\n        } else {\n          availableHeight = height - 2 * (yMin !== 0 || yMax !== 0 ? yMin + yMax : max(overflow.top, overflow.bottom));\n        }\n      }\n      await apply({\n        ...state,\n        availableWidth,\n        availableHeight\n      });\n      const nextDimensions = await platform.getDimensions(elements.floating);\n      if (width !== nextDimensions.width || height !== nextDimensions.height) {\n        return {\n          reset: {\n            rects: true\n          }\n        };\n      }\n      return {};\n    }\n  };\n};\n\nexport { arrow, autoPlacement, computePosition, detectOverflow, flip, hide, inline, limitShift, offset, shift, size };\n", "/**\n * Custom positioning reference element.\n * @see https://floating-ui.com/docs/virtual-elements\n */\n\nconst sides = ['top', 'right', 'bottom', 'left'];\nconst alignments = ['start', 'end'];\nconst placements = /*#__PURE__*/sides.reduce((acc, side) => acc.concat(side, side + \"-\" + alignments[0], side + \"-\" + alignments[1]), []);\nconst min = Math.min;\nconst max = Math.max;\nconst round = Math.round;\nconst floor = Math.floor;\nconst createCoords = v => ({\n  x: v,\n  y: v\n});\nconst oppositeSideMap = {\n  left: 'right',\n  right: 'left',\n  bottom: 'top',\n  top: 'bottom'\n};\nconst oppositeAlignmentMap = {\n  start: 'end',\n  end: 'start'\n};\nfunction clamp(start, value, end) {\n  return max(start, min(value, end));\n}\nfunction evaluate(value, param) {\n  return typeof value === 'function' ? value(param) : value;\n}\nfunction getSide(placement) {\n  return placement.split('-')[0];\n}\nfunction getAlignment(placement) {\n  return placement.split('-')[1];\n}\nfunction getOppositeAxis(axis) {\n  return axis === 'x' ? 'y' : 'x';\n}\nfunction getAxisLength(axis) {\n  return axis === 'y' ? 'height' : 'width';\n}\nfunction getSideAxis(placement) {\n  return ['top', 'bottom'].includes(getSide(placement)) ? 'y' : 'x';\n}\nfunction getAlignmentAxis(placement) {\n  return getOppositeAxis(getSideAxis(placement));\n}\nfunction getAlignmentSides(placement, rects, rtl) {\n  if (rtl === void 0) {\n    rtl = false;\n  }\n  const alignment = getAlignment(placement);\n  const alignmentAxis = getAlignmentAxis(placement);\n  const length = getAxisLength(alignmentAxis);\n  let mainAlignmentSide = alignmentAxis === 'x' ? alignment === (rtl ? 'end' : 'start') ? 'right' : 'left' : alignment === 'start' ? 'bottom' : 'top';\n  if (rects.reference[length] > rects.floating[length]) {\n    mainAlignmentSide = getOppositePlacement(mainAlignmentSide);\n  }\n  return [mainAlignmentSide, getOppositePlacement(mainAlignmentSide)];\n}\nfunction getExpandedPlacements(placement) {\n  const oppositePlacement = getOppositePlacement(placement);\n  return [getOppositeAlignmentPlacement(placement), oppositePlacement, getOppositeAlignmentPlacement(oppositePlacement)];\n}\nfunction getOppositeAlignmentPlacement(placement) {\n  return placement.replace(/start|end/g, alignment => oppositeAlignmentMap[alignment]);\n}\nfunction getSideList(side, isStart, rtl) {\n  const lr = ['left', 'right'];\n  const rl = ['right', 'left'];\n  const tb = ['top', 'bottom'];\n  const bt = ['bottom', 'top'];\n  switch (side) {\n    case 'top':\n    case 'bottom':\n      if (rtl) return isStart ? rl : lr;\n      return isStart ? lr : rl;\n    case 'left':\n    case 'right':\n      return isStart ? tb : bt;\n    default:\n      return [];\n  }\n}\nfunction getOppositeAxisPlacements(placement, flipAlignment, direction, rtl) {\n  const alignment = getAlignment(placement);\n  let list = getSideList(getSide(placement), direction === 'start', rtl);\n  if (alignment) {\n    list = list.map(side => side + \"-\" + alignment);\n    if (flipAlignment) {\n      list = list.concat(list.map(getOppositeAlignmentPlacement));\n    }\n  }\n  return list;\n}\nfunction getOppositePlacement(placement) {\n  return placement.replace(/left|right|bottom|top/g, side => oppositeSideMap[side]);\n}\nfunction expandPaddingObject(padding) {\n  return {\n    top: 0,\n    right: 0,\n    bottom: 0,\n    left: 0,\n    ...padding\n  };\n}\nfunction getPaddingObject(padding) {\n  return typeof padding !== 'number' ? expandPaddingObject(padding) : {\n    top: padding,\n    right: padding,\n    bottom: padding,\n    left: padding\n  };\n}\nfunction rectToClientRect(rect) {\n  return {\n    ...rect,\n    top: rect.y,\n    left: rect.x,\n    right: rect.x + rect.width,\n    bottom: rect.y + rect.height\n  };\n}\n\nexport { alignments, clamp, createCoords, evaluate, expandPaddingObject, floor, getAlignment, getAlignmentAxis, getAlignmentSides, getAxisLength, getExpandedPlacements, getOppositeAlignmentPlacement, getOppositeAxis, getOppositeAxisPlacements, getOppositePlacement, getPaddingObject, getSide, getSideAxis, max, min, placements, rectToClientRect, round, sides };\n", "function getNodeName(node) {\n  if (isNode(node)) {\n    return (node.nodeName || '').toLowerCase();\n  }\n  // Mocked nodes in testing environments may not be instances of Node. By\n  // returning `#document` an infinite loop won't occur.\n  // https://github.com/floating-ui/floating-ui/issues/2317\n  return '#document';\n}\nfunction getWindow(node) {\n  var _node$ownerDocument;\n  return (node == null || (_node$ownerDocument = node.ownerDocument) == null ? void 0 : _node$ownerDocument.defaultView) || window;\n}\nfunction getDocumentElement(node) {\n  var _ref;\n  return (_ref = (isNode(node) ? node.ownerDocument : node.document) || window.document) == null ? void 0 : _ref.documentElement;\n}\nfunction isNode(value) {\n  return value instanceof Node || value instanceof getWindow(value).Node;\n}\nfunction isElement(value) {\n  return value instanceof Element || value instanceof getWindow(value).Element;\n}\nfunction isHTMLElement(value) {\n  return value instanceof HTMLElement || value instanceof getWindow(value).HTMLElement;\n}\nfunction isShadowRoot(value) {\n  // Browsers without `ShadowRoot` support.\n  if (typeof ShadowRoot === 'undefined') {\n    return false;\n  }\n  return value instanceof ShadowRoot || value instanceof getWindow(value).ShadowRoot;\n}\nfunction isOverflowElement(element) {\n  const {\n    overflow,\n    overflowX,\n    overflowY,\n    display\n  } = getComputedStyle(element);\n  return /auto|scroll|overlay|hidden|clip/.test(overflow + overflowY + overflowX) && !['inline', 'contents'].includes(display);\n}\nfunction isTableElement(element) {\n  return ['table', 'td', 'th'].includes(getNodeName(element));\n}\nfunction isContainingBlock(element) {\n  const webkit = isWebKit();\n  const css = getComputedStyle(element);\n\n  // https://developer.mozilla.org/en-US/docs/Web/CSS/Containing_block#identifying_the_containing_block\n  return css.transform !== 'none' || css.perspective !== 'none' || (css.containerType ? css.containerType !== 'normal' : false) || !webkit && (css.backdropFilter ? css.backdropFilter !== 'none' : false) || !webkit && (css.filter ? css.filter !== 'none' : false) || ['transform', 'perspective', 'filter'].some(value => (css.willChange || '').includes(value)) || ['paint', 'layout', 'strict', 'content'].some(value => (css.contain || '').includes(value));\n}\nfunction getContainingBlock(element) {\n  let currentNode = getParentNode(element);\n  while (isHTMLElement(currentNode) && !isLastTraversableNode(currentNode)) {\n    if (isContainingBlock(currentNode)) {\n      return currentNode;\n    } else {\n      currentNode = getParentNode(currentNode);\n    }\n  }\n  return null;\n}\nfunction isWebKit() {\n  if (typeof CSS === 'undefined' || !CSS.supports) return false;\n  return CSS.supports('-webkit-backdrop-filter', 'none');\n}\nfunction isLastTraversableNode(node) {\n  return ['html', 'body', '#document'].includes(getNodeName(node));\n}\nfunction getComputedStyle(element) {\n  return getWindow(element).getComputedStyle(element);\n}\nfunction getNodeScroll(element) {\n  if (isElement(element)) {\n    return {\n      scrollLeft: element.scrollLeft,\n      scrollTop: element.scrollTop\n    };\n  }\n  return {\n    scrollLeft: element.pageXOffset,\n    scrollTop: element.pageYOffset\n  };\n}\nfunction getParentNode(node) {\n  if (getNodeName(node) === 'html') {\n    return node;\n  }\n  const result =\n  // Step into the shadow DOM of the parent of a slotted node.\n  node.assignedSlot ||\n  // DOM Element detected.\n  node.parentNode ||\n  // ShadowRoot detected.\n  isShadowRoot(node) && node.host ||\n  // Fallback.\n  getDocumentElement(node);\n  return isShadowRoot(result) ? result.host : result;\n}\nfunction getNearestOverflowAncestor(node) {\n  const parentNode = getParentNode(node);\n  if (isLastTraversableNode(parentNode)) {\n    return node.ownerDocument ? node.ownerDocument.body : node.body;\n  }\n  if (isHTMLElement(parentNode) && isOverflowElement(parentNode)) {\n    return parentNode;\n  }\n  return getNearestOverflowAncestor(parentNode);\n}\nfunction getOverflowAncestors(node, list, traverseIframes) {\n  var _node$ownerDocument2;\n  if (list === void 0) {\n    list = [];\n  }\n  if (traverseIframes === void 0) {\n    traverseIframes = true;\n  }\n  const scrollableAncestor = getNearestOverflowAncestor(node);\n  const isBody = scrollableAncestor === ((_node$ownerDocument2 = node.ownerDocument) == null ? void 0 : _node$ownerDocument2.body);\n  const win = getWindow(scrollableAncestor);\n  if (isBody) {\n    return list.concat(win, win.visualViewport || [], isOverflowElement(scrollableAncestor) ? scrollableAncestor : [], win.frameElement && traverseIframes ? getOverflowAncestors(win.frameElement) : []);\n  }\n  return list.concat(scrollableAncestor, getOverflowAncestors(scrollableAncestor, [], traverseIframes));\n}\n\nexport { getComputedStyle, getContainingBlock, getDocumentElement, getNearestOverflowAncestor, getNodeName, getNodeScroll, getOverflowAncestors, getParentNode, getWindow, isContainingBlock, isElement, isHTMLElement, isLastTraversableNode, isNode, isOverflowElement, isShadowRoot, isTableElement, isWebKit };\n", "import { rectToClientRect, autoPlacement as autoPlacement$1, shift as shift$1, flip as flip$1, size as size$1, hide as hide$1, arrow as arrow$1, inline as inline$1, limitShift as limitShift$1, computePosition as computePosition$1 } from '@floating-ui/core';\nexport { detectOverflow, offset } from '@floating-ui/core';\nimport { round, createCoords, max, min, floor } from '@floating-ui/utils';\nimport { getComputedStyle, isHTMLElement, isElement, getWindow, isWebKit, getDocumentElement, getNodeName, isOverflowElement, getNodeScroll, getOverflowAncestors, getParentNode, isLastTraversableNode, isContainingBlock, isTableElement, getContainingBlock } from '@floating-ui/utils/dom';\nexport { getOverflowAncestors } from '@floating-ui/utils/dom';\n\nfunction getCssDimensions(element) {\n  const css = getComputedStyle(element);\n  // In testing environments, the `width` and `height` properties are empty\n  // strings for SVG elements, returning NaN. Fallback to `0` in this case.\n  let width = parseFloat(css.width) || 0;\n  let height = parseFloat(css.height) || 0;\n  const hasOffset = isHTMLElement(element);\n  const offsetWidth = hasOffset ? element.offsetWidth : width;\n  const offsetHeight = hasOffset ? element.offsetHeight : height;\n  const shouldFallback = round(width) !== offsetWidth || round(height) !== offsetHeight;\n  if (shouldFallback) {\n    width = offsetWidth;\n    height = offsetHeight;\n  }\n  return {\n    width,\n    height,\n    $: shouldFallback\n  };\n}\n\nfunction unwrapElement(element) {\n  return !isElement(element) ? element.contextElement : element;\n}\n\nfunction getScale(element) {\n  const domElement = unwrapElement(element);\n  if (!isHTMLElement(domElement)) {\n    return createCoords(1);\n  }\n  const rect = domElement.getBoundingClientRect();\n  const {\n    width,\n    height,\n    $\n  } = getCssDimensions(domElement);\n  let x = ($ ? round(rect.width) : rect.width) / width;\n  let y = ($ ? round(rect.height) : rect.height) / height;\n\n  // 0, NaN, or Infinity should always fallback to 1.\n\n  if (!x || !Number.isFinite(x)) {\n    x = 1;\n  }\n  if (!y || !Number.isFinite(y)) {\n    y = 1;\n  }\n  return {\n    x,\n    y\n  };\n}\n\nconst noOffsets = /*#__PURE__*/createCoords(0);\nfunction getVisualOffsets(element) {\n  const win = getWindow(element);\n  if (!isWebKit() || !win.visualViewport) {\n    return noOffsets;\n  }\n  return {\n    x: win.visualViewport.offsetLeft,\n    y: win.visualViewport.offsetTop\n  };\n}\nfunction shouldAddVisualOffsets(element, isFixed, floatingOffsetParent) {\n  if (isFixed === void 0) {\n    isFixed = false;\n  }\n  if (!floatingOffsetParent || isFixed && floatingOffsetParent !== getWindow(element)) {\n    return false;\n  }\n  return isFixed;\n}\n\nfunction getBoundingClientRect(element, includeScale, isFixedStrategy, offsetParent) {\n  if (includeScale === void 0) {\n    includeScale = false;\n  }\n  if (isFixedStrategy === void 0) {\n    isFixedStrategy = false;\n  }\n  const clientRect = element.getBoundingClientRect();\n  const domElement = unwrapElement(element);\n  let scale = createCoords(1);\n  if (includeScale) {\n    if (offsetParent) {\n      if (isElement(offsetParent)) {\n        scale = getScale(offsetParent);\n      }\n    } else {\n      scale = getScale(element);\n    }\n  }\n  const visualOffsets = shouldAddVisualOffsets(domElement, isFixedStrategy, offsetParent) ? getVisualOffsets(domElement) : createCoords(0);\n  let x = (clientRect.left + visualOffsets.x) / scale.x;\n  let y = (clientRect.top + visualOffsets.y) / scale.y;\n  let width = clientRect.width / scale.x;\n  let height = clientRect.height / scale.y;\n  if (domElement) {\n    const win = getWindow(domElement);\n    const offsetWin = offsetParent && isElement(offsetParent) ? getWindow(offsetParent) : offsetParent;\n    let currentIFrame = win.frameElement;\n    while (currentIFrame && offsetParent && offsetWin !== win) {\n      const iframeScale = getScale(currentIFrame);\n      const iframeRect = currentIFrame.getBoundingClientRect();\n      const css = getComputedStyle(currentIFrame);\n      const left = iframeRect.left + (currentIFrame.clientLeft + parseFloat(css.paddingLeft)) * iframeScale.x;\n      const top = iframeRect.top + (currentIFrame.clientTop + parseFloat(css.paddingTop)) * iframeScale.y;\n      x *= iframeScale.x;\n      y *= iframeScale.y;\n      width *= iframeScale.x;\n      height *= iframeScale.y;\n      x += left;\n      y += top;\n      currentIFrame = getWindow(currentIFrame).frameElement;\n    }\n  }\n  return rectToClientRect({\n    width,\n    height,\n    x,\n    y\n  });\n}\n\nfunction convertOffsetParentRelativeRectToViewportRelativeRect(_ref) {\n  let {\n    rect,\n    offsetParent,\n    strategy\n  } = _ref;\n  const isOffsetParentAnElement = isHTMLElement(offsetParent);\n  const documentElement = getDocumentElement(offsetParent);\n  if (offsetParent === documentElement) {\n    return rect;\n  }\n  let scroll = {\n    scrollLeft: 0,\n    scrollTop: 0\n  };\n  let scale = createCoords(1);\n  const offsets = createCoords(0);\n  if (isOffsetParentAnElement || !isOffsetParentAnElement && strategy !== 'fixed') {\n    if (getNodeName(offsetParent) !== 'body' || isOverflowElement(documentElement)) {\n      scroll = getNodeScroll(offsetParent);\n    }\n    if (isHTMLElement(offsetParent)) {\n      const offsetRect = getBoundingClientRect(offsetParent);\n      scale = getScale(offsetParent);\n      offsets.x = offsetRect.x + offsetParent.clientLeft;\n      offsets.y = offsetRect.y + offsetParent.clientTop;\n    }\n  }\n  return {\n    width: rect.width * scale.x,\n    height: rect.height * scale.y,\n    x: rect.x * scale.x - scroll.scrollLeft * scale.x + offsets.x,\n    y: rect.y * scale.y - scroll.scrollTop * scale.y + offsets.y\n  };\n}\n\nfunction getClientRects(element) {\n  return Array.from(element.getClientRects());\n}\n\nfunction getWindowScrollBarX(element) {\n  // If <html> has a CSS width greater than the viewport, then this will be\n  // incorrect for RTL.\n  return getBoundingClientRect(getDocumentElement(element)).left + getNodeScroll(element).scrollLeft;\n}\n\n// Gets the entire size of the scrollable document area, even extending outside\n// of the `<html>` and `<body>` rect bounds if horizontally scrollable.\nfunction getDocumentRect(element) {\n  const html = getDocumentElement(element);\n  const scroll = getNodeScroll(element);\n  const body = element.ownerDocument.body;\n  const width = max(html.scrollWidth, html.clientWidth, body.scrollWidth, body.clientWidth);\n  const height = max(html.scrollHeight, html.clientHeight, body.scrollHeight, body.clientHeight);\n  let x = -scroll.scrollLeft + getWindowScrollBarX(element);\n  const y = -scroll.scrollTop;\n  if (getComputedStyle(body).direction === 'rtl') {\n    x += max(html.clientWidth, body.clientWidth) - width;\n  }\n  return {\n    width,\n    height,\n    x,\n    y\n  };\n}\n\nfunction getViewportRect(element, strategy) {\n  const win = getWindow(element);\n  const html = getDocumentElement(element);\n  const visualViewport = win.visualViewport;\n  let width = html.clientWidth;\n  let height = html.clientHeight;\n  let x = 0;\n  let y = 0;\n  if (visualViewport) {\n    width = visualViewport.width;\n    height = visualViewport.height;\n    const visualViewportBased = isWebKit();\n    if (!visualViewportBased || visualViewportBased && strategy === 'fixed') {\n      x = visualViewport.offsetLeft;\n      y = visualViewport.offsetTop;\n    }\n  }\n  return {\n    width,\n    height,\n    x,\n    y\n  };\n}\n\n// Returns the inner client rect, subtracting scrollbars if present.\nfunction getInnerBoundingClientRect(element, strategy) {\n  const clientRect = getBoundingClientRect(element, true, strategy === 'fixed');\n  const top = clientRect.top + element.clientTop;\n  const left = clientRect.left + element.clientLeft;\n  const scale = isHTMLElement(element) ? getScale(element) : createCoords(1);\n  const width = element.clientWidth * scale.x;\n  const height = element.clientHeight * scale.y;\n  const x = left * scale.x;\n  const y = top * scale.y;\n  return {\n    width,\n    height,\n    x,\n    y\n  };\n}\nfunction getClientRectFromClippingAncestor(element, clippingAncestor, strategy) {\n  let rect;\n  if (clippingAncestor === 'viewport') {\n    rect = getViewportRect(element, strategy);\n  } else if (clippingAncestor === 'document') {\n    rect = getDocumentRect(getDocumentElement(element));\n  } else if (isElement(clippingAncestor)) {\n    rect = getInnerBoundingClientRect(clippingAncestor, strategy);\n  } else {\n    const visualOffsets = getVisualOffsets(element);\n    rect = {\n      ...clippingAncestor,\n      x: clippingAncestor.x - visualOffsets.x,\n      y: clippingAncestor.y - visualOffsets.y\n    };\n  }\n  return rectToClientRect(rect);\n}\nfunction hasFixedPositionAncestor(element, stopNode) {\n  const parentNode = getParentNode(element);\n  if (parentNode === stopNode || !isElement(parentNode) || isLastTraversableNode(parentNode)) {\n    return false;\n  }\n  return getComputedStyle(parentNode).position === 'fixed' || hasFixedPositionAncestor(parentNode, stopNode);\n}\n\n// A \"clipping ancestor\" is an `overflow` element with the characteristic of\n// clipping (or hiding) child elements. This returns all clipping ancestors\n// of the given element up the tree.\nfunction getClippingElementAncestors(element, cache) {\n  const cachedResult = cache.get(element);\n  if (cachedResult) {\n    return cachedResult;\n  }\n  let result = getOverflowAncestors(element, [], false).filter(el => isElement(el) && getNodeName(el) !== 'body');\n  let currentContainingBlockComputedStyle = null;\n  const elementIsFixed = getComputedStyle(element).position === 'fixed';\n  let currentNode = elementIsFixed ? getParentNode(element) : element;\n\n  // https://developer.mozilla.org/en-US/docs/Web/CSS/Containing_block#identifying_the_containing_block\n  while (isElement(currentNode) && !isLastTraversableNode(currentNode)) {\n    const computedStyle = getComputedStyle(currentNode);\n    const currentNodeIsContaining = isContainingBlock(currentNode);\n    if (!currentNodeIsContaining && computedStyle.position === 'fixed') {\n      currentContainingBlockComputedStyle = null;\n    }\n    const shouldDropCurrentNode = elementIsFixed ? !currentNodeIsContaining && !currentContainingBlockComputedStyle : !currentNodeIsContaining && computedStyle.position === 'static' && !!currentContainingBlockComputedStyle && ['absolute', 'fixed'].includes(currentContainingBlockComputedStyle.position) || isOverflowElement(currentNode) && !currentNodeIsContaining && hasFixedPositionAncestor(element, currentNode);\n    if (shouldDropCurrentNode) {\n      // Drop non-containing blocks.\n      result = result.filter(ancestor => ancestor !== currentNode);\n    } else {\n      // Record last containing block for next iteration.\n      currentContainingBlockComputedStyle = computedStyle;\n    }\n    currentNode = getParentNode(currentNode);\n  }\n  cache.set(element, result);\n  return result;\n}\n\n// Gets the maximum area that the element is visible in due to any number of\n// clipping ancestors.\nfunction getClippingRect(_ref) {\n  let {\n    element,\n    boundary,\n    rootBoundary,\n    strategy\n  } = _ref;\n  const elementClippingAncestors = boundary === 'clippingAncestors' ? getClippingElementAncestors(element, this._c) : [].concat(boundary);\n  const clippingAncestors = [...elementClippingAncestors, rootBoundary];\n  const firstClippingAncestor = clippingAncestors[0];\n  const clippingRect = clippingAncestors.reduce((accRect, clippingAncestor) => {\n    const rect = getClientRectFromClippingAncestor(element, clippingAncestor, strategy);\n    accRect.top = max(rect.top, accRect.top);\n    accRect.right = min(rect.right, accRect.right);\n    accRect.bottom = min(rect.bottom, accRect.bottom);\n    accRect.left = max(rect.left, accRect.left);\n    return accRect;\n  }, getClientRectFromClippingAncestor(element, firstClippingAncestor, strategy));\n  return {\n    width: clippingRect.right - clippingRect.left,\n    height: clippingRect.bottom - clippingRect.top,\n    x: clippingRect.left,\n    y: clippingRect.top\n  };\n}\n\nfunction getDimensions(element) {\n  const {\n    width,\n    height\n  } = getCssDimensions(element);\n  return {\n    width,\n    height\n  };\n}\n\nfunction getRectRelativeToOffsetParent(element, offsetParent, strategy) {\n  const isOffsetParentAnElement = isHTMLElement(offsetParent);\n  const documentElement = getDocumentElement(offsetParent);\n  const isFixed = strategy === 'fixed';\n  const rect = getBoundingClientRect(element, true, isFixed, offsetParent);\n  let scroll = {\n    scrollLeft: 0,\n    scrollTop: 0\n  };\n  const offsets = createCoords(0);\n  if (isOffsetParentAnElement || !isOffsetParentAnElement && !isFixed) {\n    if (getNodeName(offsetParent) !== 'body' || isOverflowElement(documentElement)) {\n      scroll = getNodeScroll(offsetParent);\n    }\n    if (isOffsetParentAnElement) {\n      const offsetRect = getBoundingClientRect(offsetParent, true, isFixed, offsetParent);\n      offsets.x = offsetRect.x + offsetParent.clientLeft;\n      offsets.y = offsetRect.y + offsetParent.clientTop;\n    } else if (documentElement) {\n      offsets.x = getWindowScrollBarX(documentElement);\n    }\n  }\n  return {\n    x: rect.left + scroll.scrollLeft - offsets.x,\n    y: rect.top + scroll.scrollTop - offsets.y,\n    width: rect.width,\n    height: rect.height\n  };\n}\n\nfunction getTrueOffsetParent(element, polyfill) {\n  if (!isHTMLElement(element) || getComputedStyle(element).position === 'fixed') {\n    return null;\n  }\n  if (polyfill) {\n    return polyfill(element);\n  }\n  return element.offsetParent;\n}\n\n// Gets the closest ancestor positioned element. Handles some edge cases,\n// such as table ancestors and cross browser bugs.\nfunction getOffsetParent(element, polyfill) {\n  const window = getWindow(element);\n  if (!isHTMLElement(element)) {\n    return window;\n  }\n  let offsetParent = getTrueOffsetParent(element, polyfill);\n  while (offsetParent && isTableElement(offsetParent) && getComputedStyle(offsetParent).position === 'static') {\n    offsetParent = getTrueOffsetParent(offsetParent, polyfill);\n  }\n  if (offsetParent && (getNodeName(offsetParent) === 'html' || getNodeName(offsetParent) === 'body' && getComputedStyle(offsetParent).position === 'static' && !isContainingBlock(offsetParent))) {\n    return window;\n  }\n  return offsetParent || getContainingBlock(element) || window;\n}\n\nconst getElementRects = async function (_ref) {\n  let {\n    reference,\n    floating,\n    strategy\n  } = _ref;\n  const getOffsetParentFn = this.getOffsetParent || getOffsetParent;\n  const getDimensionsFn = this.getDimensions;\n  return {\n    reference: getRectRelativeToOffsetParent(reference, await getOffsetParentFn(floating), strategy),\n    floating: {\n      x: 0,\n      y: 0,\n      ...(await getDimensionsFn(floating))\n    }\n  };\n};\n\nfunction isRTL(element) {\n  return getComputedStyle(element).direction === 'rtl';\n}\n\nconst platform = {\n  convertOffsetParentRelativeRectToViewportRelativeRect,\n  getDocumentElement,\n  getClippingRect,\n  getOffsetParent,\n  getElementRects,\n  getClientRects,\n  getDimensions,\n  getScale,\n  isElement,\n  isRTL\n};\n\n// https://samthor.au/2021/observing-dom/\nfunction observeMove(element, onMove) {\n  let io = null;\n  let timeoutId;\n  const root = getDocumentElement(element);\n  function cleanup() {\n    clearTimeout(timeoutId);\n    io && io.disconnect();\n    io = null;\n  }\n  function refresh(skip, threshold) {\n    if (skip === void 0) {\n      skip = false;\n    }\n    if (threshold === void 0) {\n      threshold = 1;\n    }\n    cleanup();\n    const {\n      left,\n      top,\n      width,\n      height\n    } = element.getBoundingClientRect();\n    if (!skip) {\n      onMove();\n    }\n    if (!width || !height) {\n      return;\n    }\n    const insetTop = floor(top);\n    const insetRight = floor(root.clientWidth - (left + width));\n    const insetBottom = floor(root.clientHeight - (top + height));\n    const insetLeft = floor(left);\n    const rootMargin = -insetTop + \"px \" + -insetRight + \"px \" + -insetBottom + \"px \" + -insetLeft + \"px\";\n    const options = {\n      rootMargin,\n      threshold: max(0, min(1, threshold)) || 1\n    };\n    let isFirstUpdate = true;\n    function handleObserve(entries) {\n      const ratio = entries[0].intersectionRatio;\n      if (ratio !== threshold) {\n        if (!isFirstUpdate) {\n          return refresh();\n        }\n        if (!ratio) {\n          timeoutId = setTimeout(() => {\n            refresh(false, 1e-7);\n          }, 100);\n        } else {\n          refresh(false, ratio);\n        }\n      }\n      isFirstUpdate = false;\n    }\n\n    // Older browsers don't support a `document` as the root and will throw an\n    // error.\n    try {\n      io = new IntersectionObserver(handleObserve, {\n        ...options,\n        // Handle <iframe>s\n        root: root.ownerDocument\n      });\n    } catch (e) {\n      io = new IntersectionObserver(handleObserve, options);\n    }\n    io.observe(element);\n  }\n  refresh(true);\n  return cleanup;\n}\n\n/**\n * Automatically updates the position of the floating element when necessary.\n * Should only be called when the floating element is mounted on the DOM or\n * visible on the screen.\n * @returns cleanup function that should be invoked when the floating element is\n * removed from the DOM or hidden from the screen.\n * @see https://floating-ui.com/docs/autoUpdate\n */\nfunction autoUpdate(reference, floating, update, options) {\n  if (options === void 0) {\n    options = {};\n  }\n  const {\n    ancestorScroll = true,\n    ancestorResize = true,\n    elementResize = typeof ResizeObserver === 'function',\n    layoutShift = typeof IntersectionObserver === 'function',\n    animationFrame = false\n  } = options;\n  const referenceEl = unwrapElement(reference);\n  const ancestors = ancestorScroll || ancestorResize ? [...(referenceEl ? getOverflowAncestors(referenceEl) : []), ...getOverflowAncestors(floating)] : [];\n  ancestors.forEach(ancestor => {\n    ancestorScroll && ancestor.addEventListener('scroll', update, {\n      passive: true\n    });\n    ancestorResize && ancestor.addEventListener('resize', update);\n  });\n  const cleanupIo = referenceEl && layoutShift ? observeMove(referenceEl, update) : null;\n  let reobserveFrame = -1;\n  let resizeObserver = null;\n  if (elementResize) {\n    resizeObserver = new ResizeObserver(_ref => {\n      let [firstEntry] = _ref;\n      if (firstEntry && firstEntry.target === referenceEl && resizeObserver) {\n        // Prevent update loops when using the `size` middleware.\n        // https://github.com/floating-ui/floating-ui/issues/1740\n        resizeObserver.unobserve(floating);\n        cancelAnimationFrame(reobserveFrame);\n        reobserveFrame = requestAnimationFrame(() => {\n          resizeObserver && resizeObserver.observe(floating);\n        });\n      }\n      update();\n    });\n    if (referenceEl && !animationFrame) {\n      resizeObserver.observe(referenceEl);\n    }\n    resizeObserver.observe(floating);\n  }\n  let frameId;\n  let prevRefRect = animationFrame ? getBoundingClientRect(reference) : null;\n  if (animationFrame) {\n    frameLoop();\n  }\n  function frameLoop() {\n    const nextRefRect = getBoundingClientRect(reference);\n    if (prevRefRect && (nextRefRect.x !== prevRefRect.x || nextRefRect.y !== prevRefRect.y || nextRefRect.width !== prevRefRect.width || nextRefRect.height !== prevRefRect.height)) {\n      update();\n    }\n    prevRefRect = nextRefRect;\n    frameId = requestAnimationFrame(frameLoop);\n  }\n  update();\n  return () => {\n    ancestors.forEach(ancestor => {\n      ancestorScroll && ancestor.removeEventListener('scroll', update);\n      ancestorResize && ancestor.removeEventListener('resize', update);\n    });\n    cleanupIo && cleanupIo();\n    resizeObserver && resizeObserver.disconnect();\n    resizeObserver = null;\n    if (animationFrame) {\n      cancelAnimationFrame(frameId);\n    }\n  };\n}\n\n/**\n * Optimizes the visibility of the floating element by choosing the placement\n * that has the most space available automatically, without needing to specify a\n * preferred placement. Alternative to `flip`.\n * @see https://floating-ui.com/docs/autoPlacement\n */\nconst autoPlacement = autoPlacement$1;\n\n/**\n * Optimizes the visibility of the floating element by shifting it in order to\n * keep it in view when it will overflow the clipping boundary.\n * @see https://floating-ui.com/docs/shift\n */\nconst shift = shift$1;\n\n/**\n * Optimizes the visibility of the floating element by flipping the `placement`\n * in order to keep it in view when the preferred placement(s) will overflow the\n * clipping boundary. Alternative to `autoPlacement`.\n * @see https://floating-ui.com/docs/flip\n */\nconst flip = flip$1;\n\n/**\n * Provides data that allows you to change the size of the floating element —\n * for instance, prevent it from overflowing the clipping boundary or match the\n * width of the reference element.\n * @see https://floating-ui.com/docs/size\n */\nconst size = size$1;\n\n/**\n * Provides data to hide the floating element in applicable situations, such as\n * when it is not in the same clipping context as the reference element.\n * @see https://floating-ui.com/docs/hide\n */\nconst hide = hide$1;\n\n/**\n * Provides data to position an inner element of the floating element so that it\n * appears centered to the reference element.\n * @see https://floating-ui.com/docs/arrow\n */\nconst arrow = arrow$1;\n\n/**\n * Provides improved positioning for inline reference elements that can span\n * over multiple lines, such as hyperlinks or range selections.\n * @see https://floating-ui.com/docs/inline\n */\nconst inline = inline$1;\n\n/**\n * Built-in `limiter` that will stop `shift()` at a certain point.\n */\nconst limitShift = limitShift$1;\n\n/**\n * Computes the `x` and `y` coordinates that will place the floating element\n * next to a given reference element.\n */\nconst computePosition = (reference, floating, options) => {\n  // This caches the expensive `getClippingElementAncestors` function so that\n  // multiple lifecycle resets re-use the same result. It only lives for a\n  // single call. If other functions become expensive, we can add them as well.\n  const cache = new Map();\n  const mergedOptions = {\n    platform,\n    ...options\n  };\n  const platformWithCache = {\n    ...mergedOptions.platform,\n    _c: cache\n  };\n  return computePosition$1(reference, floating, {\n    ...mergedOptions,\n    platform: platformWithCache\n  });\n};\n\nexport { arrow, autoPlacement, autoUpdate, computePosition, flip, hide, inline, limitShift, platform, shift, size };\n", "import { arrow as arrow$1, computePosition } from '@floating-ui/dom';\nexport { autoPlacement, autoUpdate, computePosition, detectOverflow, flip, getOverflowAncestors, hide, inline, limitShift, offset, platform, shift, size } from '@floating-ui/dom';\nimport * as React from 'react';\nimport { useLayoutEffect, useEffect } from 'react';\nimport * as ReactDOM from 'react-dom';\n\n/**\n * Provides data to position an inner element of the floating element so that it\n * appears centered to the reference element.\n * This wraps the core `arrow` middleware to allow React refs as the element.\n * @see https://floating-ui.com/docs/arrow\n */\nconst arrow = options => {\n  function isRef(value) {\n    return {}.hasOwnProperty.call(value, 'current');\n  }\n  return {\n    name: 'arrow',\n    options,\n    fn(state) {\n      const {\n        element,\n        padding\n      } = typeof options === 'function' ? options(state) : options;\n      if (element && isRef(element)) {\n        if (element.current != null) {\n          return arrow$1({\n            element: element.current,\n            padding\n          }).fn(state);\n        }\n        return {};\n      } else if (element) {\n        return arrow$1({\n          element,\n          padding\n        }).fn(state);\n      }\n      return {};\n    }\n  };\n};\n\nvar index = typeof document !== 'undefined' ? useLayoutEffect : useEffect;\n\n// Fork of `fast-deep-equal` that only does the comparisons we need and compares\n// functions\nfunction deepEqual(a, b) {\n  if (a === b) {\n    return true;\n  }\n  if (typeof a !== typeof b) {\n    return false;\n  }\n  if (typeof a === 'function' && a.toString() === b.toString()) {\n    return true;\n  }\n  let length, i, keys;\n  if (a && b && typeof a == 'object') {\n    if (Array.isArray(a)) {\n      length = a.length;\n      if (length != b.length) return false;\n      for (i = length; i-- !== 0;) {\n        if (!deepEqual(a[i], b[i])) {\n          return false;\n        }\n      }\n      return true;\n    }\n    keys = Object.keys(a);\n    length = keys.length;\n    if (length !== Object.keys(b).length) {\n      return false;\n    }\n    for (i = length; i-- !== 0;) {\n      if (!{}.hasOwnProperty.call(b, keys[i])) {\n        return false;\n      }\n    }\n    for (i = length; i-- !== 0;) {\n      const key = keys[i];\n      if (key === '_owner' && a.$$typeof) {\n        continue;\n      }\n      if (!deepEqual(a[key], b[key])) {\n        return false;\n      }\n    }\n    return true;\n  }\n  return a !== a && b !== b;\n}\n\nfunction getDPR(element) {\n  if (typeof window === 'undefined') {\n    return 1;\n  }\n  const win = element.ownerDocument.defaultView || window;\n  return win.devicePixelRatio || 1;\n}\n\nfunction roundByDPR(element, value) {\n  const dpr = getDPR(element);\n  return Math.round(value * dpr) / dpr;\n}\n\nfunction useLatestRef(value) {\n  const ref = React.useRef(value);\n  index(() => {\n    ref.current = value;\n  });\n  return ref;\n}\n\n/**\n * Provides data to position a floating element.\n * @see https://floating-ui.com/docs/useFloating\n */\nfunction useFloating(options) {\n  if (options === void 0) {\n    options = {};\n  }\n  const {\n    placement = 'bottom',\n    strategy = 'absolute',\n    middleware = [],\n    platform,\n    elements: {\n      reference: externalReference,\n      floating: externalFloating\n    } = {},\n    transform = true,\n    whileElementsMounted,\n    open\n  } = options;\n  const [data, setData] = React.useState({\n    x: 0,\n    y: 0,\n    strategy,\n    placement,\n    middlewareData: {},\n    isPositioned: false\n  });\n  const [latestMiddleware, setLatestMiddleware] = React.useState(middleware);\n  if (!deepEqual(latestMiddleware, middleware)) {\n    setLatestMiddleware(middleware);\n  }\n  const [_reference, _setReference] = React.useState(null);\n  const [_floating, _setFloating] = React.useState(null);\n  const setReference = React.useCallback(node => {\n    if (node != referenceRef.current) {\n      referenceRef.current = node;\n      _setReference(node);\n    }\n  }, [_setReference]);\n  const setFloating = React.useCallback(node => {\n    if (node !== floatingRef.current) {\n      floatingRef.current = node;\n      _setFloating(node);\n    }\n  }, [_setFloating]);\n  const referenceEl = externalReference || _reference;\n  const floatingEl = externalFloating || _floating;\n  const referenceRef = React.useRef(null);\n  const floatingRef = React.useRef(null);\n  const dataRef = React.useRef(data);\n  const whileElementsMountedRef = useLatestRef(whileElementsMounted);\n  const platformRef = useLatestRef(platform);\n  const update = React.useCallback(() => {\n    if (!referenceRef.current || !floatingRef.current) {\n      return;\n    }\n    const config = {\n      placement,\n      strategy,\n      middleware: latestMiddleware\n    };\n    if (platformRef.current) {\n      config.platform = platformRef.current;\n    }\n    computePosition(referenceRef.current, floatingRef.current, config).then(data => {\n      const fullData = {\n        ...data,\n        isPositioned: true\n      };\n      if (isMountedRef.current && !deepEqual(dataRef.current, fullData)) {\n        dataRef.current = fullData;\n        ReactDOM.flushSync(() => {\n          setData(fullData);\n        });\n      }\n    });\n  }, [latestMiddleware, placement, strategy, platformRef]);\n  index(() => {\n    if (open === false && dataRef.current.isPositioned) {\n      dataRef.current.isPositioned = false;\n      setData(data => ({\n        ...data,\n        isPositioned: false\n      }));\n    }\n  }, [open]);\n  const isMountedRef = React.useRef(false);\n  index(() => {\n    isMountedRef.current = true;\n    return () => {\n      isMountedRef.current = false;\n    };\n  }, []);\n  index(() => {\n    if (referenceEl) referenceRef.current = referenceEl;\n    if (floatingEl) floatingRef.current = floatingEl;\n    if (referenceEl && floatingEl) {\n      if (whileElementsMountedRef.current) {\n        return whileElementsMountedRef.current(referenceEl, floatingEl, update);\n      } else {\n        update();\n      }\n    }\n  }, [referenceEl, floatingEl, update, whileElementsMountedRef]);\n  const refs = React.useMemo(() => ({\n    reference: referenceRef,\n    floating: floatingRef,\n    setReference,\n    setFloating\n  }), [setReference, setFloating]);\n  const elements = React.useMemo(() => ({\n    reference: referenceEl,\n    floating: floatingEl\n  }), [referenceEl, floatingEl]);\n  const floatingStyles = React.useMemo(() => {\n    const initialStyles = {\n      position: strategy,\n      left: 0,\n      top: 0\n    };\n    if (!elements.floating) {\n      return initialStyles;\n    }\n    const x = roundByDPR(elements.floating, data.x);\n    const y = roundByDPR(elements.floating, data.y);\n    if (transform) {\n      return {\n        ...initialStyles,\n        transform: \"translate(\" + x + \"px, \" + y + \"px)\",\n        ...(getDPR(elements.floating) >= 1.5 && {\n          willChange: 'transform'\n        })\n      };\n    }\n    return {\n      position: strategy,\n      left: x,\n      top: y\n    };\n  }, [strategy, transform, elements.floating, data.x, data.y]);\n  return React.useMemo(() => ({\n    ...data,\n    update,\n    refs,\n    elements,\n    floatingStyles\n  }), [data, update, refs, elements, floatingStyles]);\n}\n\nexport { arrow, useFloating };\n", "// NOTE: separate `:not()` selectors has broader browser support than the newer\n//  `:not([inert], [inert] *)` (Feb 2023)\n// CAREFUL: JSDom does not support `:not([inert] *)` as a selector; using it causes\n//  the entire query to fail, resulting in no nodes found, which will break a lot\n//  of things... so we have to rely on JS to identify nodes inside an inert container\nconst candidateSelectors = [\n  'input:not([inert])',\n  'select:not([inert])',\n  'textarea:not([inert])',\n  'a[href]:not([inert])',\n  'button:not([inert])',\n  '[tabindex]:not(slot):not([inert])',\n  'audio[controls]:not([inert])',\n  'video[controls]:not([inert])',\n  '[contenteditable]:not([contenteditable=\"false\"]):not([inert])',\n  'details>summary:first-of-type:not([inert])',\n  'details:not([inert])',\n];\nconst candidateSelector = /* #__PURE__ */ candidateSelectors.join(',');\n\nconst NoElement = typeof Element === 'undefined';\n\nconst matches = NoElement\n  ? function () {}\n  : Element.prototype.matches ||\n    Element.prototype.msMatchesSelector ||\n    Element.prototype.webkitMatchesSelector;\n\nconst getRootNode =\n  !NoElement && Element.prototype.getRootNode\n    ? (element) => element?.getRootNode?.()\n    : (element) => element?.ownerDocument;\n\n/**\n * Determines if a node is inert or in an inert ancestor.\n * @param {Element} [node]\n * @param {boolean} [lookUp] If true and `node` is not inert, looks up at ancestors to\n *  see if any of them are inert. If false, only `node` itself is considered.\n * @returns {boolean} True if inert itself or by way of being in an inert ancestor.\n *  False if `node` is falsy.\n */\nconst isInert = function (node, lookUp = true) {\n  // CAREFUL: JSDom does not support inert at all, so we can't use the `HTMLElement.inert`\n  //  JS API property; we have to check the attribute, which can either be empty or 'true';\n  //  if it's `null` (not specified) or 'false', it's an active element\n  const inertAtt = node?.getAttribute?.('inert');\n  const inert = inertAtt === '' || inertAtt === 'true';\n\n  // NOTE: this could also be handled with `node.matches('[inert], :is([inert] *)')`\n  //  if it weren't for `matches()` not being a function on shadow roots; the following\n  //  code works for any kind of node\n  // CAREFUL: JSDom does not appear to support certain selectors like `:not([inert] *)`\n  //  so it likely would not support `:is([inert] *)` either...\n  const result = inert || (lookUp && node && isInert(node.parentNode)); // recursive\n\n  return result;\n};\n\n/**\n * Determines if a node's content is editable.\n * @param {Element} [node]\n * @returns True if it's content-editable; false if it's not or `node` is falsy.\n */\nconst isContentEditable = function (node) {\n  // CAREFUL: JSDom does not support the `HTMLElement.isContentEditable` API so we have\n  //  to use the attribute directly to check for this, which can either be empty or 'true';\n  //  if it's `null` (not specified) or 'false', it's a non-editable element\n  const attValue = node?.getAttribute?.('contenteditable');\n  return attValue === '' || attValue === 'true';\n};\n\n/**\n * @param {Element} el container to check in\n * @param {boolean} includeContainer add container to check\n * @param {(node: Element) => boolean} filter filter candidates\n * @returns {Element[]}\n */\nconst getCandidates = function (el, includeContainer, filter) {\n  // even if `includeContainer=false`, we still have to check it for inertness because\n  //  if it's inert, all its children are inert\n  if (isInert(el)) {\n    return [];\n  }\n\n  let candidates = Array.prototype.slice.apply(\n    el.querySelectorAll(candidateSelector)\n  );\n  if (includeContainer && matches.call(el, candidateSelector)) {\n    candidates.unshift(el);\n  }\n  candidates = candidates.filter(filter);\n  return candidates;\n};\n\n/**\n * @callback GetShadowRoot\n * @param {Element} element to check for shadow root\n * @returns {ShadowRoot|boolean} ShadowRoot if available or boolean indicating if a shadowRoot is attached but not available.\n */\n\n/**\n * @callback ShadowRootFilter\n * @param {Element} shadowHostNode the element which contains shadow content\n * @returns {boolean} true if a shadow root could potentially contain valid candidates.\n */\n\n/**\n * @typedef {Object} CandidateScope\n * @property {Element} scopeParent contains inner candidates\n * @property {Element[]} candidates list of candidates found in the scope parent\n */\n\n/**\n * @typedef {Object} IterativeOptions\n * @property {GetShadowRoot|boolean} getShadowRoot true if shadow support is enabled; falsy if not;\n *  if a function, implies shadow support is enabled and either returns the shadow root of an element\n *  or a boolean stating if it has an undisclosed shadow root\n * @property {(node: Element) => boolean} filter filter candidates\n * @property {boolean} flatten if true then result will flatten any CandidateScope into the returned list\n * @property {ShadowRootFilter} shadowRootFilter filter shadow roots;\n */\n\n/**\n * @param {Element[]} elements list of element containers to match candidates from\n * @param {boolean} includeContainer add container list to check\n * @param {IterativeOptions} options\n * @returns {Array.<Element|CandidateScope>}\n */\nconst getCandidatesIteratively = function (\n  elements,\n  includeContainer,\n  options\n) {\n  const candidates = [];\n  const elementsToCheck = Array.from(elements);\n  while (elementsToCheck.length) {\n    const element = elementsToCheck.shift();\n    if (isInert(element, false)) {\n      // no need to look up since we're drilling down\n      // anything inside this container will also be inert\n      continue;\n    }\n\n    if (element.tagName === 'SLOT') {\n      // add shadow dom slot scope (slot itself cannot be focusable)\n      const assigned = element.assignedElements();\n      const content = assigned.length ? assigned : element.children;\n      const nestedCandidates = getCandidatesIteratively(content, true, options);\n      if (options.flatten) {\n        candidates.push(...nestedCandidates);\n      } else {\n        candidates.push({\n          scopeParent: element,\n          candidates: nestedCandidates,\n        });\n      }\n    } else {\n      // check candidate element\n      const validCandidate = matches.call(element, candidateSelector);\n      if (\n        validCandidate &&\n        options.filter(element) &&\n        (includeContainer || !elements.includes(element))\n      ) {\n        candidates.push(element);\n      }\n\n      // iterate over shadow content if possible\n      const shadowRoot =\n        element.shadowRoot ||\n        // check for an undisclosed shadow\n        (typeof options.getShadowRoot === 'function' &&\n          options.getShadowRoot(element));\n\n      // no inert look up because we're already drilling down and checking for inertness\n      //  on the way down, so all containers to this root node should have already been\n      //  vetted as non-inert\n      const validShadowRoot =\n        !isInert(shadowRoot, false) &&\n        (!options.shadowRootFilter || options.shadowRootFilter(element));\n\n      if (shadowRoot && validShadowRoot) {\n        // add shadow dom scope IIF a shadow root node was given; otherwise, an undisclosed\n        //  shadow exists, so look at light dom children as fallback BUT create a scope for any\n        //  child candidates found because they're likely slotted elements (elements that are\n        //  children of the web component element (which has the shadow), in the light dom, but\n        //  slotted somewhere _inside_ the undisclosed shadow) -- the scope is created below,\n        //  _after_ we return from this recursive call\n        const nestedCandidates = getCandidatesIteratively(\n          shadowRoot === true ? element.children : shadowRoot.children,\n          true,\n          options\n        );\n\n        if (options.flatten) {\n          candidates.push(...nestedCandidates);\n        } else {\n          candidates.push({\n            scopeParent: element,\n            candidates: nestedCandidates,\n          });\n        }\n      } else {\n        // there's not shadow so just dig into the element's (light dom) children\n        //  __without__ giving the element special scope treatment\n        elementsToCheck.unshift(...element.children);\n      }\n    }\n  }\n  return candidates;\n};\n\n/**\n * @private\n * Determines if the node has an explicitly specified `tabindex` attribute.\n * @param {HTMLElement} node\n * @returns {boolean} True if so; false if not.\n */\nconst hasTabIndex = function (node) {\n  return !isNaN(parseInt(node.getAttribute('tabindex'), 10));\n};\n\n/**\n * Determine the tab index of a given node.\n * @param {HTMLElement} node\n * @returns {number} Tab order (negative, 0, or positive number).\n * @throws {Error} If `node` is falsy.\n */\nconst getTabIndex = function (node) {\n  if (!node) {\n    throw new Error('No node provided');\n  }\n\n  if (node.tabIndex < 0) {\n    // in Chrome, <details/>, <audio controls/> and <video controls/> elements get a default\n    // `tabIndex` of -1 when the 'tabindex' attribute isn't specified in the DOM,\n    // yet they are still part of the regular tab order; in FF, they get a default\n    // `tabIndex` of 0; since Chrome still puts those elements in the regular tab\n    // order, consider their tab index to be 0.\n    // Also browsers do not return `tabIndex` correctly for contentEditable nodes;\n    // so if they don't have a tabindex attribute specifically set, assume it's 0.\n    if (\n      (/^(AUDIO|VIDEO|DETAILS)$/.test(node.tagName) ||\n        isContentEditable(node)) &&\n      !hasTabIndex(node)\n    ) {\n      return 0;\n    }\n  }\n\n  return node.tabIndex;\n};\n\n/**\n * Determine the tab index of a given node __for sort order purposes__.\n * @param {HTMLElement} node\n * @param {boolean} [isScope] True for a custom element with shadow root or slot that, by default,\n *  has tabIndex -1, but needs to be sorted by document order in order for its content to be\n *  inserted into the correct sort position.\n * @returns {number} Tab order (negative, 0, or positive number).\n */\nconst getSortOrderTabIndex = function (node, isScope) {\n  const tabIndex = getTabIndex(node);\n\n  if (tabIndex < 0 && isScope && !hasTabIndex(node)) {\n    return 0;\n  }\n\n  return tabIndex;\n};\n\nconst sortOrderedTabbables = function (a, b) {\n  return a.tabIndex === b.tabIndex\n    ? a.documentOrder - b.documentOrder\n    : a.tabIndex - b.tabIndex;\n};\n\nconst isInput = function (node) {\n  return node.tagName === 'INPUT';\n};\n\nconst isHiddenInput = function (node) {\n  return isInput(node) && node.type === 'hidden';\n};\n\nconst isDetailsWithSummary = function (node) {\n  const r =\n    node.tagName === 'DETAILS' &&\n    Array.prototype.slice\n      .apply(node.children)\n      .some((child) => child.tagName === 'SUMMARY');\n  return r;\n};\n\nconst getCheckedRadio = function (nodes, form) {\n  for (let i = 0; i < nodes.length; i++) {\n    if (nodes[i].checked && nodes[i].form === form) {\n      return nodes[i];\n    }\n  }\n};\n\nconst isTabbableRadio = function (node) {\n  if (!node.name) {\n    return true;\n  }\n  const radioScope = node.form || getRootNode(node);\n  const queryRadios = function (name) {\n    return radioScope.querySelectorAll(\n      'input[type=\"radio\"][name=\"' + name + '\"]'\n    );\n  };\n\n  let radioSet;\n  if (\n    typeof window !== 'undefined' &&\n    typeof window.CSS !== 'undefined' &&\n    typeof window.CSS.escape === 'function'\n  ) {\n    radioSet = queryRadios(window.CSS.escape(node.name));\n  } else {\n    try {\n      radioSet = queryRadios(node.name);\n    } catch (err) {\n      // eslint-disable-next-line no-console\n      console.error(\n        'Looks like you have a radio button with a name attribute containing invalid CSS selector characters and need the CSS.escape polyfill: %s',\n        err.message\n      );\n      return false;\n    }\n  }\n\n  const checked = getCheckedRadio(radioSet, node.form);\n  return !checked || checked === node;\n};\n\nconst isRadio = function (node) {\n  return isInput(node) && node.type === 'radio';\n};\n\nconst isNonTabbableRadio = function (node) {\n  return isRadio(node) && !isTabbableRadio(node);\n};\n\n// determines if a node is ultimately attached to the window's document\nconst isNodeAttached = function (node) {\n  // The root node is the shadow root if the node is in a shadow DOM; some document otherwise\n  //  (but NOT _the_ document; see second 'If' comment below for more).\n  // If rootNode is shadow root, it'll have a host, which is the element to which the shadow\n  //  is attached, and the one we need to check if it's in the document or not (because the\n  //  shadow, and all nodes it contains, is never considered in the document since shadows\n  //  behave like self-contained DOMs; but if the shadow's HOST, which is part of the document,\n  //  is hidden, or is not in the document itself but is detached, it will affect the shadow's\n  //  visibility, including all the nodes it contains). The host could be any normal node,\n  //  or a custom element (i.e. web component). Either way, that's the one that is considered\n  //  part of the document, not the shadow root, nor any of its children (i.e. the node being\n  //  tested).\n  // To further complicate things, we have to look all the way up until we find a shadow HOST\n  //  that is attached (or find none) because the node might be in nested shadows...\n  // If rootNode is not a shadow root, it won't have a host, and so rootNode should be the\n  //  document (per the docs) and while it's a Document-type object, that document does not\n  //  appear to be the same as the node's `ownerDocument` for some reason, so it's safer\n  //  to ignore the rootNode at this point, and use `node.ownerDocument`. Otherwise,\n  //  using `rootNode.contains(node)` will _always_ be true we'll get false-positives when\n  //  node is actually detached.\n  // NOTE: If `nodeRootHost` or `node` happens to be the `document` itself (which is possible\n  //  if a tabbable/focusable node was quickly added to the DOM, focused, and then removed\n  //  from the DOM as in https://github.com/focus-trap/focus-trap-react/issues/905), then\n  //  `ownerDocument` will be `null`, hence the optional chaining on it.\n  let nodeRoot = node && getRootNode(node);\n  let nodeRootHost = nodeRoot?.host;\n\n  // in some cases, a detached node will return itself as the root instead of a document or\n  //  shadow root object, in which case, we shouldn't try to look further up the host chain\n  let attached = false;\n  if (nodeRoot && nodeRoot !== node) {\n    attached = !!(\n      nodeRootHost?.ownerDocument?.contains(nodeRootHost) ||\n      node?.ownerDocument?.contains(node)\n    );\n\n    while (!attached && nodeRootHost) {\n      // since it's not attached and we have a root host, the node MUST be in a nested shadow DOM,\n      //  which means we need to get the host's host and check if that parent host is contained\n      //  in (i.e. attached to) the document\n      nodeRoot = getRootNode(nodeRootHost);\n      nodeRootHost = nodeRoot?.host;\n      attached = !!nodeRootHost?.ownerDocument?.contains(nodeRootHost);\n    }\n  }\n\n  return attached;\n};\n\nconst isZeroArea = function (node) {\n  const { width, height } = node.getBoundingClientRect();\n  return width === 0 && height === 0;\n};\nconst isHidden = function (node, { displayCheck, getShadowRoot }) {\n  // NOTE: visibility will be `undefined` if node is detached from the document\n  //  (see notes about this further down), which means we will consider it visible\n  //  (this is legacy behavior from a very long way back)\n  // NOTE: we check this regardless of `displayCheck=\"none\"` because this is a\n  //  _visibility_ check, not a _display_ check\n  if (getComputedStyle(node).visibility === 'hidden') {\n    return true;\n  }\n\n  const isDirectSummary = matches.call(node, 'details>summary:first-of-type');\n  const nodeUnderDetails = isDirectSummary ? node.parentElement : node;\n  if (matches.call(nodeUnderDetails, 'details:not([open]) *')) {\n    return true;\n  }\n\n  if (\n    !displayCheck ||\n    displayCheck === 'full' ||\n    displayCheck === 'legacy-full'\n  ) {\n    if (typeof getShadowRoot === 'function') {\n      // figure out if we should consider the node to be in an undisclosed shadow and use the\n      //  'non-zero-area' fallback\n      const originalNode = node;\n      while (node) {\n        const parentElement = node.parentElement;\n        const rootNode = getRootNode(node);\n        if (\n          parentElement &&\n          !parentElement.shadowRoot &&\n          getShadowRoot(parentElement) === true // check if there's an undisclosed shadow\n        ) {\n          // node has an undisclosed shadow which means we can only treat it as a black box, so we\n          //  fall back to a non-zero-area test\n          return isZeroArea(node);\n        } else if (node.assignedSlot) {\n          // iterate up slot\n          node = node.assignedSlot;\n        } else if (!parentElement && rootNode !== node.ownerDocument) {\n          // cross shadow boundary\n          node = rootNode.host;\n        } else {\n          // iterate up normal dom\n          node = parentElement;\n        }\n      }\n\n      node = originalNode;\n    }\n    // else, `getShadowRoot` might be true, but all that does is enable shadow DOM support\n    //  (i.e. it does not also presume that all nodes might have undisclosed shadows); or\n    //  it might be a falsy value, which means shadow DOM support is disabled\n\n    // Since we didn't find it sitting in an undisclosed shadow (or shadows are disabled)\n    //  now we can just test to see if it would normally be visible or not, provided it's\n    //  attached to the main document.\n    // NOTE: We must consider case where node is inside a shadow DOM and given directly to\n    //  `isTabbable()` or `isFocusable()` -- regardless of `getShadowRoot` option setting.\n\n    if (isNodeAttached(node)) {\n      // this works wherever the node is: if there's at least one client rect, it's\n      //  somehow displayed; it also covers the CSS 'display: contents' case where the\n      //  node itself is hidden in place of its contents; and there's no need to search\n      //  up the hierarchy either\n      return !node.getClientRects().length;\n    }\n\n    // Else, the node isn't attached to the document, which means the `getClientRects()`\n    //  API will __always__ return zero rects (this can happen, for example, if React\n    //  is used to render nodes onto a detached tree, as confirmed in this thread:\n    //  https://github.com/facebook/react/issues/9117#issuecomment-284228870)\n    //\n    // It also means that even window.getComputedStyle(node).display will return `undefined`\n    //  because styles are only computed for nodes that are in the document.\n    //\n    // NOTE: THIS HAS BEEN THE CASE FOR YEARS. It is not new, nor is it caused by tabbable\n    //  somehow. Though it was never stated officially, anyone who has ever used tabbable\n    //  APIs on nodes in detached containers has actually implicitly used tabbable in what\n    //  was later (as of v5.2.0 on Apr 9, 2021) called `displayCheck=\"none\"` mode -- essentially\n    //  considering __everything__ to be visible because of the innability to determine styles.\n    //\n    // v6.0.0: As of this major release, the default 'full' option __no longer treats detached\n    //  nodes as visible with the 'none' fallback.__\n    if (displayCheck !== 'legacy-full') {\n      return true; // hidden\n    }\n    // else, fallback to 'none' mode and consider the node visible\n  } else if (displayCheck === 'non-zero-area') {\n    // NOTE: Even though this tests that the node's client rect is non-zero to determine\n    //  whether it's displayed, and that a detached node will __always__ have a zero-area\n    //  client rect, we don't special-case for whether the node is attached or not. In\n    //  this mode, we do want to consider nodes that have a zero area to be hidden at all\n    //  times, and that includes attached or not.\n    return isZeroArea(node);\n  }\n\n  // visible, as far as we can tell, or per current `displayCheck=none` mode, we assume\n  //  it's visible\n  return false;\n};\n\n// form fields (nested) inside a disabled fieldset are not focusable/tabbable\n//  unless they are in the _first_ <legend> element of the top-most disabled\n//  fieldset\nconst isDisabledFromFieldset = function (node) {\n  if (/^(INPUT|BUTTON|SELECT|TEXTAREA)$/.test(node.tagName)) {\n    let parentNode = node.parentElement;\n    // check if `node` is contained in a disabled <fieldset>\n    while (parentNode) {\n      if (parentNode.tagName === 'FIELDSET' && parentNode.disabled) {\n        // look for the first <legend> among the children of the disabled <fieldset>\n        for (let i = 0; i < parentNode.children.length; i++) {\n          const child = parentNode.children.item(i);\n          // when the first <legend> (in document order) is found\n          if (child.tagName === 'LEGEND') {\n            // if its parent <fieldset> is not nested in another disabled <fieldset>,\n            // return whether `node` is a descendant of its first <legend>\n            return matches.call(parentNode, 'fieldset[disabled] *')\n              ? true\n              : !child.contains(node);\n          }\n        }\n        // the disabled <fieldset> containing `node` has no <legend>\n        return true;\n      }\n      parentNode = parentNode.parentElement;\n    }\n  }\n\n  // else, node's tabbable/focusable state should not be affected by a fieldset's\n  //  enabled/disabled state\n  return false;\n};\n\nconst isNodeMatchingSelectorFocusable = function (options, node) {\n  if (\n    node.disabled ||\n    // we must do an inert look up to filter out any elements inside an inert ancestor\n    //  because we're limited in the type of selectors we can use in JSDom (see related\n    //  note related to `candidateSelectors`)\n    isInert(node) ||\n    isHiddenInput(node) ||\n    isHidden(node, options) ||\n    // For a details element with a summary, the summary element gets the focus\n    isDetailsWithSummary(node) ||\n    isDisabledFromFieldset(node)\n  ) {\n    return false;\n  }\n  return true;\n};\n\nconst isNodeMatchingSelectorTabbable = function (options, node) {\n  if (\n    isNonTabbableRadio(node) ||\n    getTabIndex(node) < 0 ||\n    !isNodeMatchingSelectorFocusable(options, node)\n  ) {\n    return false;\n  }\n  return true;\n};\n\nconst isValidShadowRootTabbable = function (shadowHostNode) {\n  const tabIndex = parseInt(shadowHostNode.getAttribute('tabindex'), 10);\n  if (isNaN(tabIndex) || tabIndex >= 0) {\n    return true;\n  }\n  // If a custom element has an explicit negative tabindex,\n  // browsers will not allow tab targeting said element's children.\n  return false;\n};\n\n/**\n * @param {Array.<Element|CandidateScope>} candidates\n * @returns Element[]\n */\nconst sortByOrder = function (candidates) {\n  const regularTabbables = [];\n  const orderedTabbables = [];\n  candidates.forEach(function (item, i) {\n    const isScope = !!item.scopeParent;\n    const element = isScope ? item.scopeParent : item;\n    const candidateTabindex = getSortOrderTabIndex(element, isScope);\n    const elements = isScope ? sortByOrder(item.candidates) : element;\n    if (candidateTabindex === 0) {\n      isScope\n        ? regularTabbables.push(...elements)\n        : regularTabbables.push(element);\n    } else {\n      orderedTabbables.push({\n        documentOrder: i,\n        tabIndex: candidateTabindex,\n        item: item,\n        isScope: isScope,\n        content: elements,\n      });\n    }\n  });\n\n  return orderedTabbables\n    .sort(sortOrderedTabbables)\n    .reduce((acc, sortable) => {\n      sortable.isScope\n        ? acc.push(...sortable.content)\n        : acc.push(sortable.content);\n      return acc;\n    }, [])\n    .concat(regularTabbables);\n};\n\nconst tabbable = function (container, options) {\n  options = options || {};\n\n  let candidates;\n  if (options.getShadowRoot) {\n    candidates = getCandidatesIteratively(\n      [container],\n      options.includeContainer,\n      {\n        filter: isNodeMatchingSelectorTabbable.bind(null, options),\n        flatten: false,\n        getShadowRoot: options.getShadowRoot,\n        shadowRootFilter: isValidShadowRootTabbable,\n      }\n    );\n  } else {\n    candidates = getCandidates(\n      container,\n      options.includeContainer,\n      isNodeMatchingSelectorTabbable.bind(null, options)\n    );\n  }\n  return sortByOrder(candidates);\n};\n\nconst focusable = function (container, options) {\n  options = options || {};\n\n  let candidates;\n  if (options.getShadowRoot) {\n    candidates = getCandidatesIteratively(\n      [container],\n      options.includeContainer,\n      {\n        filter: isNodeMatchingSelectorFocusable.bind(null, options),\n        flatten: true,\n        getShadowRoot: options.getShadowRoot,\n      }\n    );\n  } else {\n    candidates = getCandidates(\n      container,\n      options.includeContainer,\n      isNodeMatchingSelectorFocusable.bind(null, options)\n    );\n  }\n\n  return candidates;\n};\n\nconst isTabbable = function (node, options) {\n  options = options || {};\n  if (!node) {\n    throw new Error('No node provided');\n  }\n  if (matches.call(node, candidateSelector) === false) {\n    return false;\n  }\n  return isNodeMatchingSelectorTabbable(options, node);\n};\n\nconst focusableCandidateSelector = /* #__PURE__ */ candidateSelectors\n  .concat('iframe')\n  .join(',');\n\nconst isFocusable = function (node, options) {\n  options = options || {};\n  if (!node) {\n    throw new Error('No node provided');\n  }\n  if (matches.call(node, focusableCandidateSelector) === false) {\n    return false;\n  }\n  return isNodeMatchingSelectorFocusable(options, node);\n};\n\nexport { tabbable, focusable, isTabbable, isFocusable, getTabIndex };\n"], "mappings": ";;;;;;;;;;;AAAA,IAAAA,SAAuB;AACvB,IAAAC,gBAAmD;;;ACDnD,IAAM,QAAQ,CAAC,OAAO,SAAS,UAAU,MAAM;AAC/C,IAAM,aAAa,CAAC,SAAS,KAAK;AAClC,IAAM,aAA0B,MAAM,OAAO,CAAC,KAAK,SAAS,IAAI,OAAO,MAAM,OAAO,MAAM,WAAW,CAAC,GAAG,OAAO,MAAM,WAAW,CAAC,CAAC,GAAG,CAAC,CAAC;AAIxI,IAAM,QAAQ,KAAK;;;ACNnB,SAAS,YAAY,MAAM;AACzB,MAAI,OAAO,IAAI,GAAG;AAChB,YAAQ,KAAK,YAAY,IAAI,YAAY;AAAA,EAC3C;AAIA,SAAO;AACT;AACA,SAAS,UAAU,MAAM;AACvB,MAAI;AACJ,UAAQ,QAAQ,OAAO,UAAU,sBAAsB,KAAK,kBAAkB,OAAO,SAAS,oBAAoB,gBAAgB;AACpI;AACA,SAAS,mBAAmB,MAAM;AAChC,MAAI;AACJ,UAAQ,QAAQ,OAAO,IAAI,IAAI,KAAK,gBAAgB,KAAK,aAAa,OAAO,aAAa,OAAO,SAAS,KAAK;AACjH;AACA,SAAS,OAAO,OAAO;AACrB,SAAO,iBAAiB,QAAQ,iBAAiB,UAAU,KAAK,EAAE;AACpE;AACA,SAAS,UAAU,OAAO;AACxB,SAAO,iBAAiB,WAAW,iBAAiB,UAAU,KAAK,EAAE;AACvE;AACA,SAAS,cAAc,OAAO;AAC5B,SAAO,iBAAiB,eAAe,iBAAiB,UAAU,KAAK,EAAE;AAC3E;AACA,SAAS,aAAa,OAAO;AAE3B,MAAI,OAAO,eAAe,aAAa;AACrC,WAAO;AAAA,EACT;AACA,SAAO,iBAAiB,cAAc,iBAAiB,UAAU,KAAK,EAAE;AAC1E;AAmCA,SAAS,sBAAsB,MAAM;AACnC,SAAO,CAAC,QAAQ,QAAQ,WAAW,EAAE,SAAS,YAAY,IAAI,CAAC;AACjE;AACA,SAASC,kBAAiB,SAAS;AACjC,SAAO,UAAU,OAAO,EAAE,iBAAiB,OAAO;AACpD;AAaA,SAAS,cAAc,MAAM;AAC3B,MAAI,YAAY,IAAI,MAAM,QAAQ;AAChC,WAAO;AAAA,EACT;AACA,QAAM;AAAA;AAAA,IAEN,KAAK;AAAA,IAEL,KAAK;AAAA,IAEL,aAAa,IAAI,KAAK,KAAK;AAAA,IAE3B,mBAAmB,IAAI;AAAA;AACvB,SAAO,aAAa,MAAM,IAAI,OAAO,OAAO;AAC9C;;;ACjGA,SAAS,cAAc,KAAK;AAC1B,MAAIC,iBAAgB,IAAI;AACxB,WAAS,iBAAiBA,mBAAkB,OAAO,UAAU,wBAAwB,eAAe,eAAe,OAAO,SAAS,sBAAsB,kBAAkB,MAAM;AAC/K,QAAI,gBAAgB;AACpB,IAAAA,iBAAgBA,eAAc,WAAW;AAAA,EAC3C;AACA,SAAOA;AACT;AACA,SAAS,SAAS,QAAQ,OAAO;AAC/B,MAAI,CAAC,UAAU,CAAC,OAAO;AACrB,WAAO;AAAA,EACT;AACA,QAAM,WAAW,MAAM,eAAe,MAAM,YAAY;AAGxD,MAAI,OAAO,SAAS,KAAK,GAAG;AAC1B,WAAO;AAAA,EACT;AAGA,MAAI,YAAY,aAAa,QAAQ,GAAG;AACtC,QAAI,OAAO;AACX,WAAO,MAAM;AACX,UAAI,WAAW,MAAM;AACnB,eAAO;AAAA,MACT;AAEA,aAAO,KAAK,cAAc,KAAK;AAAA,IACjC;AAAA,EACF;AAGA,SAAO;AACT;AAEA,SAAS,cAAc;AACrB,QAAM,SAAS,UAAU;AACzB,MAAI,UAAU,QAAQ,OAAO,UAAU;AACrC,WAAO,OAAO;AAAA,EAChB;AACA,SAAO,UAAU;AACnB;AACA,SAAS,eAAe;AACtB,QAAM,SAAS,UAAU;AACzB,MAAI,UAAU,MAAM,QAAQ,OAAO,MAAM,GAAG;AAC1C,WAAO,OAAO,OAAO,IAAI,UAAQ;AAC/B,UAAI;AAAA,QACF;AAAA,QACA;AAAA,MACF,IAAI;AACJ,aAAO,QAAQ,MAAM;AAAA,IACvB,CAAC,EAAE,KAAK,GAAG;AAAA,EACb;AACA,SAAO,UAAU;AACnB;AAGA,SAAS,eAAe,OAAO;AAC7B,MAAI,MAAM,mBAAmB,KAAK,MAAM,WAAW;AACjD,WAAO;AAAA,EACT;AACA,QAAM,YAAY;AAClB,OAAK,UAAU,KAAK,YAAY,CAAC,KAAK,UAAU,KAAK,aAAa,CAAC,MAAM,MAAM,aAAa;AAC1F,WAAO,MAAM,SAAS,WAAW,MAAM,YAAY;AAAA,EACrD;AACA,SAAO,MAAM,WAAW,KAAK,CAAC,MAAM;AACtC;AACA,SAAS,sBAAsB,OAAO;AACpC,SAAO,MAAM,UAAU,KAAK,MAAM,WAAW,KAAK,MAAM,UAAU,KAAK,MAAM,WAAW,KAAK,MAAM,aAAa,KAAK,MAAM,WAAW,KAAK,MAAM,gBAAgB;AAAA,EAEjK,MAAM,QAAQ,KAAK,MAAM,SAAS,KAAK,MAAM,aAAa,KAAK,MAAM,WAAW;AAClF;AACA,SAAS,WAAW;AAElB,SAAO,SAAS,KAAK,UAAU,MAAM;AACvC;AACA,SAAS,QAAQ;AACf,SAAO,YAAY,EAAE,YAAY,EAAE,WAAW,KAAK,KAAK,CAAC,UAAU;AACrE;AACA,SAAS,uBAAuB,aAAa,QAAQ;AAGnD,QAAM,SAAS,CAAC,SAAS,KAAK;AAC9B,MAAI,CAAC,QAAQ;AACX,WAAO,KAAK,IAAI,MAAS;AAAA,EAC3B;AACA,SAAO,OAAO,SAAS,WAAW;AACpC;AACA,SAAS,aAAa,OAAO;AAC3B,SAAO,iBAAiB;AAC1B;AACA,SAAS,cAAc,SAAS;AAC9B,SAAO,QAAQ,QAAQ,WAAW;AACpC;AACA,SAAS,YAAY,MAAM;AACzB,UAAQ,QAAQ,OAAO,SAAS,KAAK,kBAAkB;AACzD;AACA,SAAS,oBAAoB,OAAO,MAAM;AACxC,MAAI,QAAQ,MAAM;AAChB,WAAO;AAAA,EACT;AACA,MAAI,kBAAkB,OAAO;AAC3B,WAAO,MAAM,aAAa,EAAE,SAAS,IAAI;AAAA,EAC3C;AAGA,QAAM,IAAI;AACV,SAAO,EAAE,UAAU,QAAQ,KAAK,SAAS,EAAE,MAAM;AACnD;AACA,SAAS,UAAU,OAAO;AACxB,MAAI,kBAAkB,OAAO;AAC3B,WAAO,MAAM,aAAa,EAAE,CAAC;AAAA,EAC/B;AAIA,SAAO,MAAM;AACf;AACA,IAAM,oBAAoB;AAC1B,SAAS,kBAAkB,SAAS;AAClC,SAAO,cAAc,OAAO,KAAK,QAAQ,QAAQ,iBAAiB;AACpE;AACA,SAAS,UAAU,OAAO;AACxB,QAAM,eAAe;AACrB,QAAM,gBAAgB;AACxB;;;AC1HA,IAAMC,SAAQ,CAAC,OAAO,SAAS,UAAU,MAAM;AAC/C,IAAMC,cAAa,CAAC,SAAS,KAAK;AAClC,IAAMC,cAA0BF,OAAM,OAAO,CAAC,KAAK,SAAS,IAAI,OAAO,MAAM,OAAO,MAAMC,YAAW,CAAC,GAAG,OAAO,MAAMA,YAAW,CAAC,CAAC,GAAG,CAAC,CAAC;AACxI,IAAM,MAAM,KAAK;AACjB,IAAM,MAAM,KAAK;AAOjB,IAAM,kBAAkB;AAAA,EACtB,MAAM;AAAA,EACN,OAAO;AAAA,EACP,QAAQ;AAAA,EACR,KAAK;AACP;AACA,IAAM,uBAAuB;AAAA,EAC3B,OAAO;AAAA,EACP,KAAK;AACP;AACA,SAAS,MAAM,OAAO,OAAO,KAAK;AAChC,SAAO,IAAI,OAAO,IAAI,OAAO,GAAG,CAAC;AACnC;AACA,SAAS,SAAS,OAAO,OAAO;AAC9B,SAAO,OAAO,UAAU,aAAa,MAAM,KAAK,IAAI;AACtD;AACA,SAAS,QAAQ,WAAW;AAC1B,SAAO,UAAU,MAAM,GAAG,EAAE,CAAC;AAC/B;AACA,SAAS,aAAa,WAAW;AAC/B,SAAO,UAAU,MAAM,GAAG,EAAE,CAAC;AAC/B;AACA,SAAS,gBAAgB,MAAM;AAC7B,SAAO,SAAS,MAAM,MAAM;AAC9B;AACA,SAAS,cAAc,MAAM;AAC3B,SAAO,SAAS,MAAM,WAAW;AACnC;AACA,SAAS,YAAY,WAAW;AAC9B,SAAO,CAAC,OAAO,QAAQ,EAAE,SAAS,QAAQ,SAAS,CAAC,IAAI,MAAM;AAChE;AACA,SAAS,iBAAiB,WAAW;AACnC,SAAO,gBAAgB,YAAY,SAAS,CAAC;AAC/C;AACA,SAAS,kBAAkB,WAAW,OAAO,KAAK;AAChD,MAAI,QAAQ,QAAQ;AAClB,UAAM;AAAA,EACR;AACA,QAAM,YAAY,aAAa,SAAS;AACxC,QAAM,gBAAgB,iBAAiB,SAAS;AAChD,QAAM,SAAS,cAAc,aAAa;AAC1C,MAAI,oBAAoB,kBAAkB,MAAM,eAAe,MAAM,QAAQ,WAAW,UAAU,SAAS,cAAc,UAAU,WAAW;AAC9I,MAAI,MAAM,UAAU,MAAM,IAAI,MAAM,SAAS,MAAM,GAAG;AACpD,wBAAoB,qBAAqB,iBAAiB;AAAA,EAC5D;AACA,SAAO,CAAC,mBAAmB,qBAAqB,iBAAiB,CAAC;AACpE;AACA,SAAS,sBAAsB,WAAW;AACxC,QAAM,oBAAoB,qBAAqB,SAAS;AACxD,SAAO,CAAC,8BAA8B,SAAS,GAAG,mBAAmB,8BAA8B,iBAAiB,CAAC;AACvH;AACA,SAAS,8BAA8B,WAAW;AAChD,SAAO,UAAU,QAAQ,cAAc,eAAa,qBAAqB,SAAS,CAAC;AACrF;AACA,SAAS,YAAY,MAAM,SAAS,KAAK;AACvC,QAAM,KAAK,CAAC,QAAQ,OAAO;AAC3B,QAAM,KAAK,CAAC,SAAS,MAAM;AAC3B,QAAM,KAAK,CAAC,OAAO,QAAQ;AAC3B,QAAM,KAAK,CAAC,UAAU,KAAK;AAC3B,UAAQ,MAAM;AAAA,IACZ,KAAK;AAAA,IACL,KAAK;AACH,UAAI;AAAK,eAAO,UAAU,KAAK;AAC/B,aAAO,UAAU,KAAK;AAAA,IACxB,KAAK;AAAA,IACL,KAAK;AACH,aAAO,UAAU,KAAK;AAAA,IACxB;AACE,aAAO,CAAC;AAAA,EACZ;AACF;AACA,SAAS,0BAA0B,WAAW,eAAe,WAAW,KAAK;AAC3E,QAAM,YAAY,aAAa,SAAS;AACxC,MAAI,OAAO,YAAY,QAAQ,SAAS,GAAG,cAAc,SAAS,GAAG;AACrE,MAAI,WAAW;AACb,WAAO,KAAK,IAAI,UAAQ,OAAO,MAAM,SAAS;AAC9C,QAAI,eAAe;AACjB,aAAO,KAAK,OAAO,KAAK,IAAI,6BAA6B,CAAC;AAAA,IAC5D;AAAA,EACF;AACA,SAAO;AACT;AACA,SAAS,qBAAqB,WAAW;AACvC,SAAO,UAAU,QAAQ,0BAA0B,UAAQ,gBAAgB,IAAI,CAAC;AAClF;AACA,SAAS,oBAAoB,SAAS;AACpC,SAAO;AAAA,IACL,KAAK;AAAA,IACL,OAAO;AAAA,IACP,QAAQ;AAAA,IACR,MAAM;AAAA,IACN,GAAG;AAAA,EACL;AACF;AACA,SAAS,iBAAiB,SAAS;AACjC,SAAO,OAAO,YAAY,WAAW,oBAAoB,OAAO,IAAI;AAAA,IAClE,KAAK;AAAA,IACL,OAAO;AAAA,IACP,QAAQ;AAAA,IACR,MAAM;AAAA,EACR;AACF;AACA,SAAS,iBAAiB,MAAM;AAC9B,SAAO;AAAA,IACL,GAAG;AAAA,IACH,KAAK,KAAK;AAAA,IACV,MAAM,KAAK;AAAA,IACX,OAAO,KAAK,IAAI,KAAK;AAAA,IACrB,QAAQ,KAAK,IAAI,KAAK;AAAA,EACxB;AACF;;;AC3HA,SAAS,2BAA2B,MAAM,WAAW,KAAK;AACxD,MAAI;AAAA,IACF;AAAA,IACA;AAAA,EACF,IAAI;AACJ,QAAM,WAAW,YAAY,SAAS;AACtC,QAAM,gBAAgB,iBAAiB,SAAS;AAChD,QAAM,cAAc,cAAc,aAAa;AAC/C,QAAM,OAAO,QAAQ,SAAS;AAC9B,QAAM,aAAa,aAAa;AAChC,QAAM,UAAU,UAAU,IAAI,UAAU,QAAQ,IAAI,SAAS,QAAQ;AACrE,QAAM,UAAU,UAAU,IAAI,UAAU,SAAS,IAAI,SAAS,SAAS;AACvE,QAAM,cAAc,UAAU,WAAW,IAAI,IAAI,SAAS,WAAW,IAAI;AACzE,MAAI;AACJ,UAAQ,MAAM;AAAA,IACZ,KAAK;AACH,eAAS;AAAA,QACP,GAAG;AAAA,QACH,GAAG,UAAU,IAAI,SAAS;AAAA,MAC5B;AACA;AAAA,IACF,KAAK;AACH,eAAS;AAAA,QACP,GAAG;AAAA,QACH,GAAG,UAAU,IAAI,UAAU;AAAA,MAC7B;AACA;AAAA,IACF,KAAK;AACH,eAAS;AAAA,QACP,GAAG,UAAU,IAAI,UAAU;AAAA,QAC3B,GAAG;AAAA,MACL;AACA;AAAA,IACF,KAAK;AACH,eAAS;AAAA,QACP,GAAG,UAAU,IAAI,SAAS;AAAA,QAC1B,GAAG;AAAA,MACL;AACA;AAAA,IACF;AACE,eAAS;AAAA,QACP,GAAG,UAAU;AAAA,QACb,GAAG,UAAU;AAAA,MACf;AAAA,EACJ;AACA,UAAQ,aAAa,SAAS,GAAG;AAAA,IAC/B,KAAK;AACH,aAAO,aAAa,KAAK,eAAe,OAAO,aAAa,KAAK;AACjE;AAAA,IACF,KAAK;AACH,aAAO,aAAa,KAAK,eAAe,OAAO,aAAa,KAAK;AACjE;AAAA,EACJ;AACA,SAAO;AACT;AASA,IAAM,kBAAkB,OAAO,WAAW,UAAU,WAAW;AAC7D,QAAM;AAAA,IACJ,YAAY;AAAA,IACZ,WAAW;AAAA,IACX,aAAa,CAAC;AAAA,IACd,UAAAE;AAAA,EACF,IAAI;AACJ,QAAM,kBAAkB,WAAW,OAAO,OAAO;AACjD,QAAM,MAAM,OAAOA,UAAS,SAAS,OAAO,SAASA,UAAS,MAAM,QAAQ;AAC5E,MAAI,QAAQ,MAAMA,UAAS,gBAAgB;AAAA,IACzC;AAAA,IACA;AAAA,IACA;AAAA,EACF,CAAC;AACD,MAAI;AAAA,IACF;AAAA,IACA;AAAA,EACF,IAAI,2BAA2B,OAAO,WAAW,GAAG;AACpD,MAAI,oBAAoB;AACxB,MAAI,iBAAiB,CAAC;AACtB,MAAI,aAAa;AACjB,WAAS,IAAI,GAAG,IAAI,gBAAgB,QAAQ,KAAK;AAC/C,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,IACF,IAAI,gBAAgB,CAAC;AACrB,UAAM;AAAA,MACJ,GAAG;AAAA,MACH,GAAG;AAAA,MACH;AAAA,MACA;AAAA,IACF,IAAI,MAAM,GAAG;AAAA,MACX;AAAA,MACA;AAAA,MACA,kBAAkB;AAAA,MAClB,WAAW;AAAA,MACX;AAAA,MACA;AAAA,MACA;AAAA,MACA,UAAAA;AAAA,MACA,UAAU;AAAA,QACR;AAAA,QACA;AAAA,MACF;AAAA,IACF,CAAC;AACD,QAAI,SAAS,OAAO,QAAQ;AAC5B,QAAI,SAAS,OAAO,QAAQ;AAC5B,qBAAiB;AAAA,MACf,GAAG;AAAA,MACH,CAAC,IAAI,GAAG;AAAA,QACN,GAAG,eAAe,IAAI;AAAA,QACtB,GAAG;AAAA,MACL;AAAA,IACF;AACA,QAAI,SAAS,cAAc,IAAI;AAC7B;AACA,UAAI,OAAO,UAAU,UAAU;AAC7B,YAAI,MAAM,WAAW;AACnB,8BAAoB,MAAM;AAAA,QAC5B;AACA,YAAI,MAAM,OAAO;AACf,kBAAQ,MAAM,UAAU,OAAO,MAAMA,UAAS,gBAAgB;AAAA,YAC5D;AAAA,YACA;AAAA,YACA;AAAA,UACF,CAAC,IAAI,MAAM;AAAA,QACb;AACA,SAAC;AAAA,UACC;AAAA,UACA;AAAA,QACF,IAAI,2BAA2B,OAAO,mBAAmB,GAAG;AAAA,MAC9D;AACA,UAAI;AACJ;AAAA,IACF;AAAA,EACF;AACA,SAAO;AAAA,IACL;AAAA,IACA;AAAA,IACA,WAAW;AAAA,IACX;AAAA,IACA;AAAA,EACF;AACF;AAUA,eAAe,eAAe,OAAO,SAAS;AAC5C,MAAI;AACJ,MAAI,YAAY,QAAQ;AACtB,cAAU,CAAC;AAAA,EACb;AACA,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,IACA,UAAAA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,IAAI;AACJ,QAAM;AAAA,IACJ,WAAW;AAAA,IACX,eAAe;AAAA,IACf,iBAAiB;AAAA,IACjB,cAAc;AAAA,IACd,UAAU;AAAA,EACZ,IAAI,SAAS,SAAS,KAAK;AAC3B,QAAM,gBAAgB,iBAAiB,OAAO;AAC9C,QAAM,aAAa,mBAAmB,aAAa,cAAc;AACjE,QAAM,UAAU,SAAS,cAAc,aAAa,cAAc;AAClE,QAAM,qBAAqB,iBAAiB,MAAMA,UAAS,gBAAgB;AAAA,IACzE,WAAW,wBAAwB,OAAOA,UAAS,aAAa,OAAO,SAASA,UAAS,UAAU,OAAO,OAAO,OAAO,wBAAwB,QAAQ,UAAU,QAAQ,kBAAmB,OAAOA,UAAS,sBAAsB,OAAO,SAASA,UAAS,mBAAmB,SAAS,QAAQ;AAAA,IAChS;AAAA,IACA;AAAA,IACA;AAAA,EACF,CAAC,CAAC;AACF,QAAM,OAAO,mBAAmB,aAAa;AAAA,IAC3C,GAAG,MAAM;AAAA,IACT;AAAA,IACA;AAAA,EACF,IAAI,MAAM;AACV,QAAM,eAAe,OAAOA,UAAS,mBAAmB,OAAO,SAASA,UAAS,gBAAgB,SAAS,QAAQ;AAClH,QAAM,cAAe,OAAOA,UAAS,aAAa,OAAO,SAASA,UAAS,UAAU,YAAY,KAAO,OAAOA,UAAS,YAAY,OAAO,SAASA,UAAS,SAAS,YAAY,MAAO;AAAA,IACvL,GAAG;AAAA,IACH,GAAG;AAAA,EACL,IAAI;AAAA,IACF,GAAG;AAAA,IACH,GAAG;AAAA,EACL;AACA,QAAM,oBAAoB,iBAAiBA,UAAS,wDAAwD,MAAMA,UAAS,sDAAsD;AAAA,IAC/K;AAAA,IACA;AAAA,IACA;AAAA,EACF,CAAC,IAAI,IAAI;AACT,SAAO;AAAA,IACL,MAAM,mBAAmB,MAAM,kBAAkB,MAAM,cAAc,OAAO,YAAY;AAAA,IACxF,SAAS,kBAAkB,SAAS,mBAAmB,SAAS,cAAc,UAAU,YAAY;AAAA,IACpG,OAAO,mBAAmB,OAAO,kBAAkB,OAAO,cAAc,QAAQ,YAAY;AAAA,IAC5F,QAAQ,kBAAkB,QAAQ,mBAAmB,QAAQ,cAAc,SAAS,YAAY;AAAA,EAClG;AACF;AAOA,IAAM,QAAQ,cAAY;AAAA,EACxB,MAAM;AAAA,EACN;AAAA,EACA,MAAM,GAAG,OAAO;AACd,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA,UAAAA;AAAA,MACA;AAAA,MACA;AAAA,IACF,IAAI;AAEJ,UAAM;AAAA,MACJ;AAAA,MACA,UAAU;AAAA,IACZ,IAAI,SAAS,SAAS,KAAK,KAAK,CAAC;AACjC,QAAI,WAAW,MAAM;AACnB,aAAO,CAAC;AAAA,IACV;AACA,UAAM,gBAAgB,iBAAiB,OAAO;AAC9C,UAAM,SAAS;AAAA,MACb;AAAA,MACA;AAAA,IACF;AACA,UAAM,OAAO,iBAAiB,SAAS;AACvC,UAAM,SAAS,cAAc,IAAI;AACjC,UAAM,kBAAkB,MAAMA,UAAS,cAAc,OAAO;AAC5D,UAAM,UAAU,SAAS;AACzB,UAAM,UAAU,UAAU,QAAQ;AAClC,UAAM,UAAU,UAAU,WAAW;AACrC,UAAM,aAAa,UAAU,iBAAiB;AAC9C,UAAM,UAAU,MAAM,UAAU,MAAM,IAAI,MAAM,UAAU,IAAI,IAAI,OAAO,IAAI,IAAI,MAAM,SAAS,MAAM;AACtG,UAAM,YAAY,OAAO,IAAI,IAAI,MAAM,UAAU,IAAI;AACrD,UAAM,oBAAoB,OAAOA,UAAS,mBAAmB,OAAO,SAASA,UAAS,gBAAgB,OAAO;AAC7G,QAAI,aAAa,oBAAoB,kBAAkB,UAAU,IAAI;AAGrE,QAAI,CAAC,cAAc,CAAE,OAAOA,UAAS,aAAa,OAAO,SAASA,UAAS,UAAU,iBAAiB,IAAK;AACzG,mBAAa,SAAS,SAAS,UAAU,KAAK,MAAM,SAAS,MAAM;AAAA,IACrE;AACA,UAAM,oBAAoB,UAAU,IAAI,YAAY;AAIpD,UAAM,yBAAyB,aAAa,IAAI,gBAAgB,MAAM,IAAI,IAAI;AAC9E,UAAM,aAAa,IAAI,cAAc,OAAO,GAAG,sBAAsB;AACrE,UAAM,aAAa,IAAI,cAAc,OAAO,GAAG,sBAAsB;AAIrE,UAAM,QAAQ;AACd,UAAMC,OAAM,aAAa,gBAAgB,MAAM,IAAI;AACnD,UAAM,SAAS,aAAa,IAAI,gBAAgB,MAAM,IAAI,IAAI;AAC9D,UAAMC,UAAS,MAAM,OAAO,QAAQD,IAAG;AAMvC,UAAM,kBAAkB,CAAC,eAAe,SAAS,aAAa,SAAS,KAAK,QAAQ,UAAUC,WAAU,MAAM,UAAU,MAAM,IAAI,KAAK,SAAS,QAAQ,aAAa,cAAc,gBAAgB,MAAM,IAAI,IAAI;AACjN,UAAM,kBAAkB,kBAAkB,SAAS,QAAQ,SAAS,QAAQ,SAASD,OAAM;AAC3F,WAAO;AAAA,MACL,CAAC,IAAI,GAAG,OAAO,IAAI,IAAI;AAAA,MACvB,MAAM;AAAA,QACJ,CAAC,IAAI,GAAGC;AAAA,QACR,cAAc,SAASA,UAAS;AAAA,QAChC,GAAI,mBAAmB;AAAA,UACrB;AAAA,QACF;AAAA,MACF;AAAA,MACA,OAAO;AAAA,IACT;AAAA,EACF;AACF;AAEA,SAAS,iBAAiB,WAAW,eAAe,mBAAmB;AACrE,QAAM,qCAAqC,YAAY,CAAC,GAAG,kBAAkB,OAAO,eAAa,aAAa,SAAS,MAAM,SAAS,GAAG,GAAG,kBAAkB,OAAO,eAAa,aAAa,SAAS,MAAM,SAAS,CAAC,IAAI,kBAAkB,OAAO,eAAa,QAAQ,SAAS,MAAM,SAAS;AAClS,SAAO,mCAAmC,OAAO,eAAa;AAC5D,QAAI,WAAW;AACb,aAAO,aAAa,SAAS,MAAM,cAAc,gBAAgB,8BAA8B,SAAS,MAAM,YAAY;AAAA,IAC5H;AACA,WAAO;AAAA,EACT,CAAC;AACH;AAOA,IAAM,gBAAgB,SAAU,SAAS;AACvC,MAAI,YAAY,QAAQ;AACtB,cAAU,CAAC;AAAA,EACb;AACA,SAAO;AAAA,IACL,MAAM;AAAA,IACN;AAAA,IACA,MAAM,GAAG,OAAO;AACd,UAAI,uBAAuB,wBAAwB;AACnD,YAAM;AAAA,QACJ;AAAA,QACA;AAAA,QACA;AAAA,QACA,UAAAF;AAAA,QACA;AAAA,MACF,IAAI;AACJ,YAAM;AAAA,QACJ,YAAY;AAAA,QACZ;AAAA,QACA,oBAAoBG;AAAA,QACpB,gBAAgB;AAAA,QAChB,GAAG;AAAA,MACL,IAAI,SAAS,SAAS,KAAK;AAC3B,YAAM,eAAe,cAAc,UAAa,sBAAsBA,cAAa,iBAAiB,aAAa,MAAM,eAAe,iBAAiB,IAAI;AAC3J,YAAM,WAAW,MAAM,eAAe,OAAO,qBAAqB;AAClE,YAAM,iBAAiB,wBAAwB,eAAe,kBAAkB,OAAO,SAAS,sBAAsB,UAAU;AAChI,YAAM,mBAAmB,aAAa,YAAY;AAClD,UAAI,oBAAoB,MAAM;AAC5B,eAAO,CAAC;AAAA,MACV;AACA,YAAM,iBAAiB,kBAAkB,kBAAkB,OAAO,OAAOH,UAAS,SAAS,OAAO,SAASA,UAAS,MAAM,SAAS,QAAQ,EAAE;AAG7I,UAAI,cAAc,kBAAkB;AAClC,eAAO;AAAA,UACL,OAAO;AAAA,YACL,WAAW,aAAa,CAAC;AAAA,UAC3B;AAAA,QACF;AAAA,MACF;AACA,YAAM,mBAAmB,CAAC,SAAS,QAAQ,gBAAgB,CAAC,GAAG,SAAS,eAAe,CAAC,CAAC,GAAG,SAAS,eAAe,CAAC,CAAC,CAAC;AACvH,YAAM,eAAe,CAAC,KAAM,yBAAyB,eAAe,kBAAkB,OAAO,SAAS,uBAAuB,cAAc,CAAC,GAAI;AAAA,QAC9I,WAAW;AAAA,QACX,WAAW;AAAA,MACb,CAAC;AACD,YAAM,gBAAgB,aAAa,eAAe,CAAC;AAGnD,UAAI,eAAe;AACjB,eAAO;AAAA,UACL,MAAM;AAAA,YACJ,OAAO,eAAe;AAAA,YACtB,WAAW;AAAA,UACb;AAAA,UACA,OAAO;AAAA,YACL,WAAW;AAAA,UACb;AAAA,QACF;AAAA,MACF;AACA,YAAM,8BAA8B,aAAa,IAAI,OAAK;AACxD,cAAMI,aAAY,aAAa,EAAE,SAAS;AAC1C,eAAO,CAAC,EAAE,WAAWA,cAAa;AAAA;AAAA,UAElC,EAAE,UAAU,MAAM,GAAG,CAAC,EAAE,OAAO,CAAC,KAAK,MAAM,MAAM,GAAG,CAAC;AAAA;AAAA;AAAA,UAErD,EAAE,UAAU,CAAC;AAAA,WAAG,EAAE,SAAS;AAAA,MAC7B,CAAC,EAAE,KAAK,CAAC,GAAG,MAAM,EAAE,CAAC,IAAI,EAAE,CAAC,CAAC;AAC7B,YAAM,8BAA8B,4BAA4B,OAAO,OAAK,EAAE,CAAC,EAAE;AAAA,QAAM;AAAA;AAAA;AAAA,QAGvF,aAAa,EAAE,CAAC,CAAC,IAAI,IAAI;AAAA,MAAC,EAAE,MAAM,OAAK,KAAK,CAAC,CAAC;AAC9C,YAAM,mBAAmB,wBAAwB,4BAA4B,CAAC,MAAM,OAAO,SAAS,sBAAsB,CAAC,MAAM,4BAA4B,CAAC,EAAE,CAAC;AACjK,UAAI,mBAAmB,WAAW;AAChC,eAAO;AAAA,UACL,MAAM;AAAA,YACJ,OAAO,eAAe;AAAA,YACtB,WAAW;AAAA,UACb;AAAA,UACA,OAAO;AAAA,YACL,WAAW;AAAA,UACb;AAAA,QACF;AAAA,MACF;AACA,aAAO,CAAC;AAAA,IACV;AAAA,EACF;AACF;AAQA,IAAM,OAAO,SAAU,SAAS;AAC9B,MAAI,YAAY,QAAQ;AACtB,cAAU,CAAC;AAAA,EACb;AACA,SAAO;AAAA,IACL,MAAM;AAAA,IACN;AAAA,IACA,MAAM,GAAG,OAAO;AACd,UAAI,uBAAuB;AAC3B,YAAM;AAAA,QACJ;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA,UAAAJ;AAAA,QACA;AAAA,MACF,IAAI;AACJ,YAAM;AAAA,QACJ,UAAU,gBAAgB;AAAA,QAC1B,WAAW,iBAAiB;AAAA,QAC5B,oBAAoB;AAAA,QACpB,mBAAmB;AAAA,QACnB,4BAA4B;AAAA,QAC5B,gBAAgB;AAAA,QAChB,GAAG;AAAA,MACL,IAAI,SAAS,SAAS,KAAK;AAM3B,WAAK,wBAAwB,eAAe,UAAU,QAAQ,sBAAsB,iBAAiB;AACnG,eAAO,CAAC;AAAA,MACV;AACA,YAAM,OAAO,QAAQ,SAAS;AAC9B,YAAM,kBAAkB,QAAQ,gBAAgB,MAAM;AACtD,YAAM,MAAM,OAAOA,UAAS,SAAS,OAAO,SAASA,UAAS,MAAM,SAAS,QAAQ;AACrF,YAAM,qBAAqB,gCAAgC,mBAAmB,CAAC,gBAAgB,CAAC,qBAAqB,gBAAgB,CAAC,IAAI,sBAAsB,gBAAgB;AAChL,UAAI,CAAC,+BAA+B,8BAA8B,QAAQ;AACxE,2BAAmB,KAAK,GAAG,0BAA0B,kBAAkB,eAAe,2BAA2B,GAAG,CAAC;AAAA,MACvH;AACA,YAAMG,cAAa,CAAC,kBAAkB,GAAG,kBAAkB;AAC3D,YAAM,WAAW,MAAM,eAAe,OAAO,qBAAqB;AAClE,YAAM,YAAY,CAAC;AACnB,UAAI,kBAAkB,uBAAuB,eAAe,SAAS,OAAO,SAAS,qBAAqB,cAAc,CAAC;AACzH,UAAI,eAAe;AACjB,kBAAU,KAAK,SAAS,IAAI,CAAC;AAAA,MAC/B;AACA,UAAI,gBAAgB;AAClB,cAAME,SAAQ,kBAAkB,WAAW,OAAO,GAAG;AACrD,kBAAU,KAAK,SAASA,OAAM,CAAC,CAAC,GAAG,SAASA,OAAM,CAAC,CAAC,CAAC;AAAA,MACvD;AACA,sBAAgB,CAAC,GAAG,eAAe;AAAA,QACjC;AAAA,QACA;AAAA,MACF,CAAC;AAGD,UAAI,CAAC,UAAU,MAAM,CAAAC,UAAQA,SAAQ,CAAC,GAAG;AACvC,YAAI,uBAAuB;AAC3B,cAAM,eAAe,wBAAwB,eAAe,SAAS,OAAO,SAAS,sBAAsB,UAAU,KAAK;AAC1H,cAAM,gBAAgBH,YAAW,SAAS;AAC1C,YAAI,eAAe;AAEjB,iBAAO;AAAA,YACL,MAAM;AAAA,cACJ,OAAO;AAAA,cACP,WAAW;AAAA,YACb;AAAA,YACA,OAAO;AAAA,cACL,WAAW;AAAA,YACb;AAAA,UACF;AAAA,QACF;AAIA,YAAI,kBAAkB,wBAAwB,cAAc,OAAO,OAAK,EAAE,UAAU,CAAC,KAAK,CAAC,EAAE,KAAK,CAAC,GAAG,MAAM,EAAE,UAAU,CAAC,IAAI,EAAE,UAAU,CAAC,CAAC,EAAE,CAAC,MAAM,OAAO,SAAS,sBAAsB;AAG1L,YAAI,CAAC,gBAAgB;AACnB,kBAAQ,kBAAkB;AAAA,YACxB,KAAK,WACH;AACE,kBAAI;AACJ,oBAAMI,cAAa,wBAAwB,cAAc,IAAI,OAAK,CAAC,EAAE,WAAW,EAAE,UAAU,OAAO,CAAAC,cAAYA,YAAW,CAAC,EAAE,OAAO,CAAC,KAAKA,cAAa,MAAMA,WAAU,CAAC,CAAC,CAAC,EAAE,KAAK,CAAC,GAAG,MAAM,EAAE,CAAC,IAAI,EAAE,CAAC,CAAC,EAAE,CAAC,MAAM,OAAO,SAAS,sBAAsB,CAAC;AACtP,kBAAID,YAAW;AACb,iCAAiBA;AAAA,cACnB;AACA;AAAA,YACF;AAAA,YACF,KAAK;AACH,+BAAiB;AACjB;AAAA,UACJ;AAAA,QACF;AACA,YAAI,cAAc,gBAAgB;AAChC,iBAAO;AAAA,YACL,OAAO;AAAA,cACL,WAAW;AAAA,YACb;AAAA,UACF;AAAA,QACF;AAAA,MACF;AACA,aAAO,CAAC;AAAA,IACV;AAAA,EACF;AACF;AAEA,SAAS,eAAe,UAAU,MAAM;AACtC,SAAO;AAAA,IACL,KAAK,SAAS,MAAM,KAAK;AAAA,IACzB,OAAO,SAAS,QAAQ,KAAK;AAAA,IAC7B,QAAQ,SAAS,SAAS,KAAK;AAAA,IAC/B,MAAM,SAAS,OAAO,KAAK;AAAA,EAC7B;AACF;AACA,SAAS,sBAAsB,UAAU;AACvC,SAAOF,OAAM,KAAK,UAAQ,SAAS,IAAI,KAAK,CAAC;AAC/C;AAMA,IAAM,OAAO,SAAU,SAAS;AAC9B,MAAI,YAAY,QAAQ;AACtB,cAAU,CAAC;AAAA,EACb;AACA,SAAO;AAAA,IACL,MAAM;AAAA,IACN;AAAA,IACA,MAAM,GAAG,OAAO;AACd,YAAM;AAAA,QACJ;AAAA,MACF,IAAI;AACJ,YAAM;AAAA,QACJ,WAAW;AAAA,QACX,GAAG;AAAA,MACL,IAAI,SAAS,SAAS,KAAK;AAC3B,cAAQ,UAAU;AAAA,QAChB,KAAK,mBACH;AACE,gBAAM,WAAW,MAAM,eAAe,OAAO;AAAA,YAC3C,GAAG;AAAA,YACH,gBAAgB;AAAA,UAClB,CAAC;AACD,gBAAM,UAAU,eAAe,UAAU,MAAM,SAAS;AACxD,iBAAO;AAAA,YACL,MAAM;AAAA,cACJ,wBAAwB;AAAA,cACxB,iBAAiB,sBAAsB,OAAO;AAAA,YAChD;AAAA,UACF;AAAA,QACF;AAAA,QACF,KAAK,WACH;AACE,gBAAM,WAAW,MAAM,eAAe,OAAO;AAAA,YAC3C,GAAG;AAAA,YACH,aAAa;AAAA,UACf,CAAC;AACD,gBAAM,UAAU,eAAe,UAAU,MAAM,QAAQ;AACvD,iBAAO;AAAA,YACL,MAAM;AAAA,cACJ,gBAAgB;AAAA,cAChB,SAAS,sBAAsB,OAAO;AAAA,YACxC;AAAA,UACF;AAAA,QACF;AAAA,QACF,SACE;AACE,iBAAO,CAAC;AAAA,QACV;AAAA,MACJ;AAAA,IACF;AAAA,EACF;AACF;AAEA,SAAS,gBAAgB,OAAO;AAC9B,QAAM,OAAO,IAAI,GAAG,MAAM,IAAI,UAAQ,KAAK,IAAI,CAAC;AAChD,QAAM,OAAO,IAAI,GAAG,MAAM,IAAI,UAAQ,KAAK,GAAG,CAAC;AAC/C,QAAM,OAAO,IAAI,GAAG,MAAM,IAAI,UAAQ,KAAK,KAAK,CAAC;AACjD,QAAM,OAAO,IAAI,GAAG,MAAM,IAAI,UAAQ,KAAK,MAAM,CAAC;AAClD,SAAO;AAAA,IACL,GAAG;AAAA,IACH,GAAG;AAAA,IACH,OAAO,OAAO;AAAA,IACd,QAAQ,OAAO;AAAA,EACjB;AACF;AACA,SAAS,eAAe,OAAO;AAC7B,QAAM,cAAc,MAAM,MAAM,EAAE,KAAK,CAAC,GAAG,MAAM,EAAE,IAAI,EAAE,CAAC;AAC1D,QAAM,SAAS,CAAC;AAChB,MAAI,WAAW;AACf,WAAS,IAAI,GAAG,IAAI,YAAY,QAAQ,KAAK;AAC3C,UAAM,OAAO,YAAY,CAAC;AAC1B,QAAI,CAAC,YAAY,KAAK,IAAI,SAAS,IAAI,SAAS,SAAS,GAAG;AAC1D,aAAO,KAAK,CAAC,IAAI,CAAC;AAAA,IACpB,OAAO;AACL,aAAO,OAAO,SAAS,CAAC,EAAE,KAAK,IAAI;AAAA,IACrC;AACA,eAAW;AAAA,EACb;AACA,SAAO,OAAO,IAAI,UAAQ,iBAAiB,gBAAgB,IAAI,CAAC,CAAC;AACnE;AAMA,IAAM,SAAS,SAAU,SAAS;AAChC,MAAI,YAAY,QAAQ;AACtB,cAAU,CAAC;AAAA,EACb;AACA,SAAO;AAAA,IACL,MAAM;AAAA,IACN;AAAA,IACA,MAAM,GAAG,OAAO;AACd,YAAM;AAAA,QACJ;AAAA,QACA;AAAA,QACA;AAAA,QACA,UAAAL;AAAA,QACA;AAAA,MACF,IAAI;AAIJ,YAAM;AAAA,QACJ,UAAU;AAAA,QACV;AAAA,QACA;AAAA,MACF,IAAI,SAAS,SAAS,KAAK;AAC3B,YAAM,oBAAoB,MAAM,KAAM,OAAOA,UAAS,kBAAkB,OAAO,SAASA,UAAS,eAAe,SAAS,SAAS,MAAO,CAAC,CAAC;AAC3I,YAAM,cAAc,eAAe,iBAAiB;AACpD,YAAM,WAAW,iBAAiB,gBAAgB,iBAAiB,CAAC;AACpE,YAAM,gBAAgB,iBAAiB,OAAO;AAC9C,eAASS,yBAAwB;AAE/B,YAAI,YAAY,WAAW,KAAK,YAAY,CAAC,EAAE,OAAO,YAAY,CAAC,EAAE,SAAS,KAAK,QAAQ,KAAK,MAAM;AAEpG,iBAAO,YAAY,KAAK,UAAQ,IAAI,KAAK,OAAO,cAAc,QAAQ,IAAI,KAAK,QAAQ,cAAc,SAAS,IAAI,KAAK,MAAM,cAAc,OAAO,IAAI,KAAK,SAAS,cAAc,MAAM,KAAK;AAAA,QAC/L;AAGA,YAAI,YAAY,UAAU,GAAG;AAC3B,cAAI,YAAY,SAAS,MAAM,KAAK;AAClC,kBAAM,YAAY,YAAY,CAAC;AAC/B,kBAAM,WAAW,YAAY,YAAY,SAAS,CAAC;AACnD,kBAAM,QAAQ,QAAQ,SAAS,MAAM;AACrC,kBAAMC,OAAM,UAAU;AACtB,kBAAMC,UAAS,SAAS;AACxB,kBAAMC,QAAO,QAAQ,UAAU,OAAO,SAAS;AAC/C,kBAAMC,SAAQ,QAAQ,UAAU,QAAQ,SAAS;AACjD,kBAAMC,SAAQD,SAAQD;AACtB,kBAAMG,UAASJ,UAASD;AACxB,mBAAO;AAAA,cACL,KAAAA;AAAA,cACA,QAAAC;AAAA,cACA,MAAAC;AAAA,cACA,OAAAC;AAAA,cACA,OAAAC;AAAA,cACA,QAAAC;AAAA,cACA,GAAGH;AAAA,cACH,GAAGF;AAAA,YACL;AAAA,UACF;AACA,gBAAM,aAAa,QAAQ,SAAS,MAAM;AAC1C,gBAAM,WAAW,IAAI,GAAG,YAAY,IAAI,UAAQ,KAAK,KAAK,CAAC;AAC3D,gBAAM,UAAU,IAAI,GAAG,YAAY,IAAI,UAAQ,KAAK,IAAI,CAAC;AACzD,gBAAM,eAAe,YAAY,OAAO,UAAQ,aAAa,KAAK,SAAS,UAAU,KAAK,UAAU,QAAQ;AAC5G,gBAAM,MAAM,aAAa,CAAC,EAAE;AAC5B,gBAAM,SAAS,aAAa,aAAa,SAAS,CAAC,EAAE;AACrD,gBAAM,OAAO;AACb,gBAAM,QAAQ;AACd,gBAAM,QAAQ,QAAQ;AACtB,gBAAM,SAAS,SAAS;AACxB,iBAAO;AAAA,YACL;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,YACA,GAAG;AAAA,YACH,GAAG;AAAA,UACL;AAAA,QACF;AACA,eAAO;AAAA,MACT;AACA,YAAM,aAAa,MAAMV,UAAS,gBAAgB;AAAA,QAChD,WAAW;AAAA,UACT,uBAAAS;AAAA,QACF;AAAA,QACA,UAAU,SAAS;AAAA,QACnB;AAAA,MACF,CAAC;AACD,UAAI,MAAM,UAAU,MAAM,WAAW,UAAU,KAAK,MAAM,UAAU,MAAM,WAAW,UAAU,KAAK,MAAM,UAAU,UAAU,WAAW,UAAU,SAAS,MAAM,UAAU,WAAW,WAAW,UAAU,QAAQ;AAClN,eAAO;AAAA,UACL,OAAO;AAAA,YACL,OAAO;AAAA,UACT;AAAA,QACF;AAAA,MACF;AACA,aAAO,CAAC;AAAA,IACV;AAAA,EACF;AACF;AAKA,eAAe,qBAAqB,OAAO,SAAS;AAClD,QAAM;AAAA,IACJ;AAAA,IACA,UAAAT;AAAA,IACA;AAAA,EACF,IAAI;AACJ,QAAM,MAAM,OAAOA,UAAS,SAAS,OAAO,SAASA,UAAS,MAAM,SAAS,QAAQ;AACrF,QAAM,OAAO,QAAQ,SAAS;AAC9B,QAAM,YAAY,aAAa,SAAS;AACxC,QAAM,aAAa,YAAY,SAAS,MAAM;AAC9C,QAAM,gBAAgB,CAAC,QAAQ,KAAK,EAAE,SAAS,IAAI,IAAI,KAAK;AAC5D,QAAM,iBAAiB,OAAO,aAAa,KAAK;AAChD,QAAM,WAAW,SAAS,SAAS,KAAK;AAGxC,MAAI;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,EACF,IAAI,OAAO,aAAa,WAAW;AAAA,IACjC,UAAU;AAAA,IACV,WAAW;AAAA,IACX,eAAe;AAAA,EACjB,IAAI;AAAA,IACF,UAAU;AAAA,IACV,WAAW;AAAA,IACX,eAAe;AAAA,IACf,GAAG;AAAA,EACL;AACA,MAAI,aAAa,OAAO,kBAAkB,UAAU;AAClD,gBAAY,cAAc,QAAQ,gBAAgB,KAAK;AAAA,EACzD;AACA,SAAO,aAAa;AAAA,IAClB,GAAG,YAAY;AAAA,IACf,GAAG,WAAW;AAAA,EAChB,IAAI;AAAA,IACF,GAAG,WAAW;AAAA,IACd,GAAG,YAAY;AAAA,EACjB;AACF;AASA,IAAM,SAAS,SAAU,SAAS;AAChC,MAAI,YAAY,QAAQ;AACtB,cAAU;AAAA,EACZ;AACA,SAAO;AAAA,IACL,MAAM;AAAA,IACN;AAAA,IACA,MAAM,GAAG,OAAO;AACd,UAAI,uBAAuB;AAC3B,YAAM;AAAA,QACJ;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF,IAAI;AACJ,YAAM,aAAa,MAAM,qBAAqB,OAAO,OAAO;AAI5D,UAAI,gBAAgB,wBAAwB,eAAe,WAAW,OAAO,SAAS,sBAAsB,eAAe,wBAAwB,eAAe,UAAU,QAAQ,sBAAsB,iBAAiB;AACzN,eAAO,CAAC;AAAA,MACV;AACA,aAAO;AAAA,QACL,GAAG,IAAI,WAAW;AAAA,QAClB,GAAG,IAAI,WAAW;AAAA,QAClB,MAAM;AAAA,UACJ,GAAG;AAAA,UACH;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAAA,EACF;AACF;AAOA,IAAM,QAAQ,SAAU,SAAS;AAC/B,MAAI,YAAY,QAAQ;AACtB,cAAU,CAAC;AAAA,EACb;AACA,SAAO;AAAA,IACL,MAAM;AAAA,IACN;AAAA,IACA,MAAM,GAAG,OAAO;AACd,YAAM;AAAA,QACJ;AAAA,QACA;AAAA,QACA;AAAA,MACF,IAAI;AACJ,YAAM;AAAA,QACJ,UAAU,gBAAgB;AAAA,QAC1B,WAAW,iBAAiB;AAAA,QAC5B,UAAU;AAAA,UACR,IAAI,UAAQ;AACV,gBAAI;AAAA,cACF,GAAAgB;AAAA,cACA,GAAAC;AAAA,YACF,IAAI;AACJ,mBAAO;AAAA,cACL,GAAAD;AAAA,cACA,GAAAC;AAAA,YACF;AAAA,UACF;AAAA,QACF;AAAA,QACA,GAAG;AAAA,MACL,IAAI,SAAS,SAAS,KAAK;AAC3B,YAAM,SAAS;AAAA,QACb;AAAA,QACA;AAAA,MACF;AACA,YAAM,WAAW,MAAM,eAAe,OAAO,qBAAqB;AAClE,YAAM,YAAY,YAAY,QAAQ,SAAS,CAAC;AAChD,YAAM,WAAW,gBAAgB,SAAS;AAC1C,UAAI,gBAAgB,OAAO,QAAQ;AACnC,UAAI,iBAAiB,OAAO,SAAS;AACrC,UAAI,eAAe;AACjB,cAAM,UAAU,aAAa,MAAM,QAAQ;AAC3C,cAAM,UAAU,aAAa,MAAM,WAAW;AAC9C,cAAMC,OAAM,gBAAgB,SAAS,OAAO;AAC5C,cAAMjB,OAAM,gBAAgB,SAAS,OAAO;AAC5C,wBAAgB,MAAMiB,MAAK,eAAejB,IAAG;AAAA,MAC/C;AACA,UAAI,gBAAgB;AAClB,cAAM,UAAU,cAAc,MAAM,QAAQ;AAC5C,cAAM,UAAU,cAAc,MAAM,WAAW;AAC/C,cAAMiB,OAAM,iBAAiB,SAAS,OAAO;AAC7C,cAAMjB,OAAM,iBAAiB,SAAS,OAAO;AAC7C,yBAAiB,MAAMiB,MAAK,gBAAgBjB,IAAG;AAAA,MACjD;AACA,YAAM,gBAAgB,QAAQ,GAAG;AAAA,QAC/B,GAAG;AAAA,QACH,CAAC,QAAQ,GAAG;AAAA,QACZ,CAAC,SAAS,GAAG;AAAA,MACf,CAAC;AACD,aAAO;AAAA,QACL,GAAG;AAAA,QACH,MAAM;AAAA,UACJ,GAAG,cAAc,IAAI;AAAA,UACrB,GAAG,cAAc,IAAI;AAAA,QACvB;AAAA,MACF;AAAA,IACF;AAAA,EACF;AACF;AAIA,IAAM,aAAa,SAAU,SAAS;AACpC,MAAI,YAAY,QAAQ;AACtB,cAAU,CAAC;AAAA,EACb;AACA,SAAO;AAAA,IACL;AAAA,IACA,GAAG,OAAO;AACR,YAAM;AAAA,QACJ;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF,IAAI;AACJ,YAAM;AAAA,QACJ,QAAAC,UAAS;AAAA,QACT,UAAU,gBAAgB;AAAA,QAC1B,WAAW,iBAAiB;AAAA,MAC9B,IAAI,SAAS,SAAS,KAAK;AAC3B,YAAM,SAAS;AAAA,QACb;AAAA,QACA;AAAA,MACF;AACA,YAAM,YAAY,YAAY,SAAS;AACvC,YAAM,WAAW,gBAAgB,SAAS;AAC1C,UAAI,gBAAgB,OAAO,QAAQ;AACnC,UAAI,iBAAiB,OAAO,SAAS;AACrC,YAAM,YAAY,SAASA,SAAQ,KAAK;AACxC,YAAM,iBAAiB,OAAO,cAAc,WAAW;AAAA,QACrD,UAAU;AAAA,QACV,WAAW;AAAA,MACb,IAAI;AAAA,QACF,UAAU;AAAA,QACV,WAAW;AAAA,QACX,GAAG;AAAA,MACL;AACA,UAAI,eAAe;AACjB,cAAM,MAAM,aAAa,MAAM,WAAW;AAC1C,cAAM,WAAW,MAAM,UAAU,QAAQ,IAAI,MAAM,SAAS,GAAG,IAAI,eAAe;AAClF,cAAM,WAAW,MAAM,UAAU,QAAQ,IAAI,MAAM,UAAU,GAAG,IAAI,eAAe;AACnF,YAAI,gBAAgB,UAAU;AAC5B,0BAAgB;AAAA,QAClB,WAAW,gBAAgB,UAAU;AACnC,0BAAgB;AAAA,QAClB;AAAA,MACF;AACA,UAAI,gBAAgB;AAClB,YAAI,uBAAuB;AAC3B,cAAM,MAAM,aAAa,MAAM,UAAU;AACzC,cAAM,eAAe,CAAC,OAAO,MAAM,EAAE,SAAS,QAAQ,SAAS,CAAC;AAChE,cAAM,WAAW,MAAM,UAAU,SAAS,IAAI,MAAM,SAAS,GAAG,KAAK,iBAAiB,wBAAwB,eAAe,WAAW,OAAO,SAAS,sBAAsB,SAAS,MAAM,IAAI,MAAM,eAAe,IAAI,eAAe;AACzO,cAAM,WAAW,MAAM,UAAU,SAAS,IAAI,MAAM,UAAU,GAAG,KAAK,eAAe,MAAM,yBAAyB,eAAe,WAAW,OAAO,SAAS,uBAAuB,SAAS,MAAM,MAAM,eAAe,eAAe,YAAY;AACpP,YAAI,iBAAiB,UAAU;AAC7B,2BAAiB;AAAA,QACnB,WAAW,iBAAiB,UAAU;AACpC,2BAAiB;AAAA,QACnB;AAAA,MACF;AACA,aAAO;AAAA,QACL,CAAC,QAAQ,GAAG;AAAA,QACZ,CAAC,SAAS,GAAG;AAAA,MACf;AAAA,IACF;AAAA,EACF;AACF;AAQA,IAAM,OAAO,SAAU,SAAS;AAC9B,MAAI,YAAY,QAAQ;AACtB,cAAU,CAAC;AAAA,EACb;AACA,SAAO;AAAA,IACL,MAAM;AAAA,IACN;AAAA,IACA,MAAM,GAAG,OAAO;AACd,YAAM;AAAA,QACJ;AAAA,QACA;AAAA,QACA,UAAAF;AAAA,QACA;AAAA,MACF,IAAI;AACJ,YAAM;AAAA,QACJ,QAAQ,MAAM;AAAA,QAAC;AAAA,QACf,GAAG;AAAA,MACL,IAAI,SAAS,SAAS,KAAK;AAC3B,YAAM,WAAW,MAAM,eAAe,OAAO,qBAAqB;AAClE,YAAM,OAAO,QAAQ,SAAS;AAC9B,YAAM,YAAY,aAAa,SAAS;AACxC,YAAM,UAAU,YAAY,SAAS,MAAM;AAC3C,YAAM;AAAA,QACJ;AAAA,QACA;AAAA,MACF,IAAI,MAAM;AACV,UAAI;AACJ,UAAI;AACJ,UAAI,SAAS,SAAS,SAAS,UAAU;AACvC,qBAAa;AACb,oBAAY,eAAgB,OAAOA,UAAS,SAAS,OAAO,SAASA,UAAS,MAAM,SAAS,QAAQ,KAAM,UAAU,SAAS,SAAS;AAAA,MACzI,OAAO;AACL,oBAAY;AACZ,qBAAa,cAAc,QAAQ,QAAQ;AAAA,MAC7C;AACA,YAAM,0BAA0B,SAAS,SAAS,UAAU;AAC5D,YAAM,yBAAyB,QAAQ,SAAS,SAAS;AACzD,YAAM,UAAU,CAAC,MAAM,eAAe;AACtC,UAAI,kBAAkB;AACtB,UAAI,iBAAiB;AACrB,UAAI,SAAS;AACX,cAAM,uBAAuB,QAAQ,SAAS,OAAO,SAAS;AAC9D,yBAAiB,aAAa,UAAU,IAAI,wBAAwB,oBAAoB,IAAI;AAAA,MAC9F,OAAO;AACL,cAAM,wBAAwB,SAAS,SAAS,MAAM,SAAS;AAC/D,0BAAkB,aAAa,UAAU,IAAI,yBAAyB,qBAAqB,IAAI;AAAA,MACjG;AACA,UAAI,WAAW,CAAC,WAAW;AACzB,cAAM,OAAO,IAAI,SAAS,MAAM,CAAC;AACjC,cAAM,OAAO,IAAI,SAAS,OAAO,CAAC;AAClC,cAAM,OAAO,IAAI,SAAS,KAAK,CAAC;AAChC,cAAM,OAAO,IAAI,SAAS,QAAQ,CAAC;AACnC,YAAI,SAAS;AACX,2BAAiB,QAAQ,KAAK,SAAS,KAAK,SAAS,IAAI,OAAO,OAAO,IAAI,SAAS,MAAM,SAAS,KAAK;AAAA,QAC1G,OAAO;AACL,4BAAkB,SAAS,KAAK,SAAS,KAAK,SAAS,IAAI,OAAO,OAAO,IAAI,SAAS,KAAK,SAAS,MAAM;AAAA,QAC5G;AAAA,MACF;AACA,YAAM,MAAM;AAAA,QACV,GAAG;AAAA,QACH;AAAA,QACA;AAAA,MACF,CAAC;AACD,YAAM,iBAAiB,MAAMA,UAAS,cAAc,SAAS,QAAQ;AACrE,UAAI,UAAU,eAAe,SAAS,WAAW,eAAe,QAAQ;AACtE,eAAO;AAAA,UACL,OAAO;AAAA,YACL,OAAO;AAAA,UACT;AAAA,QACF;AAAA,MACF;AACA,aAAO,CAAC;AAAA,IACV;AAAA,EACF;AACF;;;ACx/BA,IAAMmB,SAAQ,CAAC,OAAO,SAAS,UAAU,MAAM;AAC/C,IAAMC,cAAa,CAAC,SAAS,KAAK;AAClC,IAAMC,cAA0BF,OAAM,OAAO,CAAC,KAAK,SAAS,IAAI,OAAO,MAAM,OAAO,MAAMC,YAAW,CAAC,GAAG,OAAO,MAAMA,YAAW,CAAC,CAAC,GAAG,CAAC,CAAC;AACxI,IAAME,OAAM,KAAK;AACjB,IAAMC,OAAM,KAAK;AACjB,IAAM,QAAQ,KAAK;AACnB,IAAMC,SAAQ,KAAK;AACnB,IAAM,eAAe,QAAM;AAAA,EACzB,GAAG;AAAA,EACH,GAAG;AACL;;;ACfA,SAASC,aAAY,MAAM;AACzB,MAAIC,QAAO,IAAI,GAAG;AAChB,YAAQ,KAAK,YAAY,IAAI,YAAY;AAAA,EAC3C;AAIA,SAAO;AACT;AACA,SAASC,WAAU,MAAM;AACvB,MAAI;AACJ,UAAQ,QAAQ,SAAS,sBAAsB,KAAK,kBAAkB,OAAO,SAAS,oBAAoB,gBAAgB;AAC5H;AACA,SAASC,oBAAmB,MAAM;AAChC,MAAI;AACJ,UAAQ,QAAQF,QAAO,IAAI,IAAI,KAAK,gBAAgB,KAAK,aAAa,OAAO,aAAa,OAAO,SAAS,KAAK;AACjH;AACA,SAASA,QAAO,OAAO;AACrB,SAAO,iBAAiB,QAAQ,iBAAiBC,WAAU,KAAK,EAAE;AACpE;AACA,SAASE,WAAU,OAAO;AACxB,SAAO,iBAAiB,WAAW,iBAAiBF,WAAU,KAAK,EAAE;AACvE;AACA,SAASG,eAAc,OAAO;AAC5B,SAAO,iBAAiB,eAAe,iBAAiBH,WAAU,KAAK,EAAE;AAC3E;AACA,SAASI,cAAa,OAAO;AAE3B,MAAI,OAAO,eAAe,aAAa;AACrC,WAAO;AAAA,EACT;AACA,SAAO,iBAAiB,cAAc,iBAAiBJ,WAAU,KAAK,EAAE;AAC1E;AACA,SAAS,kBAAkB,SAAS;AAClC,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,IAAIK,kBAAiB,OAAO;AAC5B,SAAO,kCAAkC,KAAK,WAAW,YAAY,SAAS,KAAK,CAAC,CAAC,UAAU,UAAU,EAAE,SAAS,OAAO;AAC7H;AACA,SAAS,eAAe,SAAS;AAC/B,SAAO,CAAC,SAAS,MAAM,IAAI,EAAE,SAASP,aAAY,OAAO,CAAC;AAC5D;AACA,SAAS,kBAAkB,SAAS;AAClC,QAAM,SAAS,SAAS;AACxB,QAAM,MAAMO,kBAAiB,OAAO;AAGpC,SAAO,IAAI,cAAc,UAAU,IAAI,gBAAgB,WAAW,IAAI,gBAAgB,IAAI,kBAAkB,WAAW,UAAU,CAAC,WAAW,IAAI,iBAAiB,IAAI,mBAAmB,SAAS,UAAU,CAAC,WAAW,IAAI,SAAS,IAAI,WAAW,SAAS,UAAU,CAAC,aAAa,eAAe,QAAQ,EAAE,KAAK,YAAU,IAAI,cAAc,IAAI,SAAS,KAAK,CAAC,KAAK,CAAC,SAAS,UAAU,UAAU,SAAS,EAAE,KAAK,YAAU,IAAI,WAAW,IAAI,SAAS,KAAK,CAAC;AACnc;AACA,SAAS,mBAAmB,SAAS;AACnC,MAAI,cAAcC,eAAc,OAAO;AACvC,SAAOH,eAAc,WAAW,KAAK,CAACI,uBAAsB,WAAW,GAAG;AACxE,QAAI,kBAAkB,WAAW,GAAG;AAClC,aAAO;AAAA,IACT,OAAO;AACL,oBAAcD,eAAc,WAAW;AAAA,IACzC;AAAA,EACF;AACA,SAAO;AACT;AACA,SAAS,WAAW;AAClB,MAAI,OAAO,QAAQ,eAAe,CAAC,IAAI;AAAU,WAAO;AACxD,SAAO,IAAI,SAAS,2BAA2B,MAAM;AACvD;AACA,SAASC,uBAAsB,MAAM;AACnC,SAAO,CAAC,QAAQ,QAAQ,WAAW,EAAE,SAAST,aAAY,IAAI,CAAC;AACjE;AACA,SAASO,kBAAiB,SAAS;AACjC,SAAOL,WAAU,OAAO,EAAE,iBAAiB,OAAO;AACpD;AACA,SAAS,cAAc,SAAS;AAC9B,MAAIE,WAAU,OAAO,GAAG;AACtB,WAAO;AAAA,MACL,YAAY,QAAQ;AAAA,MACpB,WAAW,QAAQ;AAAA,IACrB;AAAA,EACF;AACA,SAAO;AAAA,IACL,YAAY,QAAQ;AAAA,IACpB,WAAW,QAAQ;AAAA,EACrB;AACF;AACA,SAASI,eAAc,MAAM;AAC3B,MAAIR,aAAY,IAAI,MAAM,QAAQ;AAChC,WAAO;AAAA,EACT;AACA,QAAM;AAAA;AAAA,IAEN,KAAK;AAAA,IAEL,KAAK;AAAA,IAELM,cAAa,IAAI,KAAK,KAAK;AAAA,IAE3BH,oBAAmB,IAAI;AAAA;AACvB,SAAOG,cAAa,MAAM,IAAI,OAAO,OAAO;AAC9C;AACA,SAAS,2BAA2B,MAAM;AACxC,QAAM,aAAaE,eAAc,IAAI;AACrC,MAAIC,uBAAsB,UAAU,GAAG;AACrC,WAAO,KAAK,gBAAgB,KAAK,cAAc,OAAO,KAAK;AAAA,EAC7D;AACA,MAAIJ,eAAc,UAAU,KAAK,kBAAkB,UAAU,GAAG;AAC9D,WAAO;AAAA,EACT;AACA,SAAO,2BAA2B,UAAU;AAC9C;AACA,SAAS,qBAAqB,MAAM,MAAM,iBAAiB;AACzD,MAAI;AACJ,MAAI,SAAS,QAAQ;AACnB,WAAO,CAAC;AAAA,EACV;AACA,MAAI,oBAAoB,QAAQ;AAC9B,sBAAkB;AAAA,EACpB;AACA,QAAM,qBAAqB,2BAA2B,IAAI;AAC1D,QAAM,SAAS,yBAAyB,uBAAuB,KAAK,kBAAkB,OAAO,SAAS,qBAAqB;AAC3H,QAAM,MAAMH,WAAU,kBAAkB;AACxC,MAAI,QAAQ;AACV,WAAO,KAAK,OAAO,KAAK,IAAI,kBAAkB,CAAC,GAAG,kBAAkB,kBAAkB,IAAI,qBAAqB,CAAC,GAAG,IAAI,gBAAgB,kBAAkB,qBAAqB,IAAI,YAAY,IAAI,CAAC,CAAC;AAAA,EACtM;AACA,SAAO,KAAK,OAAO,oBAAoB,qBAAqB,oBAAoB,CAAC,GAAG,eAAe,CAAC;AACtG;;;ACvHA,SAAS,iBAAiB,SAAS;AACjC,QAAM,MAAMQ,kBAAiB,OAAO;AAGpC,MAAI,QAAQ,WAAW,IAAI,KAAK,KAAK;AACrC,MAAI,SAAS,WAAW,IAAI,MAAM,KAAK;AACvC,QAAM,YAAYC,eAAc,OAAO;AACvC,QAAM,cAAc,YAAY,QAAQ,cAAc;AACtD,QAAM,eAAe,YAAY,QAAQ,eAAe;AACxD,QAAM,iBAAiB,MAAM,KAAK,MAAM,eAAe,MAAM,MAAM,MAAM;AACzE,MAAI,gBAAgB;AAClB,YAAQ;AACR,aAAS;AAAA,EACX;AACA,SAAO;AAAA,IACL;AAAA,IACA;AAAA,IACA,GAAG;AAAA,EACL;AACF;AAEA,SAAS,cAAc,SAAS;AAC9B,SAAO,CAACC,WAAU,OAAO,IAAI,QAAQ,iBAAiB;AACxD;AAEA,SAAS,SAAS,SAAS;AACzB,QAAM,aAAa,cAAc,OAAO;AACxC,MAAI,CAACD,eAAc,UAAU,GAAG;AAC9B,WAAO,aAAa,CAAC;AAAA,EACvB;AACA,QAAM,OAAO,WAAW,sBAAsB;AAC9C,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,IACA;AAAA,EACF,IAAI,iBAAiB,UAAU;AAC/B,MAAI,KAAK,IAAI,MAAM,KAAK,KAAK,IAAI,KAAK,SAAS;AAC/C,MAAI,KAAK,IAAI,MAAM,KAAK,MAAM,IAAI,KAAK,UAAU;AAIjD,MAAI,CAAC,KAAK,CAAC,OAAO,SAAS,CAAC,GAAG;AAC7B,QAAI;AAAA,EACN;AACA,MAAI,CAAC,KAAK,CAAC,OAAO,SAAS,CAAC,GAAG;AAC7B,QAAI;AAAA,EACN;AACA,SAAO;AAAA,IACL;AAAA,IACA;AAAA,EACF;AACF;AAEA,IAAM,YAAyB,aAAa,CAAC;AAC7C,SAAS,iBAAiB,SAAS;AACjC,QAAM,MAAME,WAAU,OAAO;AAC7B,MAAI,CAAC,SAAS,KAAK,CAAC,IAAI,gBAAgB;AACtC,WAAO;AAAA,EACT;AACA,SAAO;AAAA,IACL,GAAG,IAAI,eAAe;AAAA,IACtB,GAAG,IAAI,eAAe;AAAA,EACxB;AACF;AACA,SAAS,uBAAuB,SAAS,SAAS,sBAAsB;AACtE,MAAI,YAAY,QAAQ;AACtB,cAAU;AAAA,EACZ;AACA,MAAI,CAAC,wBAAwB,WAAW,yBAAyBA,WAAU,OAAO,GAAG;AACnF,WAAO;AAAA,EACT;AACA,SAAO;AACT;AAEA,SAAS,sBAAsB,SAAS,cAAc,iBAAiB,cAAc;AACnF,MAAI,iBAAiB,QAAQ;AAC3B,mBAAe;AAAA,EACjB;AACA,MAAI,oBAAoB,QAAQ;AAC9B,sBAAkB;AAAA,EACpB;AACA,QAAM,aAAa,QAAQ,sBAAsB;AACjD,QAAM,aAAa,cAAc,OAAO;AACxC,MAAI,QAAQ,aAAa,CAAC;AAC1B,MAAI,cAAc;AAChB,QAAI,cAAc;AAChB,UAAID,WAAU,YAAY,GAAG;AAC3B,gBAAQ,SAAS,YAAY;AAAA,MAC/B;AAAA,IACF,OAAO;AACL,cAAQ,SAAS,OAAO;AAAA,IAC1B;AAAA,EACF;AACA,QAAM,gBAAgB,uBAAuB,YAAY,iBAAiB,YAAY,IAAI,iBAAiB,UAAU,IAAI,aAAa,CAAC;AACvI,MAAI,KAAK,WAAW,OAAO,cAAc,KAAK,MAAM;AACpD,MAAI,KAAK,WAAW,MAAM,cAAc,KAAK,MAAM;AACnD,MAAI,QAAQ,WAAW,QAAQ,MAAM;AACrC,MAAI,SAAS,WAAW,SAAS,MAAM;AACvC,MAAI,YAAY;AACd,UAAM,MAAMC,WAAU,UAAU;AAChC,UAAM,YAAY,gBAAgBD,WAAU,YAAY,IAAIC,WAAU,YAAY,IAAI;AACtF,QAAI,gBAAgB,IAAI;AACxB,WAAO,iBAAiB,gBAAgB,cAAc,KAAK;AACzD,YAAM,cAAc,SAAS,aAAa;AAC1C,YAAM,aAAa,cAAc,sBAAsB;AACvD,YAAM,MAAMH,kBAAiB,aAAa;AAC1C,YAAM,OAAO,WAAW,QAAQ,cAAc,aAAa,WAAW,IAAI,WAAW,KAAK,YAAY;AACtG,YAAM,MAAM,WAAW,OAAO,cAAc,YAAY,WAAW,IAAI,UAAU,KAAK,YAAY;AAClG,WAAK,YAAY;AACjB,WAAK,YAAY;AACjB,eAAS,YAAY;AACrB,gBAAU,YAAY;AACtB,WAAK;AACL,WAAK;AACL,sBAAgBG,WAAU,aAAa,EAAE;AAAA,IAC3C;AAAA,EACF;AACA,SAAO,iBAAiB;AAAA,IACtB;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,CAAC;AACH;AAEA,SAAS,sDAAsD,MAAM;AACnE,MAAI;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,EACF,IAAI;AACJ,QAAM,0BAA0BF,eAAc,YAAY;AAC1D,QAAM,kBAAkBG,oBAAmB,YAAY;AACvD,MAAI,iBAAiB,iBAAiB;AACpC,WAAO;AAAA,EACT;AACA,MAAI,SAAS;AAAA,IACX,YAAY;AAAA,IACZ,WAAW;AAAA,EACb;AACA,MAAI,QAAQ,aAAa,CAAC;AAC1B,QAAM,UAAU,aAAa,CAAC;AAC9B,MAAI,2BAA2B,CAAC,2BAA2B,aAAa,SAAS;AAC/E,QAAIC,aAAY,YAAY,MAAM,UAAU,kBAAkB,eAAe,GAAG;AAC9E,eAAS,cAAc,YAAY;AAAA,IACrC;AACA,QAAIJ,eAAc,YAAY,GAAG;AAC/B,YAAM,aAAa,sBAAsB,YAAY;AACrD,cAAQ,SAAS,YAAY;AAC7B,cAAQ,IAAI,WAAW,IAAI,aAAa;AACxC,cAAQ,IAAI,WAAW,IAAI,aAAa;AAAA,IAC1C;AAAA,EACF;AACA,SAAO;AAAA,IACL,OAAO,KAAK,QAAQ,MAAM;AAAA,IAC1B,QAAQ,KAAK,SAAS,MAAM;AAAA,IAC5B,GAAG,KAAK,IAAI,MAAM,IAAI,OAAO,aAAa,MAAM,IAAI,QAAQ;AAAA,IAC5D,GAAG,KAAK,IAAI,MAAM,IAAI,OAAO,YAAY,MAAM,IAAI,QAAQ;AAAA,EAC7D;AACF;AAEA,SAAS,eAAe,SAAS;AAC/B,SAAO,MAAM,KAAK,QAAQ,eAAe,CAAC;AAC5C;AAEA,SAAS,oBAAoB,SAAS;AAGpC,SAAO,sBAAsBG,oBAAmB,OAAO,CAAC,EAAE,OAAO,cAAc,OAAO,EAAE;AAC1F;AAIA,SAAS,gBAAgB,SAAS;AAChC,QAAM,OAAOA,oBAAmB,OAAO;AACvC,QAAM,SAAS,cAAc,OAAO;AACpC,QAAM,OAAO,QAAQ,cAAc;AACnC,QAAM,QAAQE,KAAI,KAAK,aAAa,KAAK,aAAa,KAAK,aAAa,KAAK,WAAW;AACxF,QAAM,SAASA,KAAI,KAAK,cAAc,KAAK,cAAc,KAAK,cAAc,KAAK,YAAY;AAC7F,MAAI,IAAI,CAAC,OAAO,aAAa,oBAAoB,OAAO;AACxD,QAAM,IAAI,CAAC,OAAO;AAClB,MAAIN,kBAAiB,IAAI,EAAE,cAAc,OAAO;AAC9C,SAAKM,KAAI,KAAK,aAAa,KAAK,WAAW,IAAI;AAAA,EACjD;AACA,SAAO;AAAA,IACL;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AACF;AAEA,SAAS,gBAAgB,SAAS,UAAU;AAC1C,QAAM,MAAMH,WAAU,OAAO;AAC7B,QAAM,OAAOC,oBAAmB,OAAO;AACvC,QAAM,iBAAiB,IAAI;AAC3B,MAAI,QAAQ,KAAK;AACjB,MAAI,SAAS,KAAK;AAClB,MAAI,IAAI;AACR,MAAI,IAAI;AACR,MAAI,gBAAgB;AAClB,YAAQ,eAAe;AACvB,aAAS,eAAe;AACxB,UAAM,sBAAsB,SAAS;AACrC,QAAI,CAAC,uBAAuB,uBAAuB,aAAa,SAAS;AACvE,UAAI,eAAe;AACnB,UAAI,eAAe;AAAA,IACrB;AAAA,EACF;AACA,SAAO;AAAA,IACL;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AACF;AAGA,SAAS,2BAA2B,SAAS,UAAU;AACrD,QAAM,aAAa,sBAAsB,SAAS,MAAM,aAAa,OAAO;AAC5E,QAAM,MAAM,WAAW,MAAM,QAAQ;AACrC,QAAM,OAAO,WAAW,OAAO,QAAQ;AACvC,QAAM,QAAQH,eAAc,OAAO,IAAI,SAAS,OAAO,IAAI,aAAa,CAAC;AACzE,QAAM,QAAQ,QAAQ,cAAc,MAAM;AAC1C,QAAM,SAAS,QAAQ,eAAe,MAAM;AAC5C,QAAM,IAAI,OAAO,MAAM;AACvB,QAAM,IAAI,MAAM,MAAM;AACtB,SAAO;AAAA,IACL;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AACF;AACA,SAAS,kCAAkC,SAAS,kBAAkB,UAAU;AAC9E,MAAI;AACJ,MAAI,qBAAqB,YAAY;AACnC,WAAO,gBAAgB,SAAS,QAAQ;AAAA,EAC1C,WAAW,qBAAqB,YAAY;AAC1C,WAAO,gBAAgBG,oBAAmB,OAAO,CAAC;AAAA,EACpD,WAAWF,WAAU,gBAAgB,GAAG;AACtC,WAAO,2BAA2B,kBAAkB,QAAQ;AAAA,EAC9D,OAAO;AACL,UAAM,gBAAgB,iBAAiB,OAAO;AAC9C,WAAO;AAAA,MACL,GAAG;AAAA,MACH,GAAG,iBAAiB,IAAI,cAAc;AAAA,MACtC,GAAG,iBAAiB,IAAI,cAAc;AAAA,IACxC;AAAA,EACF;AACA,SAAO,iBAAiB,IAAI;AAC9B;AACA,SAAS,yBAAyB,SAAS,UAAU;AACnD,QAAM,aAAaK,eAAc,OAAO;AACxC,MAAI,eAAe,YAAY,CAACL,WAAU,UAAU,KAAKM,uBAAsB,UAAU,GAAG;AAC1F,WAAO;AAAA,EACT;AACA,SAAOR,kBAAiB,UAAU,EAAE,aAAa,WAAW,yBAAyB,YAAY,QAAQ;AAC3G;AAKA,SAAS,4BAA4B,SAAS,OAAO;AACnD,QAAM,eAAe,MAAM,IAAI,OAAO;AACtC,MAAI,cAAc;AAChB,WAAO;AAAA,EACT;AACA,MAAI,SAAS,qBAAqB,SAAS,CAAC,GAAG,KAAK,EAAE,OAAO,QAAME,WAAU,EAAE,KAAKG,aAAY,EAAE,MAAM,MAAM;AAC9G,MAAI,sCAAsC;AAC1C,QAAM,iBAAiBL,kBAAiB,OAAO,EAAE,aAAa;AAC9D,MAAI,cAAc,iBAAiBO,eAAc,OAAO,IAAI;AAG5D,SAAOL,WAAU,WAAW,KAAK,CAACM,uBAAsB,WAAW,GAAG;AACpE,UAAM,gBAAgBR,kBAAiB,WAAW;AAClD,UAAM,0BAA0B,kBAAkB,WAAW;AAC7D,QAAI,CAAC,2BAA2B,cAAc,aAAa,SAAS;AAClE,4CAAsC;AAAA,IACxC;AACA,UAAM,wBAAwB,iBAAiB,CAAC,2BAA2B,CAAC,sCAAsC,CAAC,2BAA2B,cAAc,aAAa,YAAY,CAAC,CAAC,uCAAuC,CAAC,YAAY,OAAO,EAAE,SAAS,oCAAoC,QAAQ,KAAK,kBAAkB,WAAW,KAAK,CAAC,2BAA2B,yBAAyB,SAAS,WAAW;AACzZ,QAAI,uBAAuB;AAEzB,eAAS,OAAO,OAAO,cAAY,aAAa,WAAW;AAAA,IAC7D,OAAO;AAEL,4CAAsC;AAAA,IACxC;AACA,kBAAcO,eAAc,WAAW;AAAA,EACzC;AACA,QAAM,IAAI,SAAS,MAAM;AACzB,SAAO;AACT;AAIA,SAAS,gBAAgB,MAAM;AAC7B,MAAI;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,IAAI;AACJ,QAAM,2BAA2B,aAAa,sBAAsB,4BAA4B,SAAS,KAAK,EAAE,IAAI,CAAC,EAAE,OAAO,QAAQ;AACtI,QAAM,oBAAoB,CAAC,GAAG,0BAA0B,YAAY;AACpE,QAAM,wBAAwB,kBAAkB,CAAC;AACjD,QAAM,eAAe,kBAAkB,OAAO,CAAC,SAAS,qBAAqB;AAC3E,UAAM,OAAO,kCAAkC,SAAS,kBAAkB,QAAQ;AAClF,YAAQ,MAAMD,KAAI,KAAK,KAAK,QAAQ,GAAG;AACvC,YAAQ,QAAQG,KAAI,KAAK,OAAO,QAAQ,KAAK;AAC7C,YAAQ,SAASA,KAAI,KAAK,QAAQ,QAAQ,MAAM;AAChD,YAAQ,OAAOH,KAAI,KAAK,MAAM,QAAQ,IAAI;AAC1C,WAAO;AAAA,EACT,GAAG,kCAAkC,SAAS,uBAAuB,QAAQ,CAAC;AAC9E,SAAO;AAAA,IACL,OAAO,aAAa,QAAQ,aAAa;AAAA,IACzC,QAAQ,aAAa,SAAS,aAAa;AAAA,IAC3C,GAAG,aAAa;AAAA,IAChB,GAAG,aAAa;AAAA,EAClB;AACF;AAEA,SAAS,cAAc,SAAS;AAC9B,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,EACF,IAAI,iBAAiB,OAAO;AAC5B,SAAO;AAAA,IACL;AAAA,IACA;AAAA,EACF;AACF;AAEA,SAAS,8BAA8B,SAAS,cAAc,UAAU;AACtE,QAAM,0BAA0BL,eAAc,YAAY;AAC1D,QAAM,kBAAkBG,oBAAmB,YAAY;AACvD,QAAM,UAAU,aAAa;AAC7B,QAAM,OAAO,sBAAsB,SAAS,MAAM,SAAS,YAAY;AACvE,MAAI,SAAS;AAAA,IACX,YAAY;AAAA,IACZ,WAAW;AAAA,EACb;AACA,QAAM,UAAU,aAAa,CAAC;AAC9B,MAAI,2BAA2B,CAAC,2BAA2B,CAAC,SAAS;AACnE,QAAIC,aAAY,YAAY,MAAM,UAAU,kBAAkB,eAAe,GAAG;AAC9E,eAAS,cAAc,YAAY;AAAA,IACrC;AACA,QAAI,yBAAyB;AAC3B,YAAM,aAAa,sBAAsB,cAAc,MAAM,SAAS,YAAY;AAClF,cAAQ,IAAI,WAAW,IAAI,aAAa;AACxC,cAAQ,IAAI,WAAW,IAAI,aAAa;AAAA,IAC1C,WAAW,iBAAiB;AAC1B,cAAQ,IAAI,oBAAoB,eAAe;AAAA,IACjD;AAAA,EACF;AACA,SAAO;AAAA,IACL,GAAG,KAAK,OAAO,OAAO,aAAa,QAAQ;AAAA,IAC3C,GAAG,KAAK,MAAM,OAAO,YAAY,QAAQ;AAAA,IACzC,OAAO,KAAK;AAAA,IACZ,QAAQ,KAAK;AAAA,EACf;AACF;AAEA,SAAS,oBAAoB,SAAS,UAAU;AAC9C,MAAI,CAACJ,eAAc,OAAO,KAAKD,kBAAiB,OAAO,EAAE,aAAa,SAAS;AAC7E,WAAO;AAAA,EACT;AACA,MAAI,UAAU;AACZ,WAAO,SAAS,OAAO;AAAA,EACzB;AACA,SAAO,QAAQ;AACjB;AAIA,SAAS,gBAAgB,SAAS,UAAU;AAC1C,QAAMU,UAASP,WAAU,OAAO;AAChC,MAAI,CAACF,eAAc,OAAO,GAAG;AAC3B,WAAOS;AAAA,EACT;AACA,MAAI,eAAe,oBAAoB,SAAS,QAAQ;AACxD,SAAO,gBAAgB,eAAe,YAAY,KAAKV,kBAAiB,YAAY,EAAE,aAAa,UAAU;AAC3G,mBAAe,oBAAoB,cAAc,QAAQ;AAAA,EAC3D;AACA,MAAI,iBAAiBK,aAAY,YAAY,MAAM,UAAUA,aAAY,YAAY,MAAM,UAAUL,kBAAiB,YAAY,EAAE,aAAa,YAAY,CAAC,kBAAkB,YAAY,IAAI;AAC9L,WAAOU;AAAA,EACT;AACA,SAAO,gBAAgB,mBAAmB,OAAO,KAAKA;AACxD;AAEA,IAAM,kBAAkB,eAAgB,MAAM;AAC5C,MAAI;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,EACF,IAAI;AACJ,QAAM,oBAAoB,KAAK,mBAAmB;AAClD,QAAM,kBAAkB,KAAK;AAC7B,SAAO;AAAA,IACL,WAAW,8BAA8B,WAAW,MAAM,kBAAkB,QAAQ,GAAG,QAAQ;AAAA,IAC/F,UAAU;AAAA,MACR,GAAG;AAAA,MACH,GAAG;AAAA,MACH,GAAI,MAAM,gBAAgB,QAAQ;AAAA,IACpC;AAAA,EACF;AACF;AAEA,SAAS,MAAM,SAAS;AACtB,SAAOV,kBAAiB,OAAO,EAAE,cAAc;AACjD;AAEA,IAAM,WAAW;AAAA,EACf;AAAA,EACA,oBAAAI;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA,WAAAF;AAAA,EACA;AACF;AAGA,SAAS,YAAY,SAAS,QAAQ;AACpC,MAAI,KAAK;AACT,MAAIS;AACJ,QAAM,OAAOP,oBAAmB,OAAO;AACvC,WAAS,UAAU;AACjB,iBAAaO,UAAS;AACtB,UAAM,GAAG,WAAW;AACpB,SAAK;AAAA,EACP;AACA,WAAS,QAAQ,MAAM,WAAW;AAChC,QAAI,SAAS,QAAQ;AACnB,aAAO;AAAA,IACT;AACA,QAAI,cAAc,QAAQ;AACxB,kBAAY;AAAA,IACd;AACA,YAAQ;AACR,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF,IAAI,QAAQ,sBAAsB;AAClC,QAAI,CAAC,MAAM;AACT,aAAO;AAAA,IACT;AACA,QAAI,CAAC,SAAS,CAAC,QAAQ;AACrB;AAAA,IACF;AACA,UAAM,WAAWC,OAAM,GAAG;AAC1B,UAAM,aAAaA,OAAM,KAAK,eAAe,OAAO,MAAM;AAC1D,UAAM,cAAcA,OAAM,KAAK,gBAAgB,MAAM,OAAO;AAC5D,UAAM,YAAYA,OAAM,IAAI;AAC5B,UAAM,aAAa,CAAC,WAAW,QAAQ,CAAC,aAAa,QAAQ,CAAC,cAAc,QAAQ,CAAC,YAAY;AACjG,UAAM,UAAU;AAAA,MACd;AAAA,MACA,WAAWN,KAAI,GAAGG,KAAI,GAAG,SAAS,CAAC,KAAK;AAAA,IAC1C;AACA,QAAI,gBAAgB;AACpB,aAAS,cAAc,SAAS;AAC9B,YAAM,QAAQ,QAAQ,CAAC,EAAE;AACzB,UAAI,UAAU,WAAW;AACvB,YAAI,CAAC,eAAe;AAClB,iBAAO,QAAQ;AAAA,QACjB;AACA,YAAI,CAAC,OAAO;AACV,UAAAE,aAAY,WAAW,MAAM;AAC3B,oBAAQ,OAAO,IAAI;AAAA,UACrB,GAAG,GAAG;AAAA,QACR,OAAO;AACL,kBAAQ,OAAO,KAAK;AAAA,QACtB;AAAA,MACF;AACA,sBAAgB;AAAA,IAClB;AAIA,QAAI;AACF,WAAK,IAAI,qBAAqB,eAAe;AAAA,QAC3C,GAAG;AAAA;AAAA,QAEH,MAAM,KAAK;AAAA,MACb,CAAC;AAAA,IACH,SAAS,GAAG;AACV,WAAK,IAAI,qBAAqB,eAAe,OAAO;AAAA,IACtD;AACA,OAAG,QAAQ,OAAO;AAAA,EACpB;AACA,UAAQ,IAAI;AACZ,SAAO;AACT;AAUA,SAAS,WAAW,WAAW,UAAU,QAAQ,SAAS;AACxD,MAAI,YAAY,QAAQ;AACtB,cAAU,CAAC;AAAA,EACb;AACA,QAAM;AAAA,IACJ,iBAAiB;AAAA,IACjB,iBAAiB;AAAA,IACjB,gBAAgB,OAAO,mBAAmB;AAAA,IAC1C,cAAc,OAAO,yBAAyB;AAAA,IAC9C,iBAAiB;AAAA,EACnB,IAAI;AACJ,QAAM,cAAc,cAAc,SAAS;AAC3C,QAAM,YAAY,kBAAkB,iBAAiB,CAAC,GAAI,cAAc,qBAAqB,WAAW,IAAI,CAAC,GAAI,GAAG,qBAAqB,QAAQ,CAAC,IAAI,CAAC;AACvJ,YAAU,QAAQ,cAAY;AAC5B,sBAAkB,SAAS,iBAAiB,UAAU,QAAQ;AAAA,MAC5D,SAAS;AAAA,IACX,CAAC;AACD,sBAAkB,SAAS,iBAAiB,UAAU,MAAM;AAAA,EAC9D,CAAC;AACD,QAAM,YAAY,eAAe,cAAc,YAAY,aAAa,MAAM,IAAI;AAClF,MAAI,iBAAiB;AACrB,MAAI,iBAAiB;AACrB,MAAI,eAAe;AACjB,qBAAiB,IAAI,eAAe,UAAQ;AAC1C,UAAI,CAAC,UAAU,IAAI;AACnB,UAAI,cAAc,WAAW,WAAW,eAAe,gBAAgB;AAGrE,uBAAe,UAAU,QAAQ;AACjC,6BAAqB,cAAc;AACnC,yBAAiB,sBAAsB,MAAM;AAC3C,4BAAkB,eAAe,QAAQ,QAAQ;AAAA,QACnD,CAAC;AAAA,MACH;AACA,aAAO;AAAA,IACT,CAAC;AACD,QAAI,eAAe,CAAC,gBAAgB;AAClC,qBAAe,QAAQ,WAAW;AAAA,IACpC;AACA,mBAAe,QAAQ,QAAQ;AAAA,EACjC;AACA,MAAI;AACJ,MAAI,cAAc,iBAAiB,sBAAsB,SAAS,IAAI;AACtE,MAAI,gBAAgB;AAClB,cAAU;AAAA,EACZ;AACA,WAAS,YAAY;AACnB,UAAM,cAAc,sBAAsB,SAAS;AACnD,QAAI,gBAAgB,YAAY,MAAM,YAAY,KAAK,YAAY,MAAM,YAAY,KAAK,YAAY,UAAU,YAAY,SAAS,YAAY,WAAW,YAAY,SAAS;AAC/K,aAAO;AAAA,IACT;AACA,kBAAc;AACd,cAAU,sBAAsB,SAAS;AAAA,EAC3C;AACA,SAAO;AACP,SAAO,MAAM;AACX,cAAU,QAAQ,cAAY;AAC5B,wBAAkB,SAAS,oBAAoB,UAAU,MAAM;AAC/D,wBAAkB,SAAS,oBAAoB,UAAU,MAAM;AAAA,IACjE,CAAC;AACD,iBAAa,UAAU;AACvB,sBAAkB,eAAe,WAAW;AAC5C,qBAAiB;AACjB,QAAI,gBAAgB;AAClB,2BAAqB,OAAO;AAAA,IAC9B;AAAA,EACF;AACF;AAQA,IAAME,iBAAgB;AAOtB,IAAMC,SAAQ;AAQd,IAAMC,QAAO;AAQb,IAAMC,QAAO;AAOb,IAAMC,QAAO;AAOb,IAAMC,SAAQ;AAOd,IAAMC,UAAS;AAKf,IAAMC,cAAa;AAMnB,IAAMC,mBAAkB,CAAC,WAAW,UAAU,YAAY;AAIxD,QAAM,QAAQ,oBAAI,IAAI;AACtB,QAAM,gBAAgB;AAAA,IACpB;AAAA,IACA,GAAG;AAAA,EACL;AACA,QAAM,oBAAoB;AAAA,IACxB,GAAG,cAAc;AAAA,IACjB,IAAI;AAAA,EACN;AACA,SAAO,gBAAkB,WAAW,UAAU;AAAA,IAC5C,GAAG;AAAA,IACH,UAAU;AAAA,EACZ,CAAC;AACH;;;AClpBA,YAAuB;AACvB,mBAA2C;AAC3C,eAA0B;AAQ1B,IAAMC,SAAQ,aAAW;AACvB,WAAS,MAAM,OAAO;AACpB,WAAO,CAAC,EAAE,eAAe,KAAK,OAAO,SAAS;AAAA,EAChD;AACA,SAAO;AAAA,IACL,MAAM;AAAA,IACN;AAAA,IACA,GAAG,OAAO;AACR,YAAM;AAAA,QACJ;AAAA,QACA;AAAA,MACF,IAAI,OAAO,YAAY,aAAa,QAAQ,KAAK,IAAI;AACrD,UAAI,WAAW,MAAM,OAAO,GAAG;AAC7B,YAAI,QAAQ,WAAW,MAAM;AAC3B,iBAAOA,OAAQ;AAAA,YACb,SAAS,QAAQ;AAAA,YACjB;AAAA,UACF,CAAC,EAAE,GAAG,KAAK;AAAA,QACb;AACA,eAAO,CAAC;AAAA,MACV,WAAW,SAAS;AAClB,eAAOA,OAAQ;AAAA,UACb;AAAA,UACA;AAAA,QACF,CAAC,EAAE,GAAG,KAAK;AAAA,MACb;AACA,aAAO,CAAC;AAAA,IACV;AAAA,EACF;AACF;AAEA,IAAI,QAAQ,OAAO,aAAa,cAAc,+BAAkB;AAIhE,SAAS,UAAU,GAAG,GAAG;AACvB,MAAI,MAAM,GAAG;AACX,WAAO;AAAA,EACT;AACA,MAAI,OAAO,MAAM,OAAO,GAAG;AACzB,WAAO;AAAA,EACT;AACA,MAAI,OAAO,MAAM,cAAc,EAAE,SAAS,MAAM,EAAE,SAAS,GAAG;AAC5D,WAAO;AAAA,EACT;AACA,MAAI,QAAQ,GAAG;AACf,MAAI,KAAK,KAAK,OAAO,KAAK,UAAU;AAClC,QAAI,MAAM,QAAQ,CAAC,GAAG;AACpB,eAAS,EAAE;AACX,UAAI,UAAU,EAAE;AAAQ,eAAO;AAC/B,WAAK,IAAI,QAAQ,QAAQ,KAAI;AAC3B,YAAI,CAAC,UAAU,EAAE,CAAC,GAAG,EAAE,CAAC,CAAC,GAAG;AAC1B,iBAAO;AAAA,QACT;AAAA,MACF;AACA,aAAO;AAAA,IACT;AACA,WAAO,OAAO,KAAK,CAAC;AACpB,aAAS,KAAK;AACd,QAAI,WAAW,OAAO,KAAK,CAAC,EAAE,QAAQ;AACpC,aAAO;AAAA,IACT;AACA,SAAK,IAAI,QAAQ,QAAQ,KAAI;AAC3B,UAAI,CAAC,CAAC,EAAE,eAAe,KAAK,GAAG,KAAK,CAAC,CAAC,GAAG;AACvC,eAAO;AAAA,MACT;AAAA,IACF;AACA,SAAK,IAAI,QAAQ,QAAQ,KAAI;AAC3B,YAAM,MAAM,KAAK,CAAC;AAClB,UAAI,QAAQ,YAAY,EAAE,UAAU;AAClC;AAAA,MACF;AACA,UAAI,CAAC,UAAU,EAAE,GAAG,GAAG,EAAE,GAAG,CAAC,GAAG;AAC9B,eAAO;AAAA,MACT;AAAA,IACF;AACA,WAAO;AAAA,EACT;AACA,SAAO,MAAM,KAAK,MAAM;AAC1B;AAEA,SAAS,OAAO,SAAS;AACvB,MAAI,OAAO,WAAW,aAAa;AACjC,WAAO;AAAA,EACT;AACA,QAAM,MAAM,QAAQ,cAAc,eAAe;AACjD,SAAO,IAAI,oBAAoB;AACjC;AAEA,SAAS,WAAW,SAAS,OAAO;AAClC,QAAM,MAAM,OAAO,OAAO;AAC1B,SAAO,KAAK,MAAM,QAAQ,GAAG,IAAI;AACnC;AAEA,SAAS,aAAa,OAAO;AAC3B,QAAM,MAAY,aAAO,KAAK;AAC9B,QAAM,MAAM;AACV,QAAI,UAAU;AAAA,EAChB,CAAC;AACD,SAAO;AACT;AAMA,SAAS,YAAY,SAAS;AAC5B,MAAI,YAAY,QAAQ;AACtB,cAAU,CAAC;AAAA,EACb;AACA,QAAM;AAAA,IACJ,YAAY;AAAA,IACZ,WAAW;AAAA,IACX,aAAa,CAAC;AAAA,IACd,UAAAC;AAAA,IACA,UAAU;AAAA,MACR,WAAW;AAAA,MACX,UAAU;AAAA,IACZ,IAAI,CAAC;AAAA,IACL,YAAY;AAAA,IACZ;AAAA,IACA;AAAA,EACF,IAAI;AACJ,QAAM,CAAC,MAAM,OAAO,IAAU,eAAS;AAAA,IACrC,GAAG;AAAA,IACH,GAAG;AAAA,IACH;AAAA,IACA;AAAA,IACA,gBAAgB,CAAC;AAAA,IACjB,cAAc;AAAA,EAChB,CAAC;AACD,QAAM,CAAC,kBAAkB,mBAAmB,IAAU,eAAS,UAAU;AACzE,MAAI,CAAC,UAAU,kBAAkB,UAAU,GAAG;AAC5C,wBAAoB,UAAU;AAAA,EAChC;AACA,QAAM,CAAC,YAAY,aAAa,IAAU,eAAS,IAAI;AACvD,QAAM,CAAC,WAAW,YAAY,IAAU,eAAS,IAAI;AACrD,QAAM,eAAqB,kBAAY,UAAQ;AAC7C,QAAI,QAAQ,aAAa,SAAS;AAChC,mBAAa,UAAU;AACvB,oBAAc,IAAI;AAAA,IACpB;AAAA,EACF,GAAG,CAAC,aAAa,CAAC;AAClB,QAAM,cAAoB,kBAAY,UAAQ;AAC5C,QAAI,SAAS,YAAY,SAAS;AAChC,kBAAY,UAAU;AACtB,mBAAa,IAAI;AAAA,IACnB;AAAA,EACF,GAAG,CAAC,YAAY,CAAC;AACjB,QAAM,cAAc,qBAAqB;AACzC,QAAM,aAAa,oBAAoB;AACvC,QAAM,eAAqB,aAAO,IAAI;AACtC,QAAM,cAAoB,aAAO,IAAI;AACrC,QAAM,UAAgB,aAAO,IAAI;AACjC,QAAM,0BAA0B,aAAa,oBAAoB;AACjE,QAAM,cAAc,aAAaA,SAAQ;AACzC,QAAM,SAAe,kBAAY,MAAM;AACrC,QAAI,CAAC,aAAa,WAAW,CAAC,YAAY,SAAS;AACjD;AAAA,IACF;AACA,UAAM,SAAS;AAAA,MACb;AAAA,MACA;AAAA,MACA,YAAY;AAAA,IACd;AACA,QAAI,YAAY,SAAS;AACvB,aAAO,WAAW,YAAY;AAAA,IAChC;AACA,IAAAC,iBAAgB,aAAa,SAAS,YAAY,SAAS,MAAM,EAAE,KAAK,CAAAC,UAAQ;AAC9E,YAAM,WAAW;AAAA,QACf,GAAGA;AAAA,QACH,cAAc;AAAA,MAChB;AACA,UAAI,aAAa,WAAW,CAAC,UAAU,QAAQ,SAAS,QAAQ,GAAG;AACjE,gBAAQ,UAAU;AAClB,QAAS,mBAAU,MAAM;AACvB,kBAAQ,QAAQ;AAAA,QAClB,CAAC;AAAA,MACH;AAAA,IACF,CAAC;AAAA,EACH,GAAG,CAAC,kBAAkB,WAAW,UAAU,WAAW,CAAC;AACvD,QAAM,MAAM;AACV,QAAI,SAAS,SAAS,QAAQ,QAAQ,cAAc;AAClD,cAAQ,QAAQ,eAAe;AAC/B,cAAQ,CAAAA,WAAS;AAAA,QACf,GAAGA;AAAA,QACH,cAAc;AAAA,MAChB,EAAE;AAAA,IACJ;AAAA,EACF,GAAG,CAAC,IAAI,CAAC;AACT,QAAM,eAAqB,aAAO,KAAK;AACvC,QAAM,MAAM;AACV,iBAAa,UAAU;AACvB,WAAO,MAAM;AACX,mBAAa,UAAU;AAAA,IACzB;AAAA,EACF,GAAG,CAAC,CAAC;AACL,QAAM,MAAM;AACV,QAAI;AAAa,mBAAa,UAAU;AACxC,QAAI;AAAY,kBAAY,UAAU;AACtC,QAAI,eAAe,YAAY;AAC7B,UAAI,wBAAwB,SAAS;AACnC,eAAO,wBAAwB,QAAQ,aAAa,YAAY,MAAM;AAAA,MACxE,OAAO;AACL,eAAO;AAAA,MACT;AAAA,IACF;AAAA,EACF,GAAG,CAAC,aAAa,YAAY,QAAQ,uBAAuB,CAAC;AAC7D,QAAM,OAAa,cAAQ,OAAO;AAAA,IAChC,WAAW;AAAA,IACX,UAAU;AAAA,IACV;AAAA,IACA;AAAA,EACF,IAAI,CAAC,cAAc,WAAW,CAAC;AAC/B,QAAM,WAAiB,cAAQ,OAAO;AAAA,IACpC,WAAW;AAAA,IACX,UAAU;AAAA,EACZ,IAAI,CAAC,aAAa,UAAU,CAAC;AAC7B,QAAM,iBAAuB,cAAQ,MAAM;AACzC,UAAM,gBAAgB;AAAA,MACpB,UAAU;AAAA,MACV,MAAM;AAAA,MACN,KAAK;AAAA,IACP;AACA,QAAI,CAAC,SAAS,UAAU;AACtB,aAAO;AAAA,IACT;AACA,UAAM,IAAI,WAAW,SAAS,UAAU,KAAK,CAAC;AAC9C,UAAM,IAAI,WAAW,SAAS,UAAU,KAAK,CAAC;AAC9C,QAAI,WAAW;AACb,aAAO;AAAA,QACL,GAAG;AAAA,QACH,WAAW,eAAe,IAAI,SAAS,IAAI;AAAA,QAC3C,GAAI,OAAO,SAAS,QAAQ,KAAK,OAAO;AAAA,UACtC,YAAY;AAAA,QACd;AAAA,MACF;AAAA,IACF;AACA,WAAO;AAAA,MACL,UAAU;AAAA,MACV,MAAM;AAAA,MACN,KAAK;AAAA,IACP;AAAA,EACF,GAAG,CAAC,UAAU,WAAW,SAAS,UAAU,KAAK,GAAG,KAAK,CAAC,CAAC;AAC3D,SAAa,cAAQ,OAAO;AAAA,IAC1B,GAAG;AAAA,IACH;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,IAAI,CAAC,MAAM,QAAQ,MAAM,UAAU,cAAc,CAAC;AACpD;;;AClQA,IAAMC,qBAAqB,CACzB,sBACA,uBACA,yBACA,wBACA,uBACA,qCACA,gCACA,gCACA,iEACA,8CACA,sBAAsB;AAExB,IAAMC,oBAAoCD,mBAAmBE,KAAK,GAAG;AAErE,IAAMC,YAAY,OAAOC,YAAY;AAErC,IAAMC,UAAUF,YACZ,WAAY;AAAA,IACZC,QAAQE,UAAUD,WAClBD,QAAQE,UAAUC,qBAClBH,QAAQE,UAAUE;AAEtB,IAAMC,cACJ,CAACN,aAAaC,QAAQE,UAAUG,cAC5B,SAACC,SAAO;AAAA,MAAAC;AAAA,SAAKD,YAAAA,QAAAA,YAAOC,SAAAA,UAAAA,uBAAPD,QAASD,iBAAW,QAAAE,yBAApBA,SAAAA,SAAAA,qBAAAC,KAAAF,OAAuB;AAAC,IACrC,SAACA,SAAO;AAAA,SAAKA,YAAAA,QAAAA,YAAAA,SAAAA,SAAAA,QAASG;AAAa;AAUzC,IAAMC,UAAU,SAAVA,SAAoBC,MAAMC,QAAe;AAAA,MAAAC;AAAA,MAAfD,WAAM,QAAA;AAANA,aAAS;EAAI;AAI3C,MAAME,WAAWH,SAAI,QAAJA,SAAIE,SAAAA,UAAAA,qBAAJF,KAAMI,kBAAYF,QAAAA,uBAAA,SAAA,SAAlBA,mBAAAL,KAAAG,MAAqB,OAAO;AAC7C,MAAMK,QAAQF,aAAa,MAAMA,aAAa;AAO9C,MAAMG,SAASD,SAAUJ,UAAUD,QAAQD,SAAQC,KAAKO,UAAU;AAElE,SAAOD;AACT;AAOA,IAAME,oBAAoB,SAApBA,mBAA8BR,MAAM;AAAA,MAAAS;AAIxC,MAAMC,WAAWV,SAAI,QAAJA,SAAIS,SAAAA,UAAAA,sBAAJT,KAAMI,kBAAYK,QAAAA,wBAAA,SAAA,SAAlBA,oBAAAZ,KAAAG,MAAqB,iBAAiB;AACvD,SAAOU,aAAa,MAAMA,aAAa;AACzC;AAQA,IAAMC,gBAAgB,SAAhBA,eAA0BC,IAAIC,kBAAkBC,QAAQ;AAG5D,MAAIf,QAAQa,EAAE,GAAG;AACf,WAAO,CAAA;EACT;AAEA,MAAIG,aAAaC,MAAMzB,UAAU0B,MAAMC,MACrCN,GAAGO,iBAAiBjC,iBAAiB,CACvC;AACA,MAAI2B,oBAAoBvB,QAAQO,KAAKe,IAAI1B,iBAAiB,GAAG;AAC3D6B,eAAWK,QAAQR,EAAE;EACvB;AACAG,eAAaA,WAAWD,OAAOA,MAAM;AACrC,SAAOC;AACT;AAoCA,IAAMM,2BAA2B,SAA3BA,0BACJC,UACAT,kBACAU,SACA;AACA,MAAMR,aAAa,CAAA;AACnB,MAAMS,kBAAkBR,MAAMS,KAAKH,QAAQ;AAC3C,SAAOE,gBAAgBE,QAAQ;AAC7B,QAAM/B,UAAU6B,gBAAgBG,MAAK;AACrC,QAAI5B,QAAQJ,SAAS,KAAK,GAAG;AAG3B;IACF;AAEA,QAAIA,QAAQiC,YAAY,QAAQ;AAE9B,UAAMC,WAAWlC,QAAQmC,iBAAgB;AACzC,UAAMC,UAAUF,SAASH,SAASG,WAAWlC,QAAQqC;AACrD,UAAMC,mBAAmBZ,0BAAyBU,SAAS,MAAMR,OAAO;AACxE,UAAIA,QAAQW,SAAS;AACnBnB,mBAAWoB,KAAIjB,MAAfH,YAAmBkB,gBAAgB;MACrC,OAAO;AACLlB,mBAAWoB,KAAK;UACdC,aAAazC;UACboB,YAAYkB;QACd,CAAC;MACH;IACF,OAAO;AAEL,UAAMI,iBAAiB/C,QAAQO,KAAKF,SAAST,iBAAiB;AAC9D,UACEmD,kBACAd,QAAQT,OAAOnB,OAAO,MACrBkB,oBAAoB,CAACS,SAASgB,SAAS3C,OAAO,IAC/C;AACAoB,mBAAWoB,KAAKxC,OAAO;MACzB;AAGA,UAAM4C,aACJ5C,QAAQ4C;MAEP,OAAOhB,QAAQiB,kBAAkB,cAChCjB,QAAQiB,cAAc7C,OAAO;AAKjC,UAAM8C,kBACJ,CAAC1C,QAAQwC,YAAY,KAAK,MACzB,CAAChB,QAAQmB,oBAAoBnB,QAAQmB,iBAAiB/C,OAAO;AAEhE,UAAI4C,cAAcE,iBAAiB;AAOjC,YAAMR,oBAAmBZ,0BACvBkB,eAAe,OAAO5C,QAAQqC,WAAWO,WAAWP,UACpD,MACAT,OACF;AAEA,YAAIA,QAAQW,SAAS;AACnBnB,qBAAWoB,KAAIjB,MAAfH,YAAmBkB,iBAAgB;QACrC,OAAO;AACLlB,qBAAWoB,KAAK;YACdC,aAAazC;YACboB,YAAYkB;UACd,CAAC;QACH;MACF,OAAO;AAGLT,wBAAgBJ,QAAOF,MAAvBM,iBAA2B7B,QAAQqC,QAAQ;MAC7C;IACF;EACF;AACA,SAAOjB;AACT;AAQA,IAAM4B,cAAc,SAAdA,aAAwB3C,MAAM;AAClC,SAAO,CAAC4C,MAAMC,SAAS7C,KAAKI,aAAa,UAAU,GAAG,EAAE,CAAC;AAC3D;AAQA,IAAM0C,cAAc,SAAdA,aAAwB9C,MAAM;AAClC,MAAI,CAACA,MAAM;AACT,UAAM,IAAI+C,MAAM,kBAAkB;EACpC;AAEA,MAAI/C,KAAKgD,WAAW,GAAG;AAQrB,SACG,0BAA0BC,KAAKjD,KAAK4B,OAAO,KAC1CpB,kBAAkBR,IAAI,MACxB,CAAC2C,YAAY3C,IAAI,GACjB;AACA,aAAO;IACT;EACF;AAEA,SAAOA,KAAKgD;AACd;AAUA,IAAME,uBAAuB,SAAvBA,sBAAiClD,MAAMmD,SAAS;AACpD,MAAMH,WAAWF,YAAY9C,IAAI;AAEjC,MAAIgD,WAAW,KAAKG,WAAW,CAACR,YAAY3C,IAAI,GAAG;AACjD,WAAO;EACT;AAEA,SAAOgD;AACT;AAEA,IAAMI,uBAAuB,SAAvBA,sBAAiCC,GAAGC,GAAG;AAC3C,SAAOD,EAAEL,aAAaM,EAAEN,WACpBK,EAAEE,gBAAgBD,EAAEC,gBACpBF,EAAEL,WAAWM,EAAEN;AACrB;AAEA,IAAMQ,UAAU,SAAVA,SAAoBxD,MAAM;AAC9B,SAAOA,KAAK4B,YAAY;AAC1B;AAEA,IAAM6B,gBAAgB,SAAhBA,eAA0BzD,MAAM;AACpC,SAAOwD,QAAQxD,IAAI,KAAKA,KAAK0D,SAAS;AACxC;AAEA,IAAMC,uBAAuB,SAAvBA,sBAAiC3D,MAAM;AAC3C,MAAM4D,IACJ5D,KAAK4B,YAAY,aACjBZ,MAAMzB,UAAU0B,MACbC,MAAMlB,KAAKgC,QAAQ,EACnB6B,KAAK,SAACC,OAAK;AAAA,WAAKA,MAAMlC,YAAY;GAAU;AACjD,SAAOgC;AACT;AAEA,IAAMG,kBAAkB,SAAlBA,iBAA4BC,OAAOC,MAAM;AAC7C,WAASC,IAAI,GAAGA,IAAIF,MAAMtC,QAAQwC,KAAK;AACrC,QAAIF,MAAME,CAAC,EAAEC,WAAWH,MAAME,CAAC,EAAED,SAASA,MAAM;AAC9C,aAAOD,MAAME,CAAC;IAChB;EACF;AACF;AAEA,IAAME,kBAAkB,SAAlBA,iBAA4BpE,MAAM;AACtC,MAAI,CAACA,KAAKqE,MAAM;AACd,WAAO;EACT;AACA,MAAMC,aAAatE,KAAKiE,QAAQvE,YAAYM,IAAI;AAChD,MAAMuE,cAAc,SAAdA,aAAwBF,MAAM;AAClC,WAAOC,WAAWnD,iBAChB,+BAA+BkD,OAAO,IACxC;;AAGF,MAAIG;AACJ,MACE,OAAOC,WAAW,eAClB,OAAOA,OAAOC,QAAQ,eACtB,OAAOD,OAAOC,IAAIC,WAAW,YAC7B;AACAH,eAAWD,YAAYE,OAAOC,IAAIC,OAAO3E,KAAKqE,IAAI,CAAC;EACrD,OAAO;AACL,QAAI;AACFG,iBAAWD,YAAYvE,KAAKqE,IAAI;aACzBO,KAAK;AAEZC,cAAQC,MACN,4IACAF,IAAIG,OACN;AACA,aAAO;IACT;EACF;AAEA,MAAMZ,UAAUJ,gBAAgBS,UAAUxE,KAAKiE,IAAI;AACnD,SAAO,CAACE,WAAWA,YAAYnE;AACjC;AAEA,IAAMgF,UAAU,SAAVA,SAAoBhF,MAAM;AAC9B,SAAOwD,QAAQxD,IAAI,KAAKA,KAAK0D,SAAS;AACxC;AAEA,IAAMuB,qBAAqB,SAArBA,oBAA+BjF,MAAM;AACzC,SAAOgF,QAAQhF,IAAI,KAAK,CAACoE,gBAAgBpE,IAAI;AAC/C;AAGA,IAAMkF,iBAAiB,SAAjBA,gBAA2BlF,MAAM;AAAA,MAAAmF;AAwBrC,MAAIC,WAAWpF,QAAQN,YAAYM,IAAI;AACvC,MAAIqF,gBAAYF,YAAGC,cAAQ,QAAAD,cAAA,SAAA,SAARA,UAAUG;AAI7B,MAAIC,WAAW;AACf,MAAIH,YAAYA,aAAapF,MAAM;AAAA,QAAAwF,eAAAC,uBAAAC;AACjCH,eAAW,CAAC,GACVC,gBAAAH,kBAAYG,QAAAA,kBAAA,WAAAC,wBAAZD,cAAc1F,mBAAa,QAAA2F,0BAAA,UAA3BA,sBAA6BE,SAASN,YAAY,KAClDrF,SAAI,QAAJA,SAAI0F,WAAAA,sBAAJ1F,KAAMF,mBAAa4F,QAAAA,wBAAA,UAAnBA,oBAAqBC,SAAS3F,IAAI;AAGpC,WAAO,CAACuF,YAAYF,cAAc;AAAA,UAAAO,YAAAC,gBAAAC;AAIhCV,iBAAW1F,YAAY2F,YAAY;AACnCA,sBAAYO,aAAGR,cAAQ,QAAAQ,eAAA,SAAA,SAARA,WAAUN;AACzBC,iBAAW,CAAC,GAAAM,iBAACR,kBAAY,QAAAQ,mBAAA,WAAAC,wBAAZD,eAAc/F,mBAAa,QAAAgG,0BAAA,UAA3BA,sBAA6BH,SAASN,YAAY;IACjE;EACF;AAEA,SAAOE;AACT;AAEA,IAAMQ,aAAa,SAAbA,YAAuB/F,MAAM;AACjC,MAAAgG,wBAA0BhG,KAAKiG,sBAAqB,GAA5CC,QAAKF,sBAALE,OAAOC,SAAMH,sBAANG;AACf,SAAOD,UAAU,KAAKC,WAAW;AACnC;AACA,IAAMC,WAAW,SAAXA,UAAqBpG,MAAIqG,MAAmC;AAAA,MAA/BC,eAAYD,KAAZC,cAAc9D,gBAAa6D,KAAb7D;AAM/C,MAAI+D,iBAAiBvG,IAAI,EAAEwG,eAAe,UAAU;AAClD,WAAO;EACT;AAEA,MAAMC,kBAAkBnH,QAAQO,KAAKG,MAAM,+BAA+B;AAC1E,MAAM0G,mBAAmBD,kBAAkBzG,KAAK2G,gBAAgB3G;AAChE,MAAIV,QAAQO,KAAK6G,kBAAkB,uBAAuB,GAAG;AAC3D,WAAO;EACT;AAEA,MACE,CAACJ,gBACDA,iBAAiB,UACjBA,iBAAiB,eACjB;AACA,QAAI,OAAO9D,kBAAkB,YAAY;AAGvC,UAAMoE,eAAe5G;AACrB,aAAOA,MAAM;AACX,YAAM2G,gBAAgB3G,KAAK2G;AAC3B,YAAME,WAAWnH,YAAYM,IAAI;AACjC,YACE2G,iBACA,CAACA,cAAcpE,cACfC,cAAcmE,aAAa,MAAM,MACjC;AAGA,iBAAOZ,WAAW/F,IAAI;QACxB,WAAWA,KAAK8G,cAAc;AAE5B9G,iBAAOA,KAAK8G;mBACH,CAACH,iBAAiBE,aAAa7G,KAAKF,eAAe;AAE5DE,iBAAO6G,SAASvB;QAClB,OAAO;AAELtF,iBAAO2G;QACT;MACF;AAEA3G,aAAO4G;IACT;AAWA,QAAI1B,eAAelF,IAAI,GAAG;AAKxB,aAAO,CAACA,KAAK+G,eAAc,EAAGrF;IAChC;AAkBA,QAAI4E,iBAAiB,eAAe;AAClC,aAAO;IACT;EAEF,WAAWA,iBAAiB,iBAAiB;AAM3C,WAAOP,WAAW/F,IAAI;EACxB;AAIA,SAAO;AACT;AAKA,IAAMgH,yBAAyB,SAAzBA,wBAAmChH,MAAM;AAC7C,MAAI,mCAAmCiD,KAAKjD,KAAK4B,OAAO,GAAG;AACzD,QAAIrB,aAAaP,KAAK2G;AAEtB,WAAOpG,YAAY;AACjB,UAAIA,WAAWqB,YAAY,cAAcrB,WAAW0G,UAAU;AAE5D,iBAAS/C,IAAI,GAAGA,IAAI3D,WAAWyB,SAASN,QAAQwC,KAAK;AACnD,cAAMJ,QAAQvD,WAAWyB,SAASkF,KAAKhD,CAAC;AAExC,cAAIJ,MAAMlC,YAAY,UAAU;AAG9B,mBAAOtC,QAAQO,KAAKU,YAAY,sBAAsB,IAClD,OACA,CAACuD,MAAM6B,SAAS3F,IAAI;UAC1B;QACF;AAEA,eAAO;MACT;AACAO,mBAAaA,WAAWoG;IAC1B;EACF;AAIA,SAAO;AACT;AAEA,IAAMQ,kCAAkC,SAAlCA,iCAA4C5F,SAASvB,MAAM;AAC/D,MACEA,KAAKiH;;;EAILlH,QAAQC,IAAI,KACZyD,cAAczD,IAAI,KAClBoG,SAASpG,MAAMuB,OAAO;EAEtBoC,qBAAqB3D,IAAI,KACzBgH,uBAAuBhH,IAAI,GAC3B;AACA,WAAO;EACT;AACA,SAAO;AACT;AAEA,IAAMoH,iCAAiC,SAAjCA,gCAA2C7F,SAASvB,MAAM;AAC9D,MACEiF,mBAAmBjF,IAAI,KACvB8C,YAAY9C,IAAI,IAAI,KACpB,CAACmH,gCAAgC5F,SAASvB,IAAI,GAC9C;AACA,WAAO;EACT;AACA,SAAO;AACT;AAEA,IAAMqH,4BAA4B,SAA5BA,2BAAsCC,gBAAgB;AAC1D,MAAMtE,WAAWH,SAASyE,eAAelH,aAAa,UAAU,GAAG,EAAE;AACrE,MAAIwC,MAAMI,QAAQ,KAAKA,YAAY,GAAG;AACpC,WAAO;EACT;AAGA,SAAO;AACT;AAMA,IAAMuE,cAAc,SAAdA,aAAwBxG,YAAY;AACxC,MAAMyG,mBAAmB,CAAA;AACzB,MAAMC,mBAAmB,CAAA;AACzB1G,aAAW2G,QAAQ,SAAUR,MAAMhD,GAAG;AACpC,QAAMf,UAAU,CAAC,CAAC+D,KAAK9E;AACvB,QAAMzC,UAAUwD,UAAU+D,KAAK9E,cAAc8E;AAC7C,QAAMS,oBAAoBzE,qBAAqBvD,SAASwD,OAAO;AAC/D,QAAM7B,WAAW6B,UAAUoE,aAAYL,KAAKnG,UAAU,IAAIpB;AAC1D,QAAIgI,sBAAsB,GAAG;AAC3BxE,gBACIqE,iBAAiBrF,KAAIjB,MAArBsG,kBAAyBlG,QAAQ,IACjCkG,iBAAiBrF,KAAKxC,OAAO;IACnC,OAAO;AACL8H,uBAAiBtF,KAAK;QACpBoB,eAAeW;QACflB,UAAU2E;QACVT;QACA/D;QACApB,SAAST;MACX,CAAC;IACH;EACF,CAAC;AAED,SAAOmG,iBACJG,KAAKxE,oBAAoB,EACzByE,OAAO,SAACC,KAAKC,UAAa;AACzBA,aAAS5E,UACL2E,IAAI3F,KAAIjB,MAAR4G,KAAYC,SAAShG,OAAO,IAC5B+F,IAAI3F,KAAK4F,SAAShG,OAAO;AAC7B,WAAO+F;EACT,GAAG,CAAA,CAAE,EACJE,OAAOR,gBAAgB;AAC5B;AAEMS,IAAAA,WAAW,SAAXA,UAAqBC,WAAW3G,SAAS;AAC7CA,YAAUA,WAAW,CAAA;AAErB,MAAIR;AACJ,MAAIQ,QAAQiB,eAAe;AACzBzB,iBAAaM,yBACX,CAAC6G,SAAS,GACV3G,QAAQV,kBACR;MACEC,QAAQsG,+BAA+Be,KAAK,MAAM5G,OAAO;MACzDW,SAAS;MACTM,eAAejB,QAAQiB;MACvBE,kBAAkB2E;IACpB,CACF;EACF,OAAO;AACLtG,iBAAaJ,cACXuH,WACA3G,QAAQV,kBACRuG,+BAA+Be,KAAK,MAAM5G,OAAO,CACnD;EACF;AACA,SAAOgG,YAAYxG,UAAU;AAC/B;AAsCA,IAAMqH,6BAA6CC,mBAChDC,OAAO,QAAQ,EACfC,KAAK,GAAG;;;AV1pBX,IAAAC,oBAAwC;AAMxC,SAAS,aAAa,MAAM;AAC1B,SAAa,eAAQ,MAAM;AACzB,QAAI,KAAK,MAAM,SAAO,OAAO,IAAI,GAAG;AAClC,aAAO;AAAA,IACT;AACA,WAAO,WAAS;AACd,WAAK,QAAQ,SAAO;AAClB,YAAI,OAAO,QAAQ,YAAY;AAC7B,cAAI,KAAK;AAAA,QACX,WAAW,OAAO,MAAM;AACtB,cAAI,UAAU;AAAA,QAChB;AAAA,MACF,CAAC;AAAA,IACH;AAAA,EAEF,GAAG,IAAI;AACT;AAEA,IAAM,WAAW;AACjB,IAAM,aAAa;AACnB,IAAM,aAAa;AACnB,IAAM,cAAc;AACpB,SAAS,eAAeC,QAAO,MAAM,SAAS;AAC5C,SAAO,KAAK,MAAMA,SAAQ,IAAI,MAAM;AACtC;AACA,SAAS,mBAAmB,SAASA,QAAO;AAC1C,SAAOA,SAAQ,KAAKA,UAAS,QAAQ,QAAQ;AAC/C;AACA,SAAS,YAAY,SAAS,iBAAiB;AAC7C,SAAO,qBAAqB,SAAS;AAAA,IACnC;AAAA,EACF,CAAC;AACH;AACA,SAAS,YAAY,SAAS,iBAAiB;AAC7C,SAAO,qBAAqB,SAAS;AAAA,IACnC,WAAW;AAAA,IACX,eAAe,QAAQ,QAAQ;AAAA,IAC/B;AAAA,EACF,CAAC;AACH;AACA,SAAS,qBAAqB,SAAS,OAAO;AAC5C,MAAI;AAAA,IACF,gBAAgB;AAAA,IAChB,YAAY;AAAA,IACZ;AAAA,IACA,SAAS;AAAA,EACX,IAAI,UAAU,SAAS,CAAC,IAAI;AAC5B,QAAM,OAAO,QAAQ;AACrB,MAAIA,SAAQ;AACZ,KAAG;AACD,QAAI,aAAa;AACjB,IAAAA,SAAQA,UAAS,YAAY,CAAC,SAAS;AAAA,EACzC,SAASA,UAAS,KAAKA,UAAS,KAAK,SAAS,MAAM,kBAAkB,gBAAgB,SAASA,MAAK,IAAI,KAAKA,MAAK,KAAK,UAAU,cAAc,KAAKA,MAAK,MAAM,OAAO,SAAS,YAAY,aAAa,UAAU,QAAQ,eAAe,KAAKA,MAAK,MAAM,OAAO,SAAS,aAAa,aAAa,eAAe,OAAO;AACzT,SAAOA;AACT;AACA,SAAS,sBAAsB,aAAa,MAAM;AAChD,MAAI;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA,WAAW,OAAO;AAAA,EACpB,IAAI;AACJ,MAAI,YAAY;AAChB,MAAI,MAAM,QAAQ,UAAU;AAC1B,YAAQ,UAAU,KAAK;AACvB,QAAI,cAAc,IAAI;AACpB,kBAAY;AAAA,IACd,OAAO;AACL,kBAAY,qBAAqB,aAAa;AAAA,QAC5C,eAAe;AAAA,QACf,QAAQ;AAAA,QACR,WAAW;AAAA,QACX;AAAA,MACF,CAAC;AACD,UAAI,SAAS,YAAY,OAAO,YAAY,YAAY,IAAI;AAC1D,cAAM,MAAM,YAAY;AACxB,cAAM,SAAS,WAAW;AAC1B,cAAMC,UAAS,YAAY,SAAS;AACpC,YAAI,WAAW,KAAK;AAClB,sBAAY;AAAA,QACd,OAAO;AACL,sBAAY,SAAS,MAAMA,UAASA,UAAS;AAAA,QAC/C;AAAA,MACF;AAAA,IACF;AACA,QAAI,mBAAmB,aAAa,SAAS,GAAG;AAC9C,kBAAY;AAAA,IACd;AAAA,EACF;AACA,MAAI,MAAM,QAAQ,YAAY;AAC5B,YAAQ,UAAU,KAAK;AACvB,QAAI,cAAc,IAAI;AACpB,kBAAY;AAAA,IACd,OAAO;AACL,kBAAY,qBAAqB,aAAa;AAAA,QAC5C,eAAe;AAAA,QACf,QAAQ;AAAA,QACR;AAAA,MACF,CAAC;AACD,UAAI,QAAQ,YAAY,OAAO,UAAU;AACvC,oBAAY,qBAAqB,aAAa;AAAA,UAC5C,eAAe,YAAY,OAAO;AAAA,UAClC,QAAQ;AAAA,UACR;AAAA,QACF,CAAC;AAAA,MACH;AAAA,IACF;AACA,QAAI,mBAAmB,aAAa,SAAS,GAAG;AAC9C,kBAAY;AAAA,IACd;AAAA,EACF;AAGA,MAAI,gBAAgB,QAAQ;AAC1B,UAAM,UAAU,MAAM,YAAY,IAAI;AACtC,QAAI,MAAM,QAAQ,aAAa;AAC7B,cAAQ,UAAU,KAAK;AACvB,UAAI,YAAY,SAAS,OAAO,GAAG;AACjC,oBAAY,qBAAqB,aAAa;AAAA,UAC5C,eAAe;AAAA,UACf;AAAA,QACF,CAAC;AACD,YAAI,QAAQ,eAAe,WAAW,MAAM,OAAO,GAAG;AACpD,sBAAY,qBAAqB,aAAa;AAAA,YAC5C,eAAe,YAAY,YAAY,OAAO;AAAA,YAC9C;AAAA,UACF,CAAC;AAAA,QACH;AAAA,MACF,WAAW,MAAM;AACf,oBAAY,qBAAqB,aAAa;AAAA,UAC5C,eAAe,YAAY,YAAY,OAAO;AAAA,UAC9C;AAAA,QACF,CAAC;AAAA,MACH;AACA,UAAI,eAAe,WAAW,MAAM,OAAO,GAAG;AAC5C,oBAAY;AAAA,MACd;AAAA,IACF;AACA,QAAI,MAAM,QAAQ,YAAY;AAC5B,cAAQ,UAAU,KAAK;AACvB,UAAI,YAAY,SAAS,GAAG;AAC1B,oBAAY,qBAAqB,aAAa;AAAA,UAC5C,eAAe;AAAA,UACf;AAAA,UACA,WAAW;AAAA,QACb,CAAC;AACD,YAAI,QAAQ,eAAe,WAAW,MAAM,OAAO,GAAG;AACpD,sBAAY,qBAAqB,aAAa;AAAA,YAC5C,eAAe,aAAa,OAAO,YAAY;AAAA,YAC/C,WAAW;AAAA,YACX;AAAA,UACF,CAAC;AAAA,QACH;AAAA,MACF,WAAW,MAAM;AACf,oBAAY,qBAAqB,aAAa;AAAA,UAC5C,eAAe,aAAa,OAAO,YAAY;AAAA,UAC/C,WAAW;AAAA,UACX;AAAA,QACF,CAAC;AAAA,MACH;AACA,UAAI,eAAe,WAAW,MAAM,OAAO,GAAG;AAC5C,oBAAY;AAAA,MACd;AAAA,IACF;AACA,UAAM,UAAU,MAAM,WAAW,IAAI,MAAM;AAC3C,QAAI,mBAAmB,aAAa,SAAS,GAAG;AAC9C,UAAI,QAAQ,SAAS;AACnB,oBAAY,MAAM,QAAQ,aAAa,WAAW,qBAAqB,aAAa;AAAA,UAClF,eAAe,YAAY,YAAY,OAAO;AAAA,UAC9C;AAAA,QACF,CAAC;AAAA,MACH,OAAO;AACL,oBAAY;AAAA,MACd;AAAA,IACF;AAAA,EACF;AACA,SAAO;AACT;AAEA,IAAI,QAAQ;AACZ,SAAS,aAAa,IAAI,SAAS;AACjC,MAAI,YAAY,QAAQ;AACtB,cAAU,CAAC;AAAA,EACb;AACA,QAAM;AAAA,IACJ,gBAAgB;AAAA,IAChB,iBAAiB;AAAA,IACjB,OAAO;AAAA,EACT,IAAI;AACJ,oBAAkB,qBAAqB,KAAK;AAC5C,QAAM,OAAO,MAAM,MAAM,OAAO,SAAS,GAAG,MAAM;AAAA,IAChD;AAAA,EACF,CAAC;AACD,MAAI,MAAM;AACR,SAAK;AAAA,EACP,OAAO;AACL,YAAQ,sBAAsB,IAAI;AAAA,EACpC;AACF;AAEA,IAAID,SAAQ,OAAO,aAAa,cAAc,gCAAkB;AAEhE,SAAS,uBAAuB,GAAG,GAAG;AACpC,QAAM,WAAW,EAAE,wBAAwB,CAAC;AAC5C,MAAI,WAAW,KAAK,+BAA+B,WAAW,KAAK,gCAAgC;AACjG,WAAO;AAAA,EACT;AACA,MAAI,WAAW,KAAK,+BAA+B,WAAW,KAAK,4BAA4B;AAC7F,WAAO;AAAA,EACT;AACA,SAAO;AACT;AACA,SAAS,aAAa,MAAM,MAAM;AAChC,MAAI,KAAK,SAAS,KAAK,MAAM;AAC3B,WAAO;AAAA,EACT;AACA,aAAW,CAAC,KAAK,KAAK,KAAK,KAAK,QAAQ,GAAG;AACzC,QAAI,UAAU,KAAK,IAAI,GAAG,GAAG;AAC3B,aAAO;AAAA,IACT;AAAA,EACF;AACA,SAAO;AACT;AACA,IAAM,sBAAyC,qBAAc;AAAA,EAC3D,UAAU,MAAM;AAAA,EAAC;AAAA,EACjB,YAAY,MAAM;AAAA,EAAC;AAAA,EACnB,KAAkB,oBAAI,IAAI;AAAA,EAC1B,aAAa;AAAA,IACX,SAAS,CAAC;AAAA,EACZ;AACF,CAAC;AAKD,SAAS,aAAa,MAAM;AAC1B,MAAI;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,EACF,IAAI;AACJ,QAAM,CAAC,KAAK,MAAM,IAAU,gBAAS,MAAM,oBAAI,IAAI,CAAC;AACpD,QAAM,WAAiB,mBAAY,UAAQ;AACzC,WAAO,aAAW,IAAI,IAAI,OAAO,EAAE,IAAI,MAAM,IAAI,CAAC;AAAA,EACpD,GAAG,CAAC,CAAC;AACL,QAAM,aAAmB,mBAAY,UAAQ;AAC3C,WAAO,aAAW;AAChB,YAAME,OAAM,IAAI,IAAI,OAAO;AAC3B,MAAAA,KAAI,OAAO,IAAI;AACf,aAAOA;AAAA,IACT,CAAC;AAAA,EACH,GAAG,CAAC,CAAC;AACL,EAAAF,OAAM,MAAM;AACV,UAAM,SAAS,IAAI,IAAI,GAAG;AAC1B,UAAM,QAAQ,MAAM,KAAK,OAAO,KAAK,CAAC,EAAE,KAAK,sBAAsB;AACnE,UAAM,QAAQ,CAAC,MAAMA,WAAU;AAC7B,aAAO,IAAI,MAAMA,MAAK;AAAA,IACxB,CAAC;AACD,QAAI,CAAC,aAAa,KAAK,MAAM,GAAG;AAC9B,aAAO,MAAM;AAAA,IACf;AAAA,EACF,GAAG,CAAC,GAAG,CAAC;AACR,SAA0B,qBAAc,oBAAoB,UAAU;AAAA,IACpE,OAAa,eAAQ,OAAO;AAAA,MAC1B;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF,IAAI,CAAC,UAAU,YAAY,KAAK,aAAa,SAAS,CAAC;AAAA,EACzD,GAAG,QAAQ;AACb;AACA,SAAS,YAAY,OAAO;AAC1B,MAAI;AAAA,IACF;AAAA,EACF,IAAI,UAAU,SAAS,CAAC,IAAI;AAC5B,QAAM,CAAC,SAAS,QAAQ,IAAU,gBAAS,IAAI;AAC/C,QAAM,eAAqB,cAAO,IAAI;AACtC,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,IAAU,kBAAW,mBAAmB;AACxC,QAAM,MAAY,mBAAY,UAAQ;AACpC,iBAAa,UAAU;AACvB,QAAI,YAAY,MAAM;AACpB,kBAAY,QAAQ,OAAO,IAAI;AAC/B,UAAI,WAAW;AACb,YAAI;AACJ,cAAM,iBAAiB,UAAU;AACjC,kBAAU,QAAQ,OAAO,IAAI,iBAAiB,SAAS,oBAAoB,QAAQ,OAAO,SAAS,KAAK,gBAAgB,OAAO,oBAAoB;AAAA,MACrJ;AAAA,IACF;AAAA,EACF,GAAG,CAAC,SAAS,aAAa,WAAW,KAAK,CAAC;AAC3C,EAAAA,OAAM,MAAM;AACV,UAAM,OAAO,aAAa;AAC1B,QAAI,MAAM;AACR,eAAS,IAAI;AACb,aAAO,MAAM;AACX,mBAAW,IAAI;AAAA,MACjB;AAAA,IACF;AAAA,EACF,GAAG,CAAC,UAAU,UAAU,CAAC;AACzB,EAAAA,OAAM,MAAM;AACV,UAAMA,SAAQ,aAAa,UAAU,IAAI,IAAI,aAAa,OAAO,IAAI;AACrE,QAAIA,UAAS,MAAM;AACjB,eAASA,MAAK;AAAA,IAChB;AAAA,EACF,GAAG,CAAC,GAAG,CAAC;AACR,SAAa,eAAQ,OAAO;AAAA,IAC1B;AAAA,IACA,OAAO,WAAW,OAAO,KAAK;AAAA,EAChC,IAAI,CAAC,SAAS,GAAG,CAAC;AACpB;AAEA,SAAS,UAAU,QAAQ,eAAe;AACxC,MAAI,OAAO,WAAW,YAAY;AAChC,WAAO,OAAO,aAAa;AAAA,EAC7B,WAAW,QAAQ;AACjB,WAA0B,oBAAa,QAAQ,aAAa;AAAA,EAC9D;AACA,SAA0B,qBAAc,OAAO,aAAa;AAC9D;AACA,IAAM,mBAAsC,qBAAc;AAAA,EACxD,aAAa;AAAA,EACb,gBAAgB,MAAM;AAAA,EAAC;AACzB,CAAC;AACD,IAAM,iBAAiB,CAAC,YAAY,WAAW;AAC/C,IAAM,eAAe,CAAC,UAAU,UAAU;AAC1C,IAAM,UAAU,CAAC,GAAG,gBAAgB,GAAG,YAAY;AACnD,IAAM,YAA+B,kBAAW,SAASG,WAAU,MAAM,cAAc;AACrF,MAAI;AAAA,IACF;AAAA,IACA,cAAc;AAAA,IACd,OAAO;AAAA,IACP,OAAO;AAAA,IACP;AAAA,IACA,GAAG;AAAA,EACL,IAAI;AACJ,QAAM,CAAC,aAAa,cAAc,IAAU,gBAAS,CAAC;AACtD,QAAM,cAAoB,cAAO,CAAC,CAAC;AACnC,QAAM,qBAAqB,UAAU,OAAO,WAAW,aAAa,OAAO,QAAQ,CAAC;AACpF,QAAM,eAAqB,eAAQ,OAAO;AAAA,IACxC;AAAA,IACA;AAAA,EACF,IAAI,CAAC,WAAW,CAAC;AACjB,QAAM,SAAS,OAAO;AACtB,WAAS,cAAc,OAAO;AAC5B,QAAI,CAAC,QAAQ,SAAS,MAAM,GAAG;AAAG;AAClC,UAAM,WAAW,YAAY,aAAa,eAAe;AACzD,UAAM,WAAW,YAAY,aAAa,eAAe;AACzD,UAAM,YAAY;AAClB,QAAI,YAAY;AAChB,QAAI,QAAQ;AACV,kBAAY,sBAAsB,aAAa;AAAA,QAC7C;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF,CAAC;AAAA,IACH;AACA,UAAM,YAAY;AAAA,MAChB,YAAY,CAAC,WAAW;AAAA,MACxB,UAAU,CAAC,UAAU;AAAA,MACrB,MAAM,CAAC,aAAa,UAAU;AAAA,IAChC,EAAE,WAAW;AACb,UAAM,cAAc;AAAA,MAClB,YAAY,CAAC,UAAU;AAAA,MACvB,UAAU,CAAC,QAAQ;AAAA,MACnB,MAAM,CAAC,YAAY,QAAQ;AAAA,IAC7B,EAAE,WAAW;AACb,UAAM,gBAAgB,SAAS,UAAU;AAAA,MACvC,YAAY;AAAA,MACZ,UAAU;AAAA,MACV,MAAM;AAAA,IACR,EAAE,WAAW;AACb,QAAI,cAAc,eAAe,CAAC,GAAG,WAAW,GAAG,WAAW,EAAE,SAAS,MAAM,GAAG,GAAG;AACnF,UAAI,QAAQ,cAAc,YAAY,UAAU,SAAS,MAAM,GAAG,GAAG;AACnE,oBAAY;AAAA,MACd,WAAW,QAAQ,cAAc,YAAY,YAAY,SAAS,MAAM,GAAG,GAAG;AAC5E,oBAAY;AAAA,MACd,OAAO;AACL,oBAAY,qBAAqB,aAAa;AAAA,UAC5C,eAAe;AAAA,UACf,WAAW,YAAY,SAAS,MAAM,GAAG;AAAA,UACzC;AAAA,QACF,CAAC;AAAA,MACH;AAAA,IACF;AACA,QAAI,cAAc,eAAe,CAAC,mBAAmB,aAAa,SAAS,GAAG;AAC5E,YAAM,gBAAgB;AACtB,UAAI,cAAc,SAAS,MAAM,GAAG,GAAG;AACrC,cAAM,eAAe;AAAA,MACvB;AACA,qBAAe,SAAS;AAGxB,qBAAe,MAAM;AACnB,qBAAa,YAAY,QAAQ,SAAS,CAAC;AAAA,MAC7C,CAAC;AAAA,IACH;AAAA,EACF;AACA,QAAM,gBAAgB;AAAA,IACpB,GAAG;AAAA,IACH,GAAG;AAAA,IACH,KAAK;AAAA,IACL,oBAAoB,gBAAgB,SAAS,SAAY;AAAA,IACzD,UAAU,GAAG;AACX,YAAM,aAAa,OAAO,SAAS,MAAM,UAAU,CAAC;AACpD,yBAAmB,aAAa,OAAO,SAAS,mBAAmB,UAAU,CAAC;AAC9E,oBAAc,CAAC;AAAA,IACjB;AAAA,EACF;AACA,SAA0B,qBAAc,iBAAiB,UAAU;AAAA,IACjE,OAAO;AAAA,EACT,GAAsB,qBAAc,cAAc;AAAA,IAChD;AAAA,EACF,GAAG,UAAU,QAAQ,aAAa,CAAC,CAAC;AACtC,CAAC;AACD,IAAM,gBAAmC,kBAAW,SAASC,eAAc,OAAO,cAAc;AAC9F,MAAI;AAAA,IACF;AAAA,IACA,GAAG;AAAA,EACL,IAAI;AACJ,QAAM,qBAAqB,UAAU,OAAO,WAAW,aAAa,OAAO,QAAQ,CAAC;AACpF,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,EACF,IAAU,kBAAW,gBAAgB;AACrC,QAAM;AAAA,IACJ;AAAA,IACA,OAAAJ;AAAA,EACF,IAAI,YAAY;AAChB,QAAM,YAAY,aAAa,CAAC,KAAK,cAAc,mBAAmB,GAAG,CAAC;AAC1E,QAAM,WAAW,gBAAgBA;AACjC,QAAM,gBAAgB;AAAA,IACpB,GAAG;AAAA,IACH,GAAG;AAAA,IACH,KAAK;AAAA,IACL,UAAU,WAAW,IAAI;AAAA,IACzB,eAAe,WAAW,KAAK;AAAA,IAC/B,QAAQ,GAAG;AACT,YAAM,WAAW,OAAO,SAAS,MAAM,QAAQ,CAAC;AAChD,yBAAmB,WAAW,OAAO,SAAS,mBAAmB,QAAQ,CAAC;AAC1E,qBAAeA,MAAK;AAAA,IACtB;AAAA,EACF;AACA,SAAO,UAAU,QAAQ,aAAa;AACxC,CAAC;AAED,SAAS,WAAW;AAClB,aAAW,OAAO,SAAS,OAAO,OAAO,KAAK,IAAI,SAAU,QAAQ;AAClE,aAAS,IAAI,GAAG,IAAI,UAAU,QAAQ,KAAK;AACzC,UAAI,SAAS,UAAU,CAAC;AACxB,eAAS,OAAO,QAAQ;AACtB,YAAI,OAAO,UAAU,eAAe,KAAK,QAAQ,GAAG,GAAG;AACrD,iBAAO,GAAG,IAAI,OAAO,GAAG;AAAA,QAC1B;AAAA,MACF;AAAA,IACF;AACA,WAAO;AAAA,EACT;AACA,SAAO,SAAS,MAAM,MAAM,SAAS;AACvC;AAEA,IAAI,wBAAwB;AAC5B,IAAI,QAAQ;AACZ,IAAM,QAAQ,MAAM,iBAAiB;AACrC,SAAS,gBAAgB;AACvB,QAAM,CAAC,IAAI,KAAK,IAAU,gBAAS,MAAM,wBAAwB,MAAM,IAAI,MAAS;AACpF,EAAAA,OAAM,MAAM;AACV,QAAI,MAAM,MAAM;AACd,YAAM,MAAM,CAAC;AAAA,IACf;AAAA,EAEF,GAAG,CAAC,CAAC;AACL,EAAM,iBAAU,MAAM;AACpB,QAAI,CAAC,uBAAuB;AAC1B,8BAAwB;AAAA,IAC1B;AAAA,EACF,GAAG,CAAC,CAAC;AACL,SAAO;AACT;AAGA,IAAM,aAAaK,OAAmB,QAAQ,SAAS,CAAC;AAQxD,IAAM,QAAQ,cAAc;AAM5B,IAAM,gBAAmC,kBAAW,SAASC,eAAc,MAAM,KAAK;AACpF,MAAI;AAAA,IACF,SAAS;AAAA,MACP;AAAA,MACA,UAAU;AAAA,QACR;AAAA,MACF;AAAA,MACA,gBAAgB;AAAA,QACd,OAAAC;AAAA,MACF;AAAA,IACF;AAAA,IACA,QAAQ;AAAA,IACR,SAAS;AAAA,IACT,YAAY;AAAA,IACZ,cAAc;AAAA,IACd;AAAA,IACA;AAAA,IACA;AAAA,IACA,OAAO;AAAA,MACL;AAAA,MACA,GAAG;AAAA,IACL,IAAI,CAAC;AAAA,IACL,GAAG;AAAA,EACL,IAAI;AACJ,MAAI,MAAuC;AACzC,QAAI,CAAC,KAAK;AACR,cAAQ,KAAK,mEAAmE,YAAY;AAAA,IAC9F;AAAA,EACF;AACA,QAAM,aAAa,MAAM;AACzB,MAAI,CAAC,UAAU;AACb,WAAO;AAAA,EACT;AAIA,iBAAe;AACf,QAAM,kBAAkB,cAAc;AACtC,QAAM,OAAO,QAAQ,KAAK,YAAY,KAAK;AAC3C,QAAM,OAAO,SAAS,IAAI,YAAY;AACtC,QAAM,CAAC,MAAM,SAAS,IAAI,UAAU,MAAM,GAAG;AAC7C,QAAMC,SAAQ,SAAS,MAAM,QAAQ;AACrC,QAAM,gBAAgB,CAAC,CAAC;AACxB,QAAM,iBAAiB,SAAS,SAAS,SAAS;AAClD,QAAM,cAAc,gBAAgB,cAAc,QAAQ,WAAW;AACrE,MAAI,cAAc,gBAAgB,cAAc,QAAQ,UAAU;AAClE,MAAI,gBAAgBA,QAAO;AACzB,kBAAc,cAAc,QAAQ,SAAS;AAAA,EAC/C;AACA,QAAM,UAAUD,UAAS,OAAO,SAASA,OAAM,MAAM,OAAO,gBAAgBA,OAAM,IAAI;AACtF,QAAM,UAAUA,UAAS,OAAO,SAASA,OAAM,MAAM,OAAO,gBAAgBA,OAAM,IAAI;AACtF,QAAM,SAAS,KAAK,UAAU,OAAO,UAAU,QAAQ,QAAQ,QAAQ,OAAO,SAAS,UAAU,OAAO,QAAQ,IAAI,MAAM,SAAS,MAAM,OAAO,OAAO,SAAS,SAAS;AACzK,QAAM,WAAW;AAAA,IACf,KAAK,gBAAgB,mBAAmB;AAAA,IACxC,MAAM,gBAAgB,kBAAkB;AAAA,IACxC,QAAQ,gBAAgB,KAAK;AAAA,IAC7B,OAAO,gBAAgB,mBAAmB;AAAA,EAC5C,EAAE,IAAI;AACN,SAA0B,qBAAc,OAAO,SAAS,CAAC,GAAG,MAAM;AAAA,IAChE,eAAe;AAAA,IACf;AAAA,IACA,OAAO,gBAAgB,QAAQ,QAAQ;AAAA,IACvC,QAAQ;AAAA,IACR,SAAS,SAAS,QAAQ,OAAO,SAAS,QAAQ,SAAS;AAAA,IAC3D,OAAO;AAAA,MACL,UAAU;AAAA,MACV,eAAe;AAAA,MACf,CAAC,WAAW,GAAG;AAAA,MACf,CAAC,WAAW,GAAG;AAAA,MACf,CAAC,IAAI,GAAG,kBAAkB,gBAAgB,SAAS,iBAAiB,cAAc,IAAI;AAAA,MACtF,WAAW,KAAK,YAAY,aAAa,OAAO,YAAY;AAAA,MAC5D,GAAG;AAAA,IACL;AAAA,EACF,CAAC,GAAG,cAAc,KAAwB,qBAAc,QAAQ;AAAA,IAC9D,UAAU,UAAU,aAAa;AAAA,IACjC,MAAM;AAAA,IACN;AAAA,IAGA,aAAa,eAAe,IAAI,IAAI;AAAA,IACpC,GAAG;AAAA,EACL,CAAC,GAAsB,qBAAc,QAAQ;AAAA,IAC3C,QAAQ,eAAe,CAAC,IAAI,KAAK,OAAO;AAAA,IACxC,GAAG;AAAA,EACL,CAAC,GAAsB,qBAAc,YAAY;AAAA,IAC/C,IAAI;AAAA,EACN,GAAsB,qBAAc,QAAQ;AAAA,IAC1C,GAAG,CAAC;AAAA,IACJ,GAAG,mBAAmB,gBAAgB,KAAK;AAAA,IAC3C,OAAO,QAAQ;AAAA,IACf,QAAQ;AAAA,EACV,CAAC,CAAC,CAAC;AACL,CAAC;AAED,SAAS,eAAe;AACtB,QAAM,MAAM,oBAAI,IAAI;AACpB,SAAO;AAAA,IACL,KAAK,OAAO,MAAM;AAChB,UAAI;AACJ,OAAC,WAAW,IAAI,IAAI,KAAK,MAAM,OAAO,SAAS,SAAS,QAAQ,aAAW,QAAQ,IAAI,CAAC;AAAA,IAC1F;AAAA,IACA,GAAG,OAAO,UAAU;AAClB,UAAI,IAAI,OAAO,CAAC,GAAI,IAAI,IAAI,KAAK,KAAK,CAAC,GAAI,QAAQ,CAAC;AAAA,IACtD;AAAA,IACA,IAAI,OAAO,UAAU;AACnB,UAAI;AACJ,UAAI,IAAI,SAAS,YAAY,IAAI,IAAI,KAAK,MAAM,OAAO,SAAS,UAAU,OAAO,OAAK,MAAM,QAAQ,MAAM,CAAC,CAAC;AAAA,IAC9G;AAAA,EACF;AACF;AAEA,IAAM,sBAAyC,qBAAc,IAAI;AACjE,IAAM,sBAAyC,qBAAc,IAAI;AACjE,IAAM,0BAA0B,MAAM;AACpC,MAAI;AACJ,WAAS,oBAA0B,kBAAW,mBAAmB,MAAM,OAAO,SAAS,kBAAkB,OAAO;AAClH;AACA,IAAM,kBAAkB,MAAY,kBAAW,mBAAmB;AAKlE,SAAS,kBAAkB,gBAAgB;AACzC,QAAM,KAAK,MAAM;AACjB,QAAM,OAAO,gBAAgB;AAC7B,QAAM,gBAAgB,wBAAwB;AAC9C,QAAM,WAAW,kBAAkB;AACnC,EAAAP,OAAM,MAAM;AACV,UAAM,OAAO;AAAA,MACX;AAAA,MACA;AAAA,IACF;AACA,YAAQ,OAAO,SAAS,KAAK,QAAQ,IAAI;AACzC,WAAO,MAAM;AACX,cAAQ,OAAO,SAAS,KAAK,WAAW,IAAI;AAAA,IAC9C;AAAA,EACF,GAAG,CAAC,MAAM,IAAI,QAAQ,CAAC;AACvB,SAAO;AACT;AAMA,SAAS,aAAa,MAAM;AAC1B,MAAI;AAAA,IACF;AAAA,IACA;AAAA,EACF,IAAI;AACJ,QAAM,WAAW,wBAAwB;AACzC,SAA0B,qBAAc,oBAAoB,UAAU;AAAA,IACpE,OAAa,eAAQ,OAAO;AAAA,MAC1B;AAAA,MACA;AAAA,IACF,IAAI,CAAC,IAAI,QAAQ,CAAC;AAAA,EACpB,GAAG,QAAQ;AACb;AAQA,SAAS,aAAa,OAAO;AAC3B,MAAI;AAAA,IACF;AAAA,EACF,IAAI;AACJ,QAAM,WAAiB,cAAO,CAAC,CAAC;AAChC,QAAM,UAAgB,mBAAY,UAAQ;AACxC,aAAS,UAAU,CAAC,GAAG,SAAS,SAAS,IAAI;AAAA,EAC/C,GAAG,CAAC,CAAC;AACL,QAAM,aAAmB,mBAAY,UAAQ;AAC3C,aAAS,UAAU,SAAS,QAAQ,OAAO,OAAK,MAAM,IAAI;AAAA,EAC5D,GAAG,CAAC,CAAC;AACL,QAAM,SAAe,gBAAS,MAAM,aAAa,CAAC,EAAE,CAAC;AACrD,SAA0B,qBAAc,oBAAoB,UAAU;AAAA,IACpE,OAAa,eAAQ,OAAO;AAAA,MAC1B;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF,IAAI,CAAC,UAAU,SAAS,YAAY,MAAM,CAAC;AAAA,EAC7C,GAAG,QAAQ;AACb;AAEA,SAAS,gBAAgB,MAAM;AAC7B,SAAO,sBAAsB;AAC/B;AAEA,SAASS,cAAa,OAAO;AAC3B,QAAM,UAAM,sBAAO,KAAK;AACxB,EAAAT,OAAM,MAAM;AACV,QAAI,UAAU;AAAA,EAChB,CAAC;AACD,SAAO;AACT;AAEA,IAAM,wBAAqC,gBAAgB,cAAc;AACzE,SAAS,SAAS,OAAO,MAAM,aAAa;AAC1C,MAAI,eAAe,CAAC,uBAAuB,WAAW,GAAG;AACvD,WAAO;AAAA,EACT;AACA,MAAI,OAAO,UAAU,UAAU;AAC7B,WAAO;AAAA,EACT;AACA,SAAO,SAAS,OAAO,SAAS,MAAM,IAAI;AAC5C;AAMA,SAAS,SAAS,SAAS,OAAO;AAChC,MAAI,UAAU,QAAQ;AACpB,YAAQ,CAAC;AAAA,EACX;AACA,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA,UAAU;AAAA,MACR;AAAA,MACA;AAAA,IACF;AAAA,IACA;AAAA,EACF,IAAI;AACJ,QAAM;AAAA,IACJ,UAAU;AAAA,IACV,QAAQ;AAAA,IACR,cAAc;AAAA,IACd,YAAY;AAAA,IACZ,SAAS;AAAA,IACT,OAAO;AAAA,EACT,IAAI;AACJ,QAAM,OAAO,gBAAgB;AAC7B,QAAM,WAAW,wBAAwB;AACzC,QAAM,iBAAiBS,cAAa,WAAW;AAC/C,QAAM,WAAWA,cAAa,KAAK;AACnC,QAAM,iBAAuB,cAAO;AACpC,QAAM,aAAmB,cAAO;AAChC,QAAM,aAAmB,cAAO;AAChC,QAAM,iBAAuB,cAAO;AACpC,QAAM,oBAA0B,cAAO,IAAI;AAC3C,QAAM,oCAA0C,cAAO,KAAK;AAC5D,QAAM,qBAA2B,cAAO,MAAM;AAAA,EAAC,CAAC;AAChD,QAAM,cAAoB,mBAAY,MAAM;AAC1C,QAAI;AACJ,UAAM,QAAQ,wBAAwB,QAAQ,QAAQ,cAAc,OAAO,SAAS,sBAAsB;AAC1G,YAAQ,QAAQ,OAAO,SAAS,KAAK,SAAS,OAAO,MAAM,SAAS;AAAA,EACtE,GAAG,CAAC,OAAO,CAAC;AAIZ,EAAM,iBAAU,MAAM;AACpB,QAAI,CAAC,SAAS;AACZ;AAAA,IACF;AACA,aAAS,YAAY;AACnB,mBAAa,WAAW,OAAO;AAC/B,mBAAa,eAAe,OAAO;AACnC,wBAAkB,UAAU;AAAA,IAC9B;AACA,WAAO,GAAG,WAAW,SAAS;AAC9B,WAAO,MAAM;AACX,aAAO,IAAI,WAAW,SAAS;AAAA,IACjC;AAAA,EACF,GAAG,CAAC,SAAS,MAAM,CAAC;AACpB,EAAM,iBAAU,MAAM;AACpB,QAAI,CAAC,WAAW,CAAC,eAAe,WAAW,CAAC,MAAM;AAChD;AAAA,IACF;AACA,aAAS,QAAQ,OAAO;AACtB,UAAI,YAAY,GAAG;AACjB,qBAAa,OAAO,KAAK;AAAA,MAC3B;AAAA,IACF;AACA,UAAM,OAAO,YAAY,QAAQ,EAAE;AACnC,SAAK,iBAAiB,cAAc,OAAO;AAC3C,WAAO,MAAM;AACX,WAAK,oBAAoB,cAAc,OAAO;AAAA,IAChD;AAAA,EACF,GAAG,CAAC,UAAU,MAAM,cAAc,SAAS,gBAAgB,SAAS,WAAW,CAAC;AAChF,QAAM,iBAAuB,mBAAY,SAAU,OAAO,eAAe;AACvE,QAAI,kBAAkB,QAAQ;AAC5B,sBAAgB;AAAA,IAClB;AACA,UAAM,aAAa,SAAS,SAAS,SAAS,SAAS,eAAe,OAAO;AAC7E,QAAI,cAAc,CAAC,WAAW,SAAS;AACrC,mBAAa,WAAW,OAAO;AAC/B,iBAAW,UAAU,WAAW,MAAM,aAAa,OAAO,KAAK,GAAG,UAAU;AAAA,IAC9E,WAAW,eAAe;AACxB,mBAAa,WAAW,OAAO;AAC/B,mBAAa,OAAO,KAAK;AAAA,IAC3B;AAAA,EACF,GAAG,CAAC,UAAU,YAAY,CAAC;AAC3B,QAAM,0BAAgC,mBAAY,MAAM;AACtD,uBAAmB,QAAQ;AAC3B,eAAW,UAAU;AAAA,EACvB,GAAG,CAAC,CAAC;AACL,QAAM,qBAA2B,mBAAY,MAAM;AACjD,QAAI,kCAAkC,SAAS;AAC7C,YAAM,OAAO,YAAY,KAAK,SAAS,OAAO,EAAE;AAChD,WAAK,MAAM,gBAAgB;AAC3B,WAAK,gBAAgB,qBAAqB;AAC1C,wCAAkC,UAAU;AAAA,IAC9C;AAAA,EACF,GAAG,CAAC,IAAI,CAAC;AAKT,EAAM,iBAAU,MAAM;AACpB,QAAI,CAAC,SAAS;AACZ;AAAA,IACF;AACA,aAAS,uBAAuB;AAC9B,aAAO,QAAQ,QAAQ,YAAY,CAAC,SAAS,WAAW,EAAE,SAAS,QAAQ,QAAQ,UAAU,IAAI,IAAI;AAAA,IACvG;AACA,aAAS,aAAa,OAAO;AAC3B,mBAAa,WAAW,OAAO;AAC/B,wBAAkB,UAAU;AAC5B,UAAI,aAAa,CAAC,uBAAuB,eAAe,OAAO,KAAK,SAAS,KAAK,SAAS,SAAS,SAAS,MAAM,MAAM,GAAG;AAC1H;AAAA,MACF;AACA,YAAM,YAAY,SAAS,SAAS,SAAS,QAAQ,eAAe,OAAO;AAC3E,UAAI,WAAW;AACb,mBAAW,UAAU,WAAW,MAAM;AACpC,uBAAa,MAAM,KAAK;AAAA,QAC1B,GAAG,SAAS;AAAA,MACd,OAAO;AACL,qBAAa,MAAM,KAAK;AAAA,MAC1B;AAAA,IACF;AACA,aAAS,aAAa,OAAO;AAC3B,UAAI,qBAAqB,GAAG;AAC1B;AAAA,MACF;AACA,yBAAmB,QAAQ;AAC3B,YAAM,MAAM,YAAY,QAAQ;AAChC,mBAAa,eAAe,OAAO;AACnC,UAAI,eAAe,SAAS;AAE1B,YAAI,CAAC,MAAM;AACT,uBAAa,WAAW,OAAO;AAAA,QACjC;AACA,mBAAW,UAAU,eAAe,QAAQ;AAAA,UAC1C,GAAG;AAAA,UACH;AAAA,UACA,GAAG,MAAM;AAAA,UACT,GAAG,MAAM;AAAA,UACT,UAAU;AACR,+BAAmB;AACnB,oCAAwB;AAExB,2BAAe,KAAK;AAAA,UACtB;AAAA,QACF,CAAC;AACD,cAAM,UAAU,WAAW;AAC3B,YAAI,iBAAiB,aAAa,OAAO;AACzC,2BAAmB,UAAU,MAAM;AACjC,cAAI,oBAAoB,aAAa,OAAO;AAAA,QAC9C;AACA;AAAA,MACF;AAKA,YAAM,cAAc,eAAe,YAAY,UAAU,CAAC,SAAS,UAAU,MAAM,aAAa,IAAI;AACpG,UAAI,aAAa;AACf,uBAAe,KAAK;AAAA,MACtB;AAAA,IACF;AAKA,aAAS,mBAAmB,OAAO;AACjC,UAAI,qBAAqB,GAAG;AAC1B;AAAA,MACF;AACA,qBAAe,WAAW,OAAO,SAAS,eAAe,QAAQ;AAAA,QAC/D,GAAG;AAAA,QACH;AAAA,QACA,GAAG,MAAM;AAAA,QACT,GAAG,MAAM;AAAA,QACT,UAAU;AACR,6BAAmB;AACnB,kCAAwB;AACxB,yBAAe,KAAK;AAAA,QACtB;AAAA,MACF,CAAC,EAAE,KAAK;AAAA,IACV;AACA,QAAI,UAAU,YAAY,GAAG;AAC3B,YAAM,MAAM;AACZ,cAAQ,IAAI,iBAAiB,cAAc,kBAAkB;AAC7D,kBAAY,OAAO,SAAS,SAAS,iBAAiB,cAAc,kBAAkB;AACtF,cAAQ,IAAI,iBAAiB,aAAa,cAAc;AAAA,QACtD,MAAM;AAAA,MACR,CAAC;AACD,UAAI,iBAAiB,cAAc,YAAY;AAC/C,UAAI,iBAAiB,cAAc,YAAY;AAC/C,aAAO,MAAM;AACX,gBAAQ,IAAI,oBAAoB,cAAc,kBAAkB;AAChE,oBAAY,OAAO,SAAS,SAAS,oBAAoB,cAAc,kBAAkB;AACzF,gBAAQ,IAAI,oBAAoB,aAAa,YAAY;AACzD,YAAI,oBAAoB,cAAc,YAAY;AAClD,YAAI,oBAAoB,cAAc,YAAY;AAAA,MACpD;AAAA,IACF;AAAA,EACF,GAAG,CAAC,cAAc,UAAU,SAAS,SAAS,WAAW,QAAQ,MAAM,gBAAgB,yBAAyB,oBAAoB,cAAc,MAAM,MAAM,UAAU,gBAAgB,OAAO,CAAC;AAMhM,EAAAT,OAAM,MAAM;AACV,QAAI;AACJ,QAAI,CAAC,SAAS;AACZ;AAAA,IACF;AACA,QAAI,SAAS,wBAAwB,eAAe,YAAY,QAAQ,sBAAsB,UAAU,sBAAsB,YAAY,GAAG;AAC3I,YAAM,OAAO,YAAY,QAAQ,EAAE;AACnC,WAAK,aAAa,uBAAuB,EAAE;AAC3C,WAAK,MAAM,gBAAgB;AAC3B,wCAAkC,UAAU;AAC5C,UAAI,UAAU,YAAY,KAAK,UAAU;AACvC,YAAI,uBAAuB;AAC3B,cAAM,MAAM;AACZ,cAAM,iBAAiB,QAAQ,OAAO,UAAU,wBAAwB,KAAK,SAAS,QAAQ,KAAK,UAAQ,KAAK,OAAO,QAAQ,MAAM,OAAO,UAAU,yBAAyB,sBAAsB,YAAY,OAAO,SAAS,uBAAuB,SAAS;AACjQ,YAAI,gBAAgB;AAClB,yBAAe,MAAM,gBAAgB;AAAA,QACvC;AACA,YAAI,MAAM,gBAAgB;AAC1B,iBAAS,MAAM,gBAAgB;AAC/B,eAAO,MAAM;AACX,cAAI,MAAM,gBAAgB;AAC1B,mBAAS,MAAM,gBAAgB;AAAA,QACjC;AAAA,MACF;AAAA,IACF;AAAA,EACF,GAAG,CAAC,SAAS,MAAM,UAAU,UAAU,cAAc,MAAM,gBAAgB,SAAS,WAAW,CAAC;AAChG,EAAAA,OAAM,MAAM;AACV,QAAI,CAAC,MAAM;AACT,qBAAe,UAAU;AACzB,8BAAwB;AACxB,yBAAmB;AAAA,IACrB;AAAA,EACF,GAAG,CAAC,MAAM,yBAAyB,kBAAkB,CAAC;AACtD,EAAM,iBAAU,MAAM;AACpB,WAAO,MAAM;AACX,8BAAwB;AACxB,mBAAa,WAAW,OAAO;AAC/B,mBAAa,eAAe,OAAO;AACnC,yBAAmB;AAAA,IACrB;AAAA,EACF,GAAG,CAAC,SAAS,cAAc,yBAAyB,kBAAkB,CAAC;AACvE,SAAa,eAAQ,MAAM;AACzB,QAAI,CAAC,SAAS;AACZ,aAAO,CAAC;AAAA,IACV;AACA,aAAS,cAAc,OAAO;AAC5B,qBAAe,UAAU,MAAM;AAAA,IACjC;AACA,WAAO;AAAA,MACL,WAAW;AAAA,QACT,eAAe;AAAA,QACf,gBAAgB;AAAA,QAChB,YAAY,OAAO;AACjB,cAAI,QAAQ,WAAW,GAAG;AACxB;AAAA,UACF;AACA,uBAAa,eAAe,OAAO;AACnC,yBAAe,UAAU,WAAW,MAAM;AACxC,gBAAI,CAAC,kBAAkB,SAAS;AAC9B,2BAAa,MAAM,MAAM,WAAW;AAAA,YACtC;AAAA,UACF,GAAG,MAAM;AAAA,QACX;AAAA,MACF;AAAA,MACA,UAAU;AAAA,QACR,eAAe;AACb,uBAAa,WAAW,OAAO;AAAA,QACjC;AAAA,QACA,aAAa,OAAO;AAClB,iBAAO,KAAK,WAAW;AAAA,YACrB,MAAM;AAAA,YACN,MAAM;AAAA,cACJ,aAAa;AAAA,YACf;AAAA,UACF,CAAC;AACD,yBAAe,MAAM,aAAa,KAAK;AAAA,QACzC;AAAA,MACF;AAAA,IACF;AAAA,EACF,GAAG,CAAC,QAAQ,SAAS,QAAQ,MAAM,cAAc,cAAc,CAAC;AAClE;AAEA,IAAM,4BAA+C,qBAAc;AAAA,EACjE,OAAO;AAAA,EACP,cAAc;AAAA,EACd,WAAW;AAAA,EACX,WAAW;AAAA,EACX,cAAc,MAAM;AAAA,EAAC;AAAA,EACrB,UAAU,MAAM;AAAA,EAAC;AAAA,EACjB,gBAAgB;AAClB,CAAC;AACD,IAAM,uBAAuB,MAAY,kBAAW,yBAAyB;AAM7E,IAAM,qBAAqB,UAAQ;AACjC,MAAI;AAAA,IACF;AAAA,IACA;AAAA,IACA,YAAY;AAAA,EACd,IAAI;AACJ,QAAM,CAAC,OAAO,QAAQ,IAAU,kBAAW,CAAC,MAAM,UAAU;AAAA,IAC1D,GAAG;AAAA,IACH,GAAG;AAAA,EACL,IAAI;AAAA,IACF;AAAA,IACA;AAAA,IACA,cAAc;AAAA,IACd,WAAW;AAAA,IACX,gBAAgB;AAAA,EAClB,CAAC;AACD,QAAM,sBAA4B,cAAO,IAAI;AAC7C,QAAM,eAAqB,mBAAY,eAAa;AAClD,aAAS;AAAA,MACP;AAAA,IACF,CAAC;AAAA,EACH,GAAG,CAAC,CAAC;AACL,EAAAA,OAAM,MAAM;AACV,QAAI,MAAM,WAAW;AACnB,UAAI,oBAAoB,YAAY,MAAM;AACxC,4BAAoB,UAAU,MAAM;AAAA,MACtC,OAAO;AACL,iBAAS;AAAA,UACP,gBAAgB;AAAA,QAClB,CAAC;AAAA,MACH;AAAA,IACF,OAAO;AACL,eAAS;AAAA,QACP,gBAAgB;AAAA,MAClB,CAAC;AACD,0BAAoB,UAAU;AAAA,IAChC;AAAA,EACF,GAAG,CAAC,MAAM,SAAS,CAAC;AACpB,SAA0B,qBAAc,0BAA0B,UAAU;AAAA,IAC1E,OAAa,eAAQ,OAAO;AAAA,MAC1B,GAAG;AAAA,MACH;AAAA,MACA;AAAA,IACF,IAAI,CAAC,OAAO,UAAU,YAAY,CAAC;AAAA,EACrC,GAAG,QAAQ;AACb;AACA,IAAM,gBAAgB,CAAC,OAAO,UAAU;AACtC,MAAI;AAAA,IACF;AAAA,IACA;AAAA,EACF,IAAI;AACJ,MAAI;AAAA,IACF;AAAA,EACF,IAAI;AACJ,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,IAAI,qBAAqB;AACzB,EAAAA,OAAM,MAAM;AACV,QAAI,WAAW;AACb,eAAS;AAAA,QACP,OAAO;AAAA,UACL,MAAM;AAAA,UACN,OAAO,SAAS,cAAc,OAAO;AAAA,QACvC;AAAA,MACF,CAAC;AACD,UAAI,cAAc,IAAI;AACpB,qBAAa,KAAK;AAAA,MACpB;AAAA,IACF;AAAA,EACF,GAAG,CAAC,IAAI,cAAc,UAAU,WAAW,YAAY,CAAC;AACxD,EAAAA,OAAM,MAAM;AACV,aAAS,QAAQ;AACf,mBAAa,KAAK;AAClB,eAAS;AAAA,QACP,OAAO;AAAA,QACP,WAAW;AAAA,MACb,CAAC;AAAA,IACH;AACA,QAAI,CAAC,QAAQ,cAAc,IAAI;AAC7B,UAAI,WAAW;AACb,cAAM,UAAU,OAAO,WAAW,OAAO,SAAS;AAClD,eAAO,MAAM;AACX,uBAAa,OAAO;AAAA,QACtB;AAAA,MACF,OAAO;AACL,cAAM;AAAA,MACR;AAAA,IACF;AAAA,EACF,GAAG,CAAC,MAAM,UAAU,WAAW,IAAI,cAAc,cAAc,SAAS,CAAC;AACzE,EAAAA,OAAM,MAAM;AACV,QAAI,MAAM;AACR,mBAAa,EAAE;AAAA,IACjB;AAAA,EACF,GAAG,CAAC,MAAM,cAAc,EAAE,CAAC;AAC7B;AAEA,SAAS,aAAa,OAAO,IAAI;AAC/B,MAAI;AACJ,MAAI,eAAe,CAAC;AACpB,MAAI,mBAAmB,cAAc,MAAM,KAAK,UAAQ,KAAK,OAAO,EAAE,MAAM,OAAO,SAAS,YAAY;AACxG,SAAO,iBAAiB;AACtB,UAAM,cAAc,MAAM,KAAK,UAAQ,KAAK,OAAO,eAAe;AAClE,sBAAkB,eAAe,OAAO,SAAS,YAAY;AAC7D,QAAI,aAAa;AACf,qBAAe,aAAa,OAAO,WAAW;AAAA,IAChD;AAAA,EACF;AACA,SAAO;AACT;AAEA,SAAS,YAAY,OAAO,IAAI;AAC9B,MAAI,cAAc,MAAM,OAAO,UAAQ;AACrC,QAAI;AACJ,WAAO,KAAK,aAAa,QAAQ,gBAAgB,KAAK,YAAY,OAAO,SAAS,cAAc;AAAA,EAClG,CAAC;AACD,MAAI,kBAAkB;AACtB,SAAO,gBAAgB,QAAQ;AAC7B,sBAAkB,MAAM,OAAO,UAAQ;AACrC,UAAI;AACJ,cAAQ,mBAAmB,oBAAoB,OAAO,SAAS,iBAAiB,KAAK,OAAK;AACxF,YAAI;AACJ,eAAO,KAAK,aAAa,EAAE,QAAQ,iBAAiB,KAAK,YAAY,OAAO,SAAS,eAAe;AAAA,MACtG,CAAC;AAAA,IACH,CAAC;AACD,kBAAc,YAAY,OAAO,eAAe;AAAA,EAClD;AACA,SAAO;AACT;AACA,SAAS,eAAe,OAAO,IAAI;AACjC,MAAI;AACJ,MAAI,WAAW;AACf,WAAS,YAAY,QAAQ,OAAO;AAClC,QAAI,QAAQ,UAAU;AACpB,sBAAgB;AAChB,iBAAW;AAAA,IACb;AACA,UAAM,WAAW,YAAY,OAAO,MAAM;AAC1C,aAAS,QAAQ,WAAS;AACxB,kBAAY,MAAM,IAAI,QAAQ,CAAC;AAAA,IACjC,CAAC;AAAA,EACH;AACA,cAAY,IAAI,CAAC;AACjB,SAAO,MAAM,KAAK,UAAQ,KAAK,OAAO,aAAa;AACrD;AAIA,IAAI,aAA0B,oBAAI,QAAQ;AAC1C,IAAI,0BAAuC,oBAAI,QAAQ;AACvD,IAAI,YAAY,CAAC;AACjB,IAAI,YAAY;AAChB,IAAM,gBAAgB,MAAM,OAAO,gBAAgB,eAAe,WAAW,YAAY;AACzF,IAAM,aAAa,UAAQ,SAAS,KAAK,QAAQ,WAAW,KAAK,UAAU;AAC3E,IAAM,kBAAkB,CAAC,QAAQ,YAAY,QAAQ,IAAI,YAAU;AACjE,MAAI,OAAO,SAAS,MAAM,GAAG;AAC3B,WAAO;AAAA,EACT;AACA,QAAM,kBAAkB,WAAW,MAAM;AACzC,MAAI,OAAO,SAAS,eAAe,GAAG;AACpC,WAAO;AAAA,EACT;AACA,SAAO;AACT,CAAC,EAAE,OAAO,OAAK,KAAK,IAAI;AACxB,SAAS,uBAAuB,0BAA0B,MAAM,YAAY,OAAO;AACjF,QAAM,aAAa;AACnB,QAAM,mBAAmB,QAAQ,UAAU,aAAa,gBAAgB;AACxE,QAAM,gBAAgB,gBAAgB,MAAM,wBAAwB;AACpE,QAAM,iBAAiB,oBAAI,IAAI;AAC/B,QAAM,iBAAiB,IAAI,IAAI,aAAa;AAC5C,QAAM,iBAAiB,CAAC;AACxB,MAAI,CAAC,UAAU,UAAU,GAAG;AAC1B,cAAU,UAAU,IAAI,oBAAI,QAAQ;AAAA,EACtC;AACA,QAAM,gBAAgB,UAAU,UAAU;AAC1C,gBAAc,QAAQ,IAAI;AAC1B,OAAK,IAAI;AACT,iBAAe,MAAM;AACrB,WAAS,KAAK,IAAI;AAChB,QAAI,CAAC,MAAM,eAAe,IAAI,EAAE,GAAG;AACjC;AAAA,IACF;AACA,mBAAe,IAAI,EAAE;AACrB,OAAG,cAAc,KAAK,GAAG,UAAU;AAAA,EACrC;AACA,WAAS,KAAK,QAAQ;AACpB,QAAI,CAAC,UAAU,eAAe,IAAI,MAAM,GAAG;AACzC;AAAA,IACF;AACA,UAAM,UAAU,QAAQ,KAAK,OAAO,UAAU,UAAQ;AACpD,UAAI,eAAe,IAAI,IAAI,GAAG;AAC5B,aAAK,IAAI;AAAA,MACX,OAAO;AACL,cAAM,OAAO,mBAAmB,KAAK,aAAa,gBAAgB,IAAI;AACtE,cAAM,gBAAgB,SAAS,QAAQ,SAAS;AAChD,cAAM,gBAAgB,WAAW,IAAI,IAAI,KAAK,KAAK;AACnD,cAAM,eAAe,cAAc,IAAI,IAAI,KAAK,KAAK;AACrD,mBAAW,IAAI,MAAM,YAAY;AACjC,sBAAc,IAAI,MAAM,WAAW;AACnC,uBAAe,KAAK,IAAI;AACxB,YAAI,iBAAiB,KAAK,eAAe;AACvC,kCAAwB,IAAI,IAAI;AAAA,QAClC;AACA,YAAI,gBAAgB,GAAG;AACrB,eAAK,aAAa,YAAY,EAAE;AAAA,QAClC;AACA,YAAI,CAAC,iBAAiB,kBAAkB;AACtC,eAAK,aAAa,kBAAkB,MAAM;AAAA,QAC5C;AAAA,MACF;AAAA,IACF,CAAC;AAAA,EACH;AACA;AACA,SAAO,MAAM;AACX,mBAAe,QAAQ,aAAW;AAChC,YAAM,gBAAgB,WAAW,IAAI,OAAO,KAAK,KAAK;AACtD,YAAM,eAAe,cAAc,IAAI,OAAO,KAAK,KAAK;AACxD,iBAAW,IAAI,SAAS,YAAY;AACpC,oBAAc,IAAI,SAAS,WAAW;AACtC,UAAI,CAAC,cAAc;AACjB,YAAI,CAAC,wBAAwB,IAAI,OAAO,KAAK,kBAAkB;AAC7D,kBAAQ,gBAAgB,gBAAgB;AAAA,QAC1C;AACA,gCAAwB,OAAO,OAAO;AAAA,MACxC;AACA,UAAI,CAAC,aAAa;AAChB,gBAAQ,gBAAgB,UAAU;AAAA,MACpC;AAAA,IACF,CAAC;AACD;AACA,QAAI,CAAC,WAAW;AACd,mBAAa,oBAAI,QAAQ;AACzB,mBAAa,oBAAI,QAAQ;AACzB,gCAA0B,oBAAI,QAAQ;AACtC,kBAAY,CAAC;AAAA,IACf;AAAA,EACF;AACF;AACA,SAAS,WAAW,eAAe,YAAY,OAAO;AACpD,MAAI,eAAe,QAAQ;AACzB,iBAAa;AAAA,EACf;AACA,MAAI,UAAU,QAAQ;AACpB,YAAQ;AAAA,EACV;AACA,QAAM,OAAO,YAAY,cAAc,CAAC,CAAC,EAAE;AAC3C,SAAO,uBAAuB,cAAc,OAAO,MAAM,KAAK,KAAK,iBAAiB,aAAa,CAAC,CAAC,GAAG,MAAM,YAAY,KAAK;AAC/H;AAEA,IAAM,qBAAqB,OAAO;AAAA,EAChC,eAAe;AAAA,EACf;AAAA;AAAA;AAAA;AAAA,IAIA,OAAO,mBAAmB,cAAc,eAAe,SAAS,EAAE,SAAS,eAAe,IAAI,SAAS;AAAA;AACzG;AACA,SAAS,cAAc,WAAW,WAAW;AAC3C,QAAM,cAAc,SAAS,WAAW,mBAAmB,CAAC;AAC5D,MAAI,cAAc,QAAQ;AACxB,gBAAY,QAAQ;AAAA,EACtB;AACA,QAAM,cAAc,YAAY,QAAQ,cAAc,YAAY,SAAS,CAAC,CAAC;AAC7E,QAAM,uBAAuB,YAAY,MAAM,cAAc,CAAC;AAC9D,SAAO,qBAAqB,CAAC;AAC/B;AACA,SAAS,kBAAkB;AACzB,SAAO,cAAc,SAAS,MAAM,MAAM;AAC5C;AACA,SAAS,sBAAsB;AAC7B,SAAO,cAAc,SAAS,MAAM,MAAM;AAC5C;AACA,SAAS,eAAe,OAAO,WAAW;AACxC,QAAM,mBAAmB,aAAa,MAAM;AAC5C,QAAM,gBAAgB,MAAM;AAC5B,SAAO,CAAC,iBAAiB,CAAC,SAAS,kBAAkB,aAAa;AACpE;AACA,SAAS,mBAAmB,WAAW;AACrC,QAAM,mBAAmB,SAAS,WAAW,mBAAmB,CAAC;AACjE,mBAAiB,QAAQ,aAAW;AAClC,YAAQ,QAAQ,WAAW,QAAQ,aAAa,UAAU,KAAK;AAC/D,YAAQ,aAAa,YAAY,IAAI;AAAA,EACvC,CAAC;AACH;AACA,SAAS,kBAAkB,WAAW;AACpC,QAAM,WAAW,UAAU,iBAAiB,iBAAiB;AAC7D,WAAS,QAAQ,aAAW;AAC1B,UAAM,WAAW,QAAQ,QAAQ;AACjC,WAAO,QAAQ,QAAQ;AACvB,QAAI,UAAU;AACZ,cAAQ,aAAa,YAAY,QAAQ;AAAA,IAC3C,OAAO;AACL,cAAQ,gBAAgB,UAAU;AAAA,IACpC;AAAA,EACF,CAAC;AACH;AAKA,IAAM,gBAAgB;AAAA,EACpB,QAAQ;AAAA,EACR,MAAM;AAAA,EACN,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,UAAU;AAAA,EACV,SAAS;AAAA,EACT,UAAU;AAAA,EACV,YAAY;AAAA,EACZ,OAAO;AAAA,EACP,KAAK;AAAA,EACL,MAAM;AACR;AACA,IAAI;AACJ,SAAS,sBAAsB,OAAO;AACpC,MAAI,MAAM,QAAQ,OAAO;AACvB,UAAM;AACN,iBAAa,SAAS;AAAA,EACxB;AACF;AACA,IAAM,aAAgC,kBAAW,SAASU,YAAW,OAAO,KAAK;AAC/E,QAAM,CAAC,MAAM,OAAO,IAAU,gBAAS;AACvC,EAAAV,OAAM,MAAM;AACV,QAAI,SAAS,GAAG;AAMd,cAAQ,QAAQ;AAAA,IAClB;AACA,aAAS,iBAAiB,WAAW,qBAAqB;AAC1D,WAAO,MAAM;AACX,eAAS,oBAAoB,WAAW,qBAAqB;AAAA,IAC/D;AAAA,EACF,GAAG,CAAC,CAAC;AACL,QAAM,YAAY;AAAA,IAChB;AAAA,IACA,UAAU;AAAA;AAAA,IAEV;AAAA,IACA,eAAe,OAAO,SAAY;AAAA,IAClC,CAAC,gBAAgB,aAAa,CAAC,GAAG;AAAA,IAClC,OAAO;AAAA,EACT;AACA,SAA0B,qBAAc,QAAQ,SAAS,CAAC,GAAG,OAAO,SAAS,CAAC;AAChF,CAAC;AAED,IAAM,gBAAmC,qBAAc,IAAI;AAC3D,SAAS,sBAAsB,OAAO;AACpC,MAAI;AAAA,IACF;AAAA,IACA;AAAA,EACF,IAAI,UAAU,SAAS,CAAC,IAAI;AAC5B,QAAM,CAAC,YAAY,aAAa,IAAU,gBAAS,IAAI;AACvD,QAAM,WAAW,MAAM;AACvB,QAAM,gBAAgB,iBAAiB;AACvC,QAAM,OAAa,eAAQ,OAAO;AAAA,IAChC;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,IAAI,CAAC,IAAI,MAAM,eAAe,QAAQ,CAAC;AACvC,QAAM,UAAgB,cAAO;AAC7B,EAAAA,OAAM,MAAM;AACV,WAAO,MAAM;AACX,oBAAc,OAAO,SAAS,WAAW,OAAO;AAAA,IAClD;AAAA,EACF,GAAG,CAAC,YAAY,IAAI,CAAC;AACrB,EAAAA,OAAM,MAAM;AACV,QAAI,QAAQ,YAAY;AAAM;AAC9B,YAAQ,UAAU;AAClB,UAAM;AAAA,MACJ,IAAAW;AAAA,MACA,MAAAC;AAAA,MACA,eAAAC;AAAA,MACA,UAAAC;AAAA,IACF,IAAI;AACJ,UAAM,iBAAiBH,MAAK,SAAS,eAAeA,GAAE,IAAI;AAC1D,UAAM,OAAO,gBAAgB,QAAQ;AACrC,QAAI,gBAAgB;AAClB,YAAM,UAAU,SAAS,cAAc,KAAK;AAC5C,cAAQ,KAAKG;AACb,cAAQ,aAAa,MAAM,EAAE;AAC7B,qBAAe,YAAY,OAAO;AAClC,oBAAc,OAAO;AAAA,IACvB,OAAO;AACL,UAAI,YAAYF,UAASC,kBAAiB,OAAO,SAASA,eAAc;AACxE,UAAI,aAAa,CAAC,UAAU,SAAS;AAAG,oBAAY,UAAU;AAC9D,kBAAY,aAAa,SAAS;AAClC,UAAI,YAAY;AAChB,UAAIF,KAAI;AACN,oBAAY,SAAS,cAAc,KAAK;AACxC,kBAAU,KAAKA;AACf,kBAAU,YAAY,SAAS;AAAA,MACjC;AACA,YAAM,UAAU,SAAS,cAAc,KAAK;AAC5C,cAAQ,KAAKG;AACb,cAAQ,aAAa,MAAM,EAAE;AAC7B,kBAAY,aAAa;AACzB,gBAAU,YAAY,OAAO;AAC7B,oBAAc,OAAO;AAAA,IACvB;AAAA,EACF,GAAG,CAAC,IAAI,CAAC;AACT,SAAO;AACT;AAMA,SAAS,eAAe,MAAM;AAC5B,MAAI;AAAA,IACF;AAAA,IACA;AAAA,IACA,OAAO;AAAA,IACP,mBAAmB;AAAA,EACrB,IAAI;AACJ,QAAM,aAAa,sBAAsB;AAAA,IACvC;AAAA,IACA;AAAA,EACF,CAAC;AACD,QAAM,CAAC,mBAAmB,oBAAoB,IAAU,gBAAS,IAAI;AACrE,QAAM,mBAAyB,cAAO,IAAI;AAC1C,QAAM,kBAAwB,cAAO,IAAI;AACzC,QAAM,kBAAwB,cAAO,IAAI;AACzC,QAAM,iBAAuB,cAAO,IAAI;AACxC,QAAM;AAAA;AAAA;AAAA,IAGN,CAAC,CAAC;AAAA,IAEF,CAAC,kBAAkB;AAAA,IAEnB,kBAAkB,QAAQ,oBAAoB,CAAC,EAAE,QAAQ;AAAA;AAGzD,EAAM,iBAAU,MAAM;AACpB,QAAI,CAAC,cAAc,CAAC,oBAAoB,qBAAqB,QAAQ,kBAAkB,OAAO;AAC5F;AAAA,IACF;AAKA,aAAS,QAAQ,OAAO;AACtB,UAAI,cAAc,eAAe,KAAK,GAAG;AACvC,cAAM,WAAW,MAAM,SAAS;AAChC,cAAM,cAAc,WAAW,oBAAoB;AACnD,oBAAY,UAAU;AAAA,MACxB;AAAA,IACF;AAGA,eAAW,iBAAiB,WAAW,SAAS,IAAI;AACpD,eAAW,iBAAiB,YAAY,SAAS,IAAI;AACrD,WAAO,MAAM;AACX,iBAAW,oBAAoB,WAAW,SAAS,IAAI;AACvD,iBAAW,oBAAoB,YAAY,SAAS,IAAI;AAAA,IAC1D;AAAA,EACF,GAAG,CAAC,YAAY,kBAAkB,qBAAqB,OAAO,SAAS,kBAAkB,KAAK,CAAC;AAC/F,SAA0B,qBAAc,cAAc,UAAU;AAAA,IAC9D,OAAa,eAAQ,OAAO;AAAA,MAC1B;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF,IAAI,CAAC,kBAAkB,UAAU,CAAC;AAAA,EACpC,GAAG,sBAAsB,cAAiC,qBAAc,YAAY;AAAA,IAClF,aAAa;AAAA,IACb,KAAK;AAAA,IACL,SAAS,WAAS;AAChB,UAAI,eAAe,OAAO,UAAU,GAAG;AACrC,YAAI;AACJ,SAAC,wBAAwB,gBAAgB,YAAY,OAAO,SAAS,sBAAsB,MAAM;AAAA,MACnG,OAAO;AACL,cAAM,eAAe,oBAAoB,MAAM,qBAAqB,OAAO,SAAS,kBAAkB,KAAK,aAAa;AACxH,wBAAgB,OAAO,SAAS,aAAa,MAAM;AAAA,MACrD;AAAA,IACF;AAAA,EACF,CAAC,GAAG,sBAAsB,cAAiC,qBAAc,QAAQ;AAAA,IAC/E,aAAa,WAAW;AAAA,IACxB,OAAO;AAAA,EACT,CAAC,GAAG,kBAA2B,gCAAa,UAAU,UAAU,GAAG,sBAAsB,cAAiC,qBAAc,YAAY;AAAA,IAClJ,aAAa;AAAA,IACb,KAAK;AAAA,IACL,SAAS,WAAS;AAChB,UAAI,eAAe,OAAO,UAAU,GAAG;AACrC,YAAI;AACJ,SAAC,wBAAwB,eAAe,YAAY,OAAO,SAAS,sBAAsB,MAAM;AAAA,MAClG,OAAO;AACL,cAAM,eAAe,gBAAgB,MAAM,qBAAqB,OAAO,SAAS,kBAAkB,KAAK,aAAa;AACpH,wBAAgB,OAAO,SAAS,aAAa,MAAM;AACnD,SAAC,qBAAqB,OAAO,SAAS,kBAAkB,qBAAqB,qBAAqB,OAAO,SAAS,kBAAkB,aAAa,OAAO,MAAM,WAAW;AAAA,MAC3K;AAAA,IACF;AAAA,EACF,CAAC,CAAC;AACJ;AACA,IAAM,mBAAmB,MAAY,kBAAW,aAAa;AAE7D,IAAM,wBAA2C,kBAAW,SAASC,uBAAsB,OAAO,KAAK;AACrG,SAA0B,qBAAc,UAAU,SAAS,CAAC,GAAG,OAAO;AAAA,IACpE,MAAM;AAAA,IACN;AAAA,IACA,UAAU;AAAA,IACV,OAAO;AAAA,EACT,CAAC,CAAC;AACJ,CAAC;AAKD,SAAS,qBAAqB,OAAO;AACnC,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,IACA,WAAW;AAAA,IACX,QAAQ,CAAC,SAAS;AAAA,IAClB,QAAQ,UAAU;AAAA,IAClB,eAAe;AAAA,IACf,cAAc;AAAA,IACd,QAAQ;AAAA,IACR,wBAAwB;AAAA,IACxB,kBAAkB;AAAA,EACpB,IAAI;AACJ,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA,UAAU;AAAA,MACR;AAAA,MACA;AAAA,IACF;AAAA,EACF,IAAI;AAGJ,QAAM,SAAS,cAAc,IAAI,UAAU;AAC3C,QAAM,WAAWN,cAAa,KAAK;AACnC,QAAM,kBAAkBA,cAAa,YAAY;AACjD,QAAM,iBAAiBA,cAAa,WAAW;AAC/C,QAAM,OAAO,gBAAgB;AAC7B,QAAM,gBAAgB,iBAAiB;AAGvC,QAAM,qBAAqB,OAAO,iBAAiB,YAAY,eAAe;AAC9E,QAAM,wBAA8B,cAAO,IAAI;AAC/C,QAAM,sBAA4B,cAAO,IAAI;AAC7C,QAAM,wBAA8B,cAAO,KAAK;AAChD,QAAM,8BAAoC,cAAO,IAAI;AACrD,QAAM,mBAAyB,cAAO,KAAK;AAC3C,QAAM,iBAAiB,iBAAiB;AAOxC,QAAM,8BAA8B,gBAAgB,aAAa,aAAa,MAAM,MAAM,cAAc,kBAAkB,YAAY,KAAK;AAC3I,QAAM,qBAA2B,mBAAY,SAAU,WAAW;AAChE,QAAI,cAAc,QAAQ;AACxB,kBAAY;AAAA,IACd;AACA,WAAO,YAAY,SAAS,WAAW,mBAAmB,CAAC,IAAI,CAAC;AAAA,EAClE,GAAG,CAAC,QAAQ,CAAC;AACb,QAAM,sBAA4B,mBAAY,eAAa;AACzD,UAAM,UAAU,mBAAmB,SAAS;AAC5C,WAAO,SAAS,QAAQ,IAAI,UAAQ;AAClC,UAAI,gBAAgB,SAAS,aAAa;AACxC,eAAO;AAAA,MACT;AACA,UAAI,YAAY,SAAS,YAAY;AACnC,eAAO;AAAA,MACT;AACA,aAAO;AAAA,IACT,CAAC,EAAE,OAAO,OAAO,EAAE,KAAK;AAAA,EAC1B,GAAG,CAAC,cAAc,UAAU,UAAU,kBAAkB,CAAC;AACzD,EAAM,iBAAU,MAAM;AACpB,QAAI,YAAY,CAAC;AAAO;AACxB,aAAS,UAAU,OAAO;AACxB,UAAI,MAAM,QAAQ,OAAO;AAEvB,YAAI,SAAS,UAAU,cAAc,YAAY,QAAQ,CAAC,CAAC,KAAK,mBAAmB,EAAE,WAAW,KAAK,CAAC,6BAA6B;AACjI,oBAAU,KAAK;AAAA,QACjB;AACA,cAAM,MAAM,oBAAoB;AAChC,cAAM,SAAS,UAAU,KAAK;AAC9B,YAAI,SAAS,QAAQ,CAAC,MAAM,eAAe,WAAW,cAAc;AAClE,oBAAU,KAAK;AACf,cAAI,MAAM,UAAU;AAClB,yBAAa,IAAI,IAAI,SAAS,CAAC,CAAC;AAAA,UAClC,OAAO;AACL,yBAAa,IAAI,CAAC,CAAC;AAAA,UACrB;AAAA,QACF;AACA,YAAI,SAAS,QAAQ,CAAC,MAAM,cAAc,WAAW,YAAY,MAAM,UAAU;AAC/E,oBAAU,KAAK;AACf,uBAAa,IAAI,CAAC,CAAC;AAAA,QACrB;AAAA,MACF;AAAA,IACF;AACA,UAAM,MAAM,YAAY,QAAQ;AAChC,QAAI,iBAAiB,WAAW,SAAS;AACzC,WAAO,MAAM;AACX,UAAI,oBAAoB,WAAW,SAAS;AAAA,IAC9C;AAAA,EACF,GAAG,CAAC,UAAU,cAAc,UAAU,OAAO,UAAU,MAAM,6BAA6B,oBAAoB,mBAAmB,CAAC;AAClI,EAAM,iBAAU,MAAM;AACpB,QAAI,YAAY,CAAC;AAAiB;AAGlC,aAAS,oBAAoB;AAC3B,uBAAiB,UAAU;AAC3B,iBAAW,MAAM;AACf,yBAAiB,UAAU;AAAA,MAC7B,CAAC;AAAA,IACH;AACA,aAAS,mBAAmB,OAAO;AACjC,YAAM,gBAAgB,MAAM;AAC5B,qBAAe,MAAM;AACnB,cAAM,uBAAuB,EAAE,SAAS,cAAc,aAAa,KAAK,SAAS,UAAU,aAAa,KAAK,SAAS,eAAe,QAAQ,KAAK,SAAS,iBAAiB,OAAO,SAAS,cAAc,YAAY,aAAa,KAAK,iBAAiB,QAAQ,cAAc,aAAa,gBAAgB,aAAa,CAAC,KAAK,SAAS,YAAY,KAAK,SAAS,SAAS,MAAM,EAAE,KAAK,UAAQ;AAC9X,cAAI,eAAe;AACnB,iBAAO,UAAU,gBAAgB,KAAK,YAAY,OAAO,SAAS,cAAc,SAAS,UAAU,aAAa,KAAK,UAAU,iBAAiB,KAAK,YAAY,OAAO,SAAS,eAAe,SAAS,cAAc,aAAa;AAAA,QACtO,CAAC,KAAK,aAAa,KAAK,SAAS,SAAS,MAAM,EAAE,KAAK,UAAQ;AAC7D,cAAI,gBAAgB;AACpB,mBAAS,iBAAiB,KAAK,YAAY,OAAO,SAAS,eAAe,SAAS,cAAc,mBAAmB,iBAAiB,KAAK,YAAY,OAAO,SAAS,eAAe,SAAS,kBAAkB;AAAA,QAClN,CAAC;AAID,YAAI,iBAAiB,wBAAwB,CAAC,iBAAiB;AAAA,QAE/D,kBAAkB,4BAA4B,SAAS;AACrD,gCAAsB,UAAU;AAChC,uBAAa,OAAO,KAAK;AAAA,QAC3B;AAAA,MACF,CAAC;AAAA,IACH;AACA,QAAI,YAAY,cAAc,YAAY,GAAG;AAC3C,mBAAa,iBAAiB,YAAY,kBAAkB;AAC5D,mBAAa,iBAAiB,eAAe,iBAAiB;AAC9D,OAAC,SAAS,SAAS,iBAAiB,YAAY,kBAAkB;AAClE,aAAO,MAAM;AACX,qBAAa,oBAAoB,YAAY,kBAAkB;AAC/D,qBAAa,oBAAoB,eAAe,iBAAiB;AACjE,SAAC,SAAS,SAAS,oBAAoB,YAAY,kBAAkB;AAAA,MACvE;AAAA,IACF;AAAA,EACF,GAAG,CAAC,UAAU,cAAc,UAAU,OAAO,QAAQ,MAAM,eAAe,cAAc,eAAe,CAAC;AACxG,EAAM,iBAAU,MAAM;AACpB,QAAI;AACJ,QAAI;AAAU;AAGd,UAAM,cAAc,MAAM,MAAM,iBAAiB,OAAO,UAAU,wBAAwB,cAAc,eAAe,OAAO,SAAS,sBAAsB,iBAAiB,MAAM,gBAAgB,QAAQ,IAAI,GAAG,MAAM,CAAC,CAAC;AAC3N,QAAI,UAAU;AACZ,YAAM,iBAAiB,CAAC,UAAU,GAAG,aAAa,sBAAsB,SAAS,oBAAoB,SAAS,SAAS,QAAQ,SAAS,WAAW,KAAK,8BAA8B,eAAe,IAAI,EAAE,OAAO,OAAK,KAAK,IAAI;AAChO,YAAM,UAAU,QAAQ,WAAW,gBAAgB,QAAQ,CAAC,MAAM,IAAI,WAAW,cAAc;AAC/F,aAAO,MAAM;AACX,gBAAQ;AAAA,MACV;AAAA,IACF;AAAA,EACF,GAAG,CAAC,UAAU,cAAc,UAAU,OAAO,UAAU,eAAe,6BAA6B,MAAM,CAAC;AAC1G,EAAAT,OAAM,MAAM;AACV,QAAI,YAAY,CAAC;AAAU;AAC3B,UAAM,MAAM,YAAY,QAAQ;AAChC,UAAM,2BAA2B,cAAc,GAAG;AAGlD,mBAAe,MAAM;AACnB,YAAM,oBAAoB,oBAAoB,QAAQ;AACtD,YAAM,oBAAoB,gBAAgB;AAC1C,YAAM,aAAa,OAAO,sBAAsB,WAAW,kBAAkB,iBAAiB,IAAI,kBAAkB,YAAY;AAChI,YAAM,+BAA+B,SAAS,UAAU,wBAAwB;AAChF,UAAI,CAAC,sBAAsB,CAAC,gCAAgC,MAAM;AAChE,qBAAa,WAAW;AAAA,UACtB,eAAe,cAAc;AAAA,QAC/B,CAAC;AAAA,MACH;AAAA,IACF,CAAC;AAAA,EACH,GAAG,CAAC,UAAU,MAAM,UAAU,oBAAoB,qBAAqB,eAAe,CAAC;AACvF,EAAAA,OAAM,MAAM;AACV,QAAI,YAAY,CAAC;AAAU;AAC3B,QAAI,2BAA2B;AAC/B,UAAM,MAAM,YAAY,QAAQ;AAChC,UAAM,2BAA2B,cAAc,GAAG;AAClD,UAAM,cAAc,QAAQ;AAC5B,gCAA4B,UAAU;AAItC,aAAS,UAAU,SAAS;AAC1B,UAAI,QAAQ,SAAS,eAAe,KAAK,aAAa,SAAS;AAC7D,oCAA4B,UAAU,KAAK,aAAa;AAAA,MAC1D;AACA,UAAI,CAAC,kBAAkB,WAAW,EAAE,SAAS,QAAQ,IAAI,GAAG;AAC1D;AAAA,MACF;AACA,YAAMgB,eAAc,QAAQ,KAAK;AACjC,UAAI,OAAOA,iBAAgB,UAAU;AACnC,8BAAsB,UAAU;AAChC,mCAA2BA,aAAY;AAAA,MACzC,OAAO;AACL,8BAAsB,UAAU,CAACA;AAAA,MACnC;AAAA,IACF;AACA,WAAO,GAAG,WAAW,SAAS;AAC9B,WAAO,MAAM;AACX,aAAO,IAAI,WAAW,SAAS;AAC/B,YAAM,WAAW,cAAc,GAAG;AAClC,YAAM,uBAAuB,SAAS,UAAU,QAAQ,KAAK,QAAQ,YAAY,KAAK,SAAS,SAAS,MAAM,EAAE,KAAK,UAAQ;AAC3H,YAAI;AACJ,eAAO,UAAU,iBAAiB,KAAK,YAAY,OAAO,SAAS,eAAe,SAAS,UAAU,QAAQ;AAAA,MAC/G,CAAC,KAAK,YAAY,aAAa,CAAC,SAAS,WAAW,EAAE,SAAS,YAAY,UAAU,IAAI;AACzF,UAAI,wBAAwB,KAAK,aAAa,SAAS;AACrD,oCAA4B,UAAU,KAAK,aAAa;AAAA,MAC1D;AACA;AAAA;AAAA,QAEA,eAAe,WAAW,cAAc,4BAA4B,OAAO,KAAK,CAAC,sBAAsB;AAAA,QAAS;AAC9G,qBAAa,4BAA4B,SAAS;AAAA;AAAA;AAAA;AAAA;AAAA,UAKhD,gBAAgB;AAAA,UAChB,eAAe;AAAA,QACjB,CAAC;AAAA,MACH;AAAA,IACF;AAAA,EACF,GAAG,CAAC,UAAU,UAAU,gBAAgB,SAAS,MAAM,QAAQ,MAAM,MAAM,CAAC;AAI5E,EAAAhB,OAAM,MAAM;AACV,QAAI,YAAY,CAAC;AAAe;AAChC,kBAAc,qBAAqB;AAAA,MACjC;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF,CAAC;AACD,WAAO,MAAM;AACX,oBAAc,qBAAqB,IAAI;AAAA,IACzC;AAAA,EACF,GAAG,CAAC,UAAU,eAAe,OAAO,MAAM,cAAc,MAAM,eAAe,CAAC;AAC9E,EAAAA,OAAM,MAAM;AACV,QAAI;AAAU;AACd,QAAI,YAAY,OAAO,qBAAqB,cAAc,CAAC,oBAAoB;AAC7E,YAAM,iBAAiB,MAAM;AAC3B,cAAM,WAAW,SAAS,aAAa,UAAU;AACjD,YAAI,SAAS,QAAQ,SAAS,UAAU,KAAK,cAAc,YAAY,QAAQ,CAAC,MAAM,KAAK,aAAa,WAAW,mBAAmB,EAAE,WAAW,GAAG;AACpJ,cAAI,aAAa,KAAK;AACpB,qBAAS,aAAa,YAAY,GAAG;AAAA,UACvC;AAAA,QACF,WAAW,aAAa,MAAM;AAC5B,mBAAS,aAAa,YAAY,IAAI;AAAA,QACxC;AAAA,MACF;AACA,qBAAe;AACf,YAAM,WAAW,IAAI,iBAAiB,cAAc;AACpD,eAAS,QAAQ,UAAU;AAAA,QACzB,WAAW;AAAA,QACX,SAAS;AAAA,QACT,YAAY;AAAA,MACd,CAAC;AACD,aAAO,MAAM;AACX,iBAAS,WAAW;AAAA,MACtB;AAAA,IACF;AAAA,EACF,GAAG,CAAC,UAAU,UAAU,MAAM,UAAU,oBAAoB,kBAAkB,CAAC;AAC/E,WAAS,oBAAoB,UAAU;AACrC,QAAI,YAAY,CAAC,yBAAyB,CAAC,OAAO;AAChD,aAAO;AAAA,IACT;AACA,WAA0B,qBAAc,uBAAuB;AAAA,MAC7D,KAAK,aAAa,UAAU,wBAAwB;AAAA,MACpD,SAAS,WAAS,aAAa,OAAO,MAAM,WAAW;AAAA,IACzD,GAAG,OAAO,0BAA0B,WAAW,wBAAwB,SAAS;AAAA,EAClF;AACA,QAAM,qBAAqB,CAAC,YAAY,UAAU,CAAC,gCAAgC,kBAAkB;AACrG,SAA0B,qBAAoB,iBAAU,MAAM,sBAAyC,qBAAc,YAAY;AAAA,IAC/H,aAAa;AAAA,IACb,KAAK,iBAAiB,OAAO,SAAS,cAAc;AAAA,IACpD,SAAS,WAAS;AAChB,UAAI,OAAO;AACT,cAAM,MAAM,oBAAoB;AAChC,qBAAa,MAAM,CAAC,MAAM,cAAc,IAAI,CAAC,IAAI,IAAI,IAAI,SAAS,CAAC,CAAC;AAAA,MACtE,WAAW,iBAAiB,QAAQ,cAAc,oBAAoB,cAAc,YAAY;AAC9F,8BAAsB,UAAU;AAChC,YAAI,eAAe,OAAO,cAAc,UAAU,GAAG;AACnD,gBAAM,eAAe,gBAAgB,KAAK;AAC1C,0BAAgB,OAAO,SAAS,aAAa,MAAM;AAAA,QACrD,OAAO;AACL,cAAI;AACJ,WAAC,wBAAwB,cAAc,iBAAiB,YAAY,OAAO,SAAS,sBAAsB,MAAM;AAAA,QAClH;AAAA,MACF;AAAA,IACF;AAAA,EACF,CAAC,GAAG,CAAC,+BAA+B,oBAAoB,OAAO,GAAG,UAAU,oBAAoB,KAAK,GAAG,sBAAyC,qBAAc,YAAY;AAAA,IACzK,aAAa;AAAA,IACb,KAAK,iBAAiB,OAAO,SAAS,cAAc;AAAA,IACpD,SAAS,WAAS;AAChB,UAAI,OAAO;AACT,qBAAa,oBAAoB,EAAE,CAAC,CAAC;AAAA,MACvC,WAAW,iBAAiB,QAAQ,cAAc,oBAAoB,cAAc,YAAY;AAC9F,YAAI,iBAAiB;AACnB,gCAAsB,UAAU;AAAA,QAClC;AACA,YAAI,eAAe,OAAO,cAAc,UAAU,GAAG;AACnD,gBAAM,eAAe,oBAAoB,KAAK;AAC9C,0BAAgB,OAAO,SAAS,aAAa,MAAM;AAAA,QACrD,OAAO;AACL,cAAI;AACJ,WAAC,wBAAwB,cAAc,gBAAgB,YAAY,OAAO,SAAS,sBAAsB,MAAM;AAAA,QACjH;AAAA,MACF;AAAA,IACF;AAAA,EACF,CAAC,CAAC;AACJ;AAEA,IAAM,cAA2B,oBAAI,IAAI;AAQzC,IAAM,kBAAqC,kBAAW,SAASiB,iBAAgB,MAAM,KAAK;AACxF,MAAI;AAAA,IACF,aAAa;AAAA,IACb,GAAG;AAAA,EACL,IAAI;AACJ,QAAM,SAAS,MAAM;AACrB,EAAAjB,OAAM,MAAM;AACV,QAAI,CAAC;AAAY;AACjB,gBAAY,IAAI,MAAM;AACtB,UAAM,QAAQ,qBAAqB,KAAK,YAAY,CAAC;AACrD,UAAM,YAAY,SAAS,KAAK;AAEhC,UAAM,aAAa,KAAK,MAAM,SAAS,gBAAgB,sBAAsB,EAAE,IAAI,IAAI,SAAS,gBAAgB;AAChH,UAAM,cAAc,aAAa,gBAAgB;AACjD,UAAM,iBAAiB,OAAO,aAAa,SAAS,gBAAgB;AACpE,UAAM,UAAU,UAAU,OAAO,WAAW,UAAU,IAAI,IAAI,OAAO;AACrE,UAAM,UAAU,UAAU,MAAM,WAAW,UAAU,GAAG,IAAI,OAAO;AACnE,cAAU,WAAW;AACrB,QAAI,gBAAgB;AAClB,gBAAU,WAAW,IAAI,iBAAiB;AAAA,IAC5C;AAIA,QAAI,OAAO;AACT,UAAI,uBAAuB;AAE3B,YAAM,eAAe,wBAAwB,OAAO,mBAAmB,OAAO,SAAS,sBAAsB,eAAe;AAC5H,YAAM,cAAc,yBAAyB,OAAO,mBAAmB,OAAO,SAAS,uBAAuB,cAAc;AAC5H,aAAO,OAAO,WAAW;AAAA,QACvB,UAAU;AAAA,QACV,KAAK,EAAE,UAAU,KAAK,MAAM,SAAS,KAAK;AAAA,QAC1C,MAAM,EAAE,UAAU,KAAK,MAAM,UAAU,KAAK;AAAA,QAC5C,OAAO;AAAA,MACT,CAAC;AAAA,IACH;AACA,WAAO,MAAM;AACX,kBAAY,OAAO,MAAM;AACzB,UAAI,YAAY,SAAS,GAAG;AAC1B,eAAO,OAAO,WAAW;AAAA,UACvB,UAAU;AAAA,UACV,CAAC,WAAW,GAAG;AAAA,QACjB,CAAC;AACD,YAAI,OAAO;AACT,iBAAO,OAAO,WAAW;AAAA,YACvB,UAAU;AAAA,YACV,KAAK;AAAA,YACL,MAAM;AAAA,YACN,OAAO;AAAA,UACT,CAAC;AACD,iBAAO,SAAS,SAAS,OAAO;AAAA,QAClC;AAAA,MACF;AAAA,IACF;AAAA,EACF,GAAG,CAAC,QAAQ,UAAU,CAAC;AACvB,SAA0B,qBAAc,OAAO,SAAS;AAAA,IACtD;AAAA,EACF,GAAG,MAAM;AAAA,IACP,OAAO;AAAA,MACL,UAAU;AAAA,MACV,UAAU;AAAA,MACV,KAAK;AAAA,MACL,OAAO;AAAA,MACP,QAAQ;AAAA,MACR,MAAM;AAAA,MACN,GAAG,KAAK;AAAA,IACV;AAAA,EACF,CAAC,CAAC;AACJ,CAAC;AAED,SAAS,eAAe,OAAO;AAC7B,SAAO,cAAc,MAAM,MAAM,KAAK,MAAM,OAAO,YAAY;AACjE;AACA,SAAS,eAAe,SAAS;AAC/B,SAAO,kBAAkB,OAAO;AAClC;AAKA,SAAS,SAAS,SAAS,OAAO;AAChC,MAAI,UAAU,QAAQ;AACpB,YAAQ,CAAC;AAAA,EACX;AACA,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,IACA;AAAA,IACA,UAAU;AAAA,MACR;AAAA,IACF;AAAA,EACF,IAAI;AACJ,QAAM;AAAA,IACJ,UAAU;AAAA,IACV,OAAO,cAAc;AAAA,IACrB,SAAS;AAAA,IACT,cAAc;AAAA,IACd,mBAAmB;AAAA,EACrB,IAAI;AACJ,QAAM,iBAAuB,cAAO;AACpC,QAAM,gBAAsB,cAAO,KAAK;AACxC,SAAa,eAAQ,MAAM;AACzB,QAAI,CAAC;AAAS,aAAO,CAAC;AACtB,WAAO;AAAA,MACL,WAAW;AAAA,QACT,cAAc,OAAO;AACnB,yBAAe,UAAU,MAAM;AAAA,QACjC;AAAA,QACA,YAAY,OAAO;AAGjB,cAAI,MAAM,WAAW,GAAG;AACtB;AAAA,UACF;AACA,cAAI,uBAAuB,eAAe,SAAS,IAAI,KAAK,aAAa;AACvE;AAAA,UACF;AACA,cAAI,gBAAgB,SAAS;AAC3B;AAAA,UACF;AACA,cAAI,QAAQ,WAAW,QAAQ,QAAQ,YAAY,QAAQ,QAAQ,UAAU,SAAS,cAAc,OAAO;AACzG,yBAAa,OAAO,MAAM,WAAW;AAAA,UACvC,OAAO;AAEL,kBAAM,eAAe;AACrB,yBAAa,MAAM,MAAM,WAAW;AAAA,UACtC;AAAA,QACF;AAAA,QACA,QAAQ,OAAO;AACb,cAAI,gBAAgB,eAAe,eAAe,SAAS;AACzD,2BAAe,UAAU;AACzB;AAAA,UACF;AACA,cAAI,uBAAuB,eAAe,SAAS,IAAI,KAAK,aAAa;AACvE;AAAA,UACF;AACA,cAAI,QAAQ,WAAW,QAAQ,QAAQ,YAAY,QAAQ,QAAQ,UAAU,SAAS,UAAU,OAAO;AACrG,yBAAa,OAAO,MAAM,WAAW;AAAA,UACvC,OAAO;AACL,yBAAa,MAAM,MAAM,WAAW;AAAA,UACtC;AAAA,QACF;AAAA,QACA,UAAU,OAAO;AACf,yBAAe,UAAU;AACzB,cAAI,MAAM,oBAAoB,CAAC,oBAAoB,eAAe,KAAK,GAAG;AACxE;AAAA,UACF;AACA,cAAI,MAAM,QAAQ,OAAO,CAAC,eAAe,YAAY,GAAG;AAEtD,kBAAM,eAAe;AACrB,0BAAc,UAAU;AAAA,UAC1B;AACA,cAAI,MAAM,QAAQ,SAAS;AACzB,gBAAI,QAAQ,QAAQ;AAClB,2BAAa,OAAO,MAAM,WAAW;AAAA,YACvC,OAAO;AACL,2BAAa,MAAM,MAAM,WAAW;AAAA,YACtC;AAAA,UACF;AAAA,QACF;AAAA,QACA,QAAQ,OAAO;AACb,cAAI,MAAM,oBAAoB,CAAC,oBAAoB,eAAe,KAAK,KAAK,eAAe,YAAY,GAAG;AACxG;AAAA,UACF;AACA,cAAI,MAAM,QAAQ,OAAO,cAAc,SAAS;AAC9C,0BAAc,UAAU;AACxB,gBAAI,QAAQ,QAAQ;AAClB,2BAAa,OAAO,MAAM,WAAW;AAAA,YACvC,OAAO;AACL,2BAAa,MAAM,MAAM,WAAW;AAAA,YACtC;AAAA,UACF;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAAA,EACF,GAAG,CAAC,SAAS,SAAS,aAAa,aAAa,kBAAkB,cAAc,QAAQ,MAAM,YAAY,CAAC;AAC7G;AAGA,IAAM,qBAAqBK,OAAmB,qBAAqB,SAAS,CAAC;AAC7E,IAAM,yBAAyB,uBAAuB,QAAM,GAAG;AAC/D,SAAS,eAAe,UAAU;AAChC,QAAM,MAAY,cAAO,MAAM;AAC7B,QAAI,MAAuC;AACzC,YAAM,IAAI,MAAM,+CAA+C;AAAA,IACjE;AAAA,EACF,CAAC;AACD,yBAAuB,MAAM;AAC3B,QAAI,UAAU;AAAA,EAChB,CAAC;AACD,SAAa,mBAAY,WAAY;AACnC,aAAS,OAAO,UAAU,QAAQ,OAAO,IAAI,MAAM,IAAI,GAAG,OAAO,GAAG,OAAO,MAAM,QAAQ;AACvF,WAAK,IAAI,IAAI,UAAU,IAAI;AAAA,IAC7B;AACA,WAAO,IAAI,WAAW,OAAO,SAAS,IAAI,QAAQ,GAAG,IAAI;AAAA,EAC3D,GAAG,CAAC,CAAC;AACP;AAEA,SAAS,qBAAqB,QAAQ,MAAM;AAC1C,MAAI,UAAU;AACd,MAAI,UAAU;AACd,MAAI,oBAAoB;AACxB,SAAO;AAAA,IACL,gBAAgB,OAAO,WAAW;AAAA,IAClC,wBAAwB;AACtB,UAAI,iBAAiB;AACrB,YAAM,YAAY,kBAAkB,OAAO,YAAY,OAAO,SAAS,gBAAgB,sBAAsB,MAAM;AAAA,QACjH,OAAO;AAAA,QACP,QAAQ;AAAA,QACR,GAAG;AAAA,QACH,GAAG;AAAA,MACL;AACA,YAAM,UAAU,KAAK,SAAS,OAAO,KAAK,SAAS;AACnD,YAAM,UAAU,KAAK,SAAS,OAAO,KAAK,SAAS;AACnD,YAAM,6BAA6B,CAAC,cAAc,WAAW,EAAE,WAAW,wBAAwB,KAAK,QAAQ,QAAQ,cAAc,OAAO,SAAS,sBAAsB,SAAS,EAAE,KAAK,KAAK,gBAAgB;AAChN,UAAI,QAAQ,QAAQ;AACpB,UAAI,SAAS,QAAQ;AACrB,UAAI,IAAI,QAAQ;AAChB,UAAI,IAAI,QAAQ;AAChB,UAAI,WAAW,QAAQ,KAAK,KAAK,SAAS;AACxC,kBAAU,QAAQ,IAAI,KAAK;AAAA,MAC7B;AACA,UAAI,WAAW,QAAQ,KAAK,KAAK,SAAS;AACxC,kBAAU,QAAQ,IAAI,KAAK;AAAA,MAC7B;AACA,WAAK,WAAW;AAChB,WAAK,WAAW;AAChB,cAAQ;AACR,eAAS;AACT,UAAI,CAAC,qBAAqB,4BAA4B;AACpD,gBAAQ,KAAK,SAAS,MAAM,QAAQ,QAAQ;AAC5C,iBAAS,KAAK,SAAS,MAAM,QAAQ,SAAS;AAC9C,YAAI,WAAW,KAAK,KAAK,OAAO,KAAK,IAAI;AACzC,YAAI,WAAW,KAAK,KAAK,OAAO,KAAK,IAAI;AAAA,MAC3C,WAAW,qBAAqB,CAAC,4BAA4B;AAC3D,iBAAS,KAAK,SAAS,MAAM,QAAQ,SAAS;AAC9C,gBAAQ,KAAK,SAAS,MAAM,QAAQ,QAAQ;AAAA,MAC9C;AACA,0BAAoB;AACpB,aAAO;AAAA,QACL;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA,KAAK;AAAA,QACL,OAAO,IAAI;AAAA,QACX,QAAQ,IAAI;AAAA,QACZ,MAAM;AAAA,MACR;AAAA,IACF;AAAA,EACF;AACF;AACA,SAAS,kBAAkB,OAAO;AAChC,SAAO,SAAS,QAAQ,MAAM,WAAW;AAC3C;AAMA,SAAS,eAAe,SAAS,OAAO;AACtC,MAAI,UAAU,QAAQ;AACpB,YAAQ,CAAC;AAAA,EACX;AACA,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,IACA;AAAA,IACA,UAAU;AAAA,MACR;AAAA,IACF;AAAA,EACF,IAAI;AACJ,QAAM;AAAA,IACJ,UAAU;AAAA,IACV,OAAO;AAAA,IACP,IAAI;AAAA,IACJ,IAAI;AAAA,EACN,IAAI;AACJ,QAAM,aAAmB,cAAO,KAAK;AACrC,QAAM,qBAA2B,cAAO,IAAI;AAC5C,QAAM,CAAC,aAAa,cAAc,IAAU,gBAAS;AACrD,QAAM,CAAC,UAAU,WAAW,IAAU,gBAAS,CAAC,CAAC;AACjD,QAAM,eAAe,eAAe,CAACa,IAAGC,OAAM;AAC5C,QAAI,WAAW;AAAS;AAKxB,QAAI,QAAQ,QAAQ,aAAa,CAAC,kBAAkB,QAAQ,QAAQ,SAAS,GAAG;AAC9E;AAAA,IACF;AACA,SAAK,qBAAqB,qBAAqB,KAAK,cAAc;AAAA,MAChE,GAAAD;AAAA,MACA,GAAAC;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF,CAAC,CAAC;AAAA,EACJ,CAAC;AACD,QAAM,6BAA6B,eAAe,WAAS;AACzD,QAAI,KAAK,QAAQ,KAAK;AAAM;AAC5B,QAAI,CAAC,MAAM;AACT,mBAAa,MAAM,SAAS,MAAM,OAAO;AAAA,IAC3C,WAAW,CAAC,mBAAmB,SAAS;AAItC,kBAAY,CAAC,CAAC;AAAA,IAChB;AAAA,EACF,CAAC;AAMD,QAAM,YAAY,uBAAuB,WAAW,IAAI,WAAW;AACnE,QAAM,cAAoB,mBAAY,MAAM;AAE1C,QAAI,CAAC,aAAa,CAAC,WAAW,KAAK,QAAQ,KAAK;AAAM;AACtD,UAAM,MAAM,UAAU,KAAK,SAAS,OAAO;AAC3C,aAAS,gBAAgB,OAAO;AAC9B,YAAM,SAAS,UAAU,KAAK;AAC9B,UAAI,CAAC,SAAS,KAAK,SAAS,SAAS,MAAM,GAAG;AAC5C,qBAAa,MAAM,SAAS,MAAM,OAAO;AAAA,MAC3C,OAAO;AACL,YAAI,oBAAoB,aAAa,eAAe;AACpD,2BAAmB,UAAU;AAAA,MAC/B;AAAA,IACF;AACA,QAAI,CAAC,QAAQ,QAAQ,aAAa,kBAAkB,QAAQ,QAAQ,SAAS,GAAG;AAC9E,UAAI,iBAAiB,aAAa,eAAe;AACjD,YAAM,UAAU,MAAM;AACpB,YAAI,oBAAoB,aAAa,eAAe;AACpD,2BAAmB,UAAU;AAAA,MAC/B;AACA,yBAAmB,UAAU;AAC7B,aAAO;AAAA,IACT;AACA,SAAK,qBAAqB,KAAK,aAAa,OAAO;AAAA,EACrD,GAAG,CAAC,SAAS,SAAS,WAAW,MAAM,cAAc,GAAG,CAAC,CAAC;AAC1D,EAAM,iBAAU,MAAM;AACpB,WAAO,YAAY;AAAA,EACrB,GAAG,CAAC,aAAa,QAAQ,CAAC;AAC1B,EAAM,iBAAU,MAAM;AACpB,QAAI,WAAW,CAAC,UAAU;AACxB,iBAAW,UAAU;AAAA,IACvB;AAAA,EACF,GAAG,CAAC,SAAS,QAAQ,CAAC;AACtB,EAAM,iBAAU,MAAM;AACpB,QAAI,CAAC,WAAW,MAAM;AACpB,iBAAW,UAAU;AAAA,IACvB;AAAA,EACF,GAAG,CAAC,SAAS,IAAI,CAAC;AAClB,EAAAnB,OAAM,MAAM;AACV,QAAI,YAAY,KAAK,QAAQ,KAAK,OAAO;AACvC,iBAAW,UAAU;AACrB,mBAAa,GAAG,CAAC;AAAA,IACnB;AAAA,EACF,GAAG,CAAC,SAAS,GAAG,GAAG,YAAY,CAAC;AAChC,SAAa,eAAQ,MAAM;AACzB,QAAI,CAAC;AAAS,aAAO,CAAC;AACtB,aAAS,kBAAkB,MAAM;AAC/B,UAAI;AAAA,QACF,aAAAoB;AAAA,MACF,IAAI;AACJ,qBAAeA,YAAW;AAAA,IAC5B;AACA,WAAO;AAAA,MACL,WAAW;AAAA,QACT,eAAe;AAAA,QACf,gBAAgB;AAAA,QAChB,aAAa;AAAA,QACb,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,EACF,GAAG,CAAC,SAAS,0BAA0B,CAAC;AAC1C;AAEA,IAAM,oBAAoB;AAAA,EACxB,aAAa;AAAA,EACb,WAAW;AAAA,EACX,OAAO;AACT;AACA,IAAM,qBAAqB;AAAA,EACzB,aAAa;AAAA,EACb,WAAW;AAAA,EACX,OAAO;AACT;AACA,IAAM,uBAAuB,aAAW;AACtC,MAAI,oBAAoB;AACxB,SAAO;AAAA,IACL,kBAAkB,OAAO,YAAY,YAAY,WAAW,qBAAqB,WAAW,OAAO,SAAS,QAAQ,cAAc,OAAO,qBAAqB;AAAA,IAC9J,qBAAqB,OAAO,YAAY,YAAY,WAAW,wBAAwB,WAAW,OAAO,SAAS,QAAQ,iBAAiB,OAAO,wBAAwB;AAAA,EAC5K;AACF;AAMA,SAAS,WAAW,SAAS,OAAO;AAClC,MAAI,UAAU,QAAQ;AACpB,YAAQ,CAAC;AAAA,EACX;AACA,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA,UAAU;AAAA,MACR;AAAA,MACA;AAAA,MACA;AAAA,IACF;AAAA,IACA;AAAA,EACF,IAAI;AACJ,QAAM;AAAA,IACJ,UAAU;AAAA,IACV,YAAY;AAAA,IACZ,cAAc,wBAAwB;AAAA,IACtC,oBAAoB;AAAA,IACpB,iBAAiB;AAAA,IACjB,sBAAsB;AAAA,IACtB,iBAAiB;AAAA,IACjB;AAAA,EACF,IAAI;AACJ,QAAM,OAAO,gBAAgB;AAC7B,QAAM,SAAS,wBAAwB,KAAK;AAC5C,QAAM,iBAAiB,eAAe,OAAO,0BAA0B,aAAa,wBAAwB,MAAM,KAAK;AACvH,QAAM,eAAe,OAAO,0BAA0B,aAAa,iBAAiB;AACpF,QAAM,qBAA2B,cAAO,KAAK;AAC7C,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,EACF,IAAI,qBAAqB,OAAO;AAChC,QAAM,uBAAuB,eAAe,WAAS;AACnD,QAAI,CAAC,QAAQ,CAAC,WAAW,CAAC,aAAa,MAAM,QAAQ,UAAU;AAC7D;AAAA,IACF;AACA,UAAM,WAAW,OAAO,YAAY,KAAK,SAAS,SAAS,MAAM,IAAI,CAAC;AACtE,QAAI,CAAC,kBAAkB;AACrB,YAAM,gBAAgB;AACtB,UAAI,SAAS,SAAS,GAAG;AACvB,YAAI,gBAAgB;AACpB,iBAAS,QAAQ,WAAS;AACxB,cAAI;AACJ,eAAK,iBAAiB,MAAM,YAAY,QAAQ,eAAe,QAAQ,CAAC,MAAM,QAAQ,QAAQ,QAAQ,oBAAoB;AACxH,4BAAgB;AAChB;AAAA,UACF;AAAA,QACF,CAAC;AACD,YAAI,CAAC,eAAe;AAClB;AAAA,QACF;AAAA,MACF;AAAA,IACF;AACA,WAAO,KAAK,WAAW;AAAA,MACrB,MAAM;AAAA,MACN,MAAM;AAAA,QACJ,aAAa;AAAA,UACX,eAAe;AAAA,QACjB;AAAA,MACF;AAAA,IACF,CAAC;AACD,iBAAa,OAAO,aAAa,KAAK,IAAI,MAAM,cAAc,KAAK;AAAA,EACrE,CAAC;AACD,QAAM,sBAAsB,eAAe,WAAS;AAGlD,UAAM,kBAAkB,mBAAmB;AAC3C,uBAAmB,UAAU;AAC7B,QAAI,iBAAiB;AACnB;AAAA,IACF;AACA,QAAI,OAAO,iBAAiB,cAAc,CAAC,aAAa,KAAK,GAAG;AAC9D;AAAA,IACF;AACA,UAAM,SAAS,UAAU,KAAK;AAC9B,UAAM,gBAAgB,MAAM,gBAAgB,OAAO,IAAI;AACvD,UAAM,UAAU,YAAY,QAAQ,EAAE,iBAAiB,aAAa;AACpE,QAAI,qBAAqB,UAAU,MAAM,IAAI,SAAS;AACtD,WAAO,sBAAsB,CAAC,sBAAsB,kBAAkB,GAAG;AACvE,YAAM,aAAa,cAAc,kBAAkB;AACnD,UAAI,eAAe,YAAY,QAAQ,EAAE,QAAQ,CAAC,UAAU,UAAU,GAAG;AACvE;AAAA,MACF,OAAO;AACL,6BAAqB;AAAA,MACvB;AAAA,IACF;AAIA,QAAI,QAAQ,UAAU,UAAU,MAAM,KAAK,CAAC,cAAc,MAAM;AAAA,IAEhE,CAAC,SAAS,QAAQ,QAAQ;AAAA;AAAA,IAG1B,MAAM,KAAK,OAAO,EAAE,MAAM,YAAU,CAAC,SAAS,oBAAoB,MAAM,CAAC,GAAG;AAC1E;AAAA,IACF;AAGA,QAAI,cAAc,MAAM,KAAK,UAAU;AAGrC,YAAM,aAAa,OAAO,cAAc,KAAK,OAAO,cAAc,OAAO;AACzE,YAAM,aAAa,OAAO,eAAe,KAAK,OAAO,eAAe,OAAO;AAC3E,UAAI,QAAQ,cAAc,MAAM,UAAU,OAAO;AAMjD,UAAI,YAAY;AACd,cAAMZ,SAAQa,kBAAiB,MAAM,EAAE,cAAc;AACrD,YAAIb,QAAO;AACT,kBAAQ,MAAM,WAAW,OAAO,cAAc,OAAO;AAAA,QACvD;AAAA,MACF;AACA,UAAI,SAAS,cAAc,MAAM,UAAU,OAAO,cAAc;AAC9D;AAAA,MACF;AAAA,IACF;AACA,UAAM,yBAAyB,QAAQ,YAAY,KAAK,SAAS,SAAS,MAAM,EAAE,KAAK,UAAQ;AAC7F,UAAI;AACJ,aAAO,oBAAoB,QAAQ,gBAAgB,KAAK,YAAY,OAAO,SAAS,cAAc,SAAS,QAAQ;AAAA,IACrH,CAAC;AACD,QAAI,oBAAoB,OAAO,QAAQ,KAAK,oBAAoB,OAAO,YAAY,KAAK,wBAAwB;AAC9G;AAAA,IACF;AACA,UAAM,WAAW,OAAO,YAAY,KAAK,SAAS,SAAS,MAAM,IAAI,CAAC;AACtE,QAAI,SAAS,SAAS,GAAG;AACvB,UAAI,gBAAgB;AACpB,eAAS,QAAQ,WAAS;AACxB,YAAI;AACJ,aAAK,kBAAkB,MAAM,YAAY,QAAQ,gBAAgB,QAAQ,CAAC,MAAM,QAAQ,QAAQ,QAAQ,uBAAuB;AAC7H,0BAAgB;AAChB;AAAA,QACF;AAAA,MACF,CAAC;AACD,UAAI,CAAC,eAAe;AAClB;AAAA,MACF;AAAA,IACF;AACA,WAAO,KAAK,WAAW;AAAA,MACrB,MAAM;AAAA,MACN,MAAM;AAAA,QACJ,aAAa,SAAS;AAAA,UACpB,eAAe;AAAA,QACjB,IAAI,eAAe,KAAK,KAAK,sBAAsB,KAAK;AAAA,MAC1D;AAAA,IACF,CAAC;AACD,iBAAa,OAAO,KAAK;AAAA,EAC3B,CAAC;AACD,EAAM,iBAAU,MAAM;AACpB,QAAI,CAAC,QAAQ,CAAC,SAAS;AACrB;AAAA,IACF;AACA,YAAQ,QAAQ,qBAAqB;AACrC,YAAQ,QAAQ,wBAAwB;AACxC,aAAS,SAAS,OAAO;AACvB,mBAAa,OAAO,KAAK;AAAA,IAC3B;AACA,UAAM,MAAM,YAAY,QAAQ;AAChC,iBAAa,IAAI,iBAAiB,WAAW,oBAAoB;AACjE,oBAAgB,IAAI,iBAAiB,mBAAmB,mBAAmB;AAC3E,QAAI,YAAY,CAAC;AACjB,QAAI,gBAAgB;AAClB,UAAI,UAAU,YAAY,GAAG;AAC3B,oBAAY,qBAAqB,YAAY;AAAA,MAC/C;AACA,UAAI,UAAU,QAAQ,GAAG;AACvB,oBAAY,UAAU,OAAO,qBAAqB,QAAQ,CAAC;AAAA,MAC7D;AACA,UAAI,CAAC,UAAU,SAAS,KAAK,aAAa,UAAU,gBAAgB;AAClE,oBAAY,UAAU,OAAO,qBAAqB,UAAU,cAAc,CAAC;AAAA,MAC7E;AAAA,IACF;AAGA,gBAAY,UAAU,OAAO,cAAY;AACvC,UAAI;AACJ,aAAO,eAAe,mBAAmB,IAAI,gBAAgB,OAAO,SAAS,iBAAiB;AAAA,IAChG,CAAC;AACD,cAAU,QAAQ,cAAY;AAC5B,eAAS,iBAAiB,UAAU,UAAU;AAAA,QAC5C,SAAS;AAAA,MACX,CAAC;AAAA,IACH,CAAC;AACD,WAAO,MAAM;AACX,mBAAa,IAAI,oBAAoB,WAAW,oBAAoB;AACpE,sBAAgB,IAAI,oBAAoB,mBAAmB,mBAAmB;AAC9E,gBAAU,QAAQ,cAAY;AAC5B,iBAAS,oBAAoB,UAAU,QAAQ;AAAA,MACjD,CAAC;AAAA,IACH;AAAA,EACF,GAAG,CAAC,SAAS,UAAU,cAAc,WAAW,WAAW,cAAc,mBAAmB,MAAM,cAAc,gBAAgB,SAAS,kBAAkB,qBAAqB,sBAAsB,mBAAmB,CAAC;AAC1N,EAAM,iBAAU,MAAM;AACpB,uBAAmB,UAAU;AAAA,EAC/B,GAAG,CAAC,cAAc,iBAAiB,CAAC;AACpC,SAAa,eAAQ,MAAM;AACzB,QAAI,CAAC,SAAS;AACZ,aAAO,CAAC;AAAA,IACV;AACA,WAAO;AAAA,MACL,WAAW;AAAA,QACT,WAAW;AAAA,QACX,CAAC,kBAAkB,mBAAmB,CAAC,GAAG,WAAS;AACjD,cAAI,gBAAgB;AAClB,mBAAO,KAAK,WAAW;AAAA,cACrB,MAAM;AAAA,cACN,MAAM;AAAA,gBACJ,aAAa;AAAA,cACf;AAAA,YACF,CAAC;AACD,yBAAa,OAAO,MAAM,WAAW;AAAA,UACvC;AAAA,QACF;AAAA,MACF;AAAA,MACA,UAAU;AAAA,QACR,WAAW;AAAA,QACX,CAAC,mBAAmB,iBAAiB,CAAC,GAAG,MAAM;AAC7C,6BAAmB,UAAU;AAAA,QAC/B;AAAA,MACF;AAAA,IACF;AAAA,EACF,GAAG,CAAC,SAAS,QAAQ,gBAAgB,mBAAmB,qBAAqB,cAAc,oBAAoB,CAAC;AAClH;AAEA,IAAI;AACJ,IAAI,MAAuC;AACzC,kBAA6B,oBAAI,IAAI;AACvC;AAMA,SAASc,aAAY,SAAS;AAC5B,MAAI;AACJ,MAAI,YAAY,QAAQ;AACtB,cAAU,CAAC;AAAA,EACb;AACA,QAAM;AAAA,IACJ,OAAO;AAAA,IACP,cAAc;AAAA,IACd;AAAA,EACF,IAAI;AACJ,MAAI,MAAuC;AACzC,QAAI;AACJ,UAAM,MAAM;AACZ,SAAK,oBAAoB,QAAQ,aAAa,QAAQ,kBAAkB,aAAa,CAAC,UAAU,QAAQ,SAAS,SAAS,GAAG;AAC3H,UAAI;AACJ,UAAI,GAAG,iBAAiB,kBAAkB,QAAQ,eAAe,IAAI,GAAG,IAAI;AAC1E,YAAI;AACJ,SAAC,kBAAkB,kBAAkB,OAAO,SAAS,gBAAgB,IAAI,GAAG;AAC5E,gBAAQ,MAAM,GAAG;AAAA,MACnB;AAAA,IACF;AAAA,EACF;AACA,QAAM,CAAC,eAAe,eAAe,IAAU,gBAAS,IAAI;AAC5D,QAAM,iBAAiB,qBAAqB,QAAQ,aAAa,OAAO,SAAS,mBAAmB,cAAc;AAClH,QAAM,WAAW,YAAc,OAAO;AACtC,QAAM,OAAO,gBAAgB;AAC7B,QAAM,eAAe,eAAe,CAACC,OAAM,UAAU;AACnD,QAAIA,OAAM;AACR,cAAQ,QAAQ,YAAY;AAAA,IAC9B;AACA,6BAAyB,OAAO,SAAS,sBAAsBA,OAAM,KAAK;AAAA,EAC5E,CAAC;AACD,QAAM,kBAAwB,cAAO,IAAI;AACzC,QAAM,UAAgB,cAAO,CAAC,CAAC;AAC/B,QAAM,SAAe,gBAAS,MAAM,aAAa,CAAC,EAAE,CAAC;AACrD,QAAM,aAAa,MAAM;AACzB,QAAM,uBAA6B,mBAAY,UAAQ;AACrD,UAAM,oBAAoB,UAAU,IAAI,IAAI;AAAA,MAC1C,uBAAuB,MAAM,KAAK,sBAAsB;AAAA,MACxD,gBAAgB;AAAA,IAClB,IAAI;AACJ,aAAS,KAAK,aAAa,iBAAiB;AAAA,EAC9C,GAAG,CAAC,SAAS,IAAI,CAAC;AAClB,QAAM,eAAqB,mBAAY,UAAQ;AAC7C,QAAI,UAAU,IAAI,KAAK,SAAS,MAAM;AACpC,sBAAgB,UAAU;AAC1B,sBAAgB,IAAI;AAAA,IACtB;AAIA,QAAI,UAAU,SAAS,KAAK,UAAU,OAAO,KAAK,SAAS,KAAK,UAAU,YAAY;AAAA;AAAA;AAAA,IAItF,SAAS,QAAQ,CAAC,UAAU,IAAI,GAAG;AACjC,eAAS,KAAK,aAAa,IAAI;AAAA,IACjC;AAAA,EACF,GAAG,CAAC,SAAS,IAAI,CAAC;AAClB,QAAM,OAAa,eAAQ,OAAO;AAAA,IAChC,GAAG,SAAS;AAAA,IACZ;AAAA,IACA;AAAA,IACA,cAAc;AAAA,EAChB,IAAI,CAAC,SAAS,MAAM,cAAc,oBAAoB,CAAC;AACvD,QAAM,WAAiB,eAAQ,OAAO;AAAA,IACpC,GAAG,SAAS;AAAA,IACZ;AAAA,EACF,IAAI,CAAC,SAAS,UAAU,YAAY,CAAC;AACrC,QAAM,UAAgB,eAAQ,OAAO;AAAA,IACnC,GAAG;AAAA,IACH;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,IAAI,CAAC,UAAU,QAAQ,YAAY,QAAQ,MAAM,cAAc,MAAM,QAAQ,CAAC;AAC9E,EAAAvB,OAAM,MAAM;AACV,UAAM,OAAO,QAAQ,OAAO,SAAS,KAAK,SAAS,QAAQ,KAAK,CAAAwB,UAAQA,MAAK,OAAO,MAAM;AAC1F,QAAI,MAAM;AACR,WAAK,UAAU;AAAA,IACjB;AAAA,EACF,CAAC;AACD,SAAa,eAAQ,OAAO;AAAA,IAC1B,GAAG;AAAA,IACH;AAAA,IACA;AAAA,IACA;AAAA,EACF,IAAI,CAAC,UAAU,MAAM,UAAU,OAAO,CAAC;AACzC;AAOA,SAAS,SAAS,SAAS,OAAO;AAChC,MAAI,UAAU,QAAQ;AACpB,YAAQ,CAAC;AAAA,EACX;AACA,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA,UAAU;AAAA,MACR;AAAA,MACA;AAAA,IACF;AAAA,EACF,IAAI;AACJ,QAAM;AAAA,IACJ,UAAU;AAAA,IACV,eAAe;AAAA,EACjB,IAAI;AACJ,QAAM,iBAAuB,cAAO,EAAE;AACtC,QAAM,gBAAsB,cAAO,KAAK;AACxC,QAAM,aAAmB,cAAO;AAChC,EAAM,iBAAU,MAAM;AACpB,QAAI,CAAC,SAAS;AACZ;AAAA,IACF;AACA,UAAM,MAAM,YAAY,QAAQ;AAChC,UAAM,MAAM,IAAI,eAAe;AAK/B,aAAS,SAAS;AAChB,UAAI,CAAC,QAAQ,cAAc,YAAY,KAAK,iBAAiB,cAAc,YAAY,YAAY,CAAC,GAAG;AACrG,sBAAc,UAAU;AAAA,MAC1B;AAAA,IACF;AACA,QAAI,iBAAiB,QAAQ,MAAM;AACnC,WAAO,MAAM;AACX,UAAI,oBAAoB,QAAQ,MAAM;AAAA,IACxC;AAAA,EACF,GAAG,CAAC,UAAU,cAAc,MAAM,OAAO,CAAC;AAC1C,EAAM,iBAAU,MAAM;AACpB,QAAI,CAAC,SAAS;AACZ;AAAA,IACF;AACA,aAAS,UAAU,SAAS;AAC1B,UAAI,QAAQ,SAAS,oBAAoB,QAAQ,SAAS,aAAa;AACrE,sBAAc,UAAU;AAAA,MAC1B;AAAA,IACF;AACA,WAAO,GAAG,WAAW,SAAS;AAC9B,WAAO,MAAM;AACX,aAAO,IAAI,WAAW,SAAS;AAAA,IACjC;AAAA,EACF,GAAG,CAAC,QAAQ,OAAO,CAAC;AACpB,EAAM,iBAAU,MAAM;AACpB,WAAO,MAAM;AACX,mBAAa,WAAW,OAAO;AAAA,IACjC;AAAA,EACF,GAAG,CAAC,CAAC;AACL,SAAa,eAAQ,MAAM;AACzB,QAAI,CAAC,SAAS;AACZ,aAAO,CAAC;AAAA,IACV;AACA,WAAO;AAAA,MACL,WAAW;AAAA,QACT,cAAc,MAAM;AAClB,cAAI;AAAA,YACF;AAAA,UACF,IAAI;AACJ,yBAAe,UAAU;AACzB,wBAAc,UAAU,CAAC,EAAE,eAAe;AAAA,QAC5C;AAAA,QACA,eAAe;AACb,wBAAc,UAAU;AAAA,QAC1B;AAAA,QACA,QAAQ,OAAO;AACb,cAAI;AACJ,cAAI,cAAc,SAAS;AACzB;AAAA,UACF;AAIA,cAAI,MAAM,SAAS,aAAa,wBAAwB,QAAQ,QAAQ,cAAc,OAAO,SAAS,sBAAsB,UAAU,eAAe,oBAAoB,QAAQ,QAAQ,WAAW,YAAY,GAAG;AACjN;AAAA,UACF;AACA,uBAAa,MAAM,MAAM,WAAW;AAAA,QACtC;AAAA,QACA,OAAO,OAAO;AACZ,wBAAc,UAAU;AACxB,gBAAM,gBAAgB,MAAM;AAI5B,gBAAM,oBAAoB,UAAU,aAAa,KAAK,cAAc,aAAa,gBAAgB,aAAa,CAAC,KAAK,cAAc,aAAa,WAAW,MAAM;AAGhK,qBAAW,UAAU,WAAW,MAAM;AAIpC,gBAAI,SAAS,KAAK,SAAS,SAAS,aAAa,KAAK,SAAS,cAAc,aAAa,KAAK,mBAAmB;AAChH;AAAA,YACF;AACA,yBAAa,OAAO,MAAM,WAAW;AAAA,UACvC,CAAC;AAAA,QACH;AAAA,MACF;AAAA,IACF;AAAA,EACF,GAAG,CAAC,SAAS,cAAc,cAAc,MAAM,SAAS,YAAY,CAAC;AACvE;AAEA,SAAS,WAAW,WAAW,WAAW,YAAY;AACpD,QAAM,MAAM,oBAAI,IAAI;AACpB,SAAO;AAAA,IACL,GAAI,eAAe,cAAc;AAAA,MAC/B,UAAU;AAAA,IACZ;AAAA,IACA,GAAG;AAAA,IACH,GAAG,UAAU,IAAI,WAAS,QAAQ,MAAM,UAAU,IAAI,IAAI,EAAE,OAAO,SAAS,EAAE,OAAO,CAAC,KAAK,UAAU;AACnG,UAAI,CAAC,OAAO;AACV,eAAO;AAAA,MACT;AACA,aAAO,QAAQ,KAAK,EAAE,QAAQ,UAAQ;AACpC,YAAI,CAAC,KAAK,KAAK,IAAI;AACnB,YAAI,IAAI,QAAQ,IAAI,MAAM,GAAG;AAC3B,cAAI,CAAC,IAAI,IAAI,GAAG,GAAG;AACjB,gBAAI,IAAI,KAAK,CAAC,CAAC;AAAA,UACjB;AACA,cAAI,OAAO,UAAU,YAAY;AAC/B,gBAAI;AACJ,aAAC,WAAW,IAAI,IAAI,GAAG,MAAM,OAAO,SAAS,SAAS,KAAK,KAAK;AAChE,gBAAI,GAAG,IAAI,WAAY;AACrB,kBAAI;AACJ,uBAAS,OAAO,UAAU,QAAQ,OAAO,IAAI,MAAM,IAAI,GAAG,OAAO,GAAG,OAAO,MAAM,QAAQ;AACvF,qBAAK,IAAI,IAAI,UAAU,IAAI;AAAA,cAC7B;AACA,sBAAQ,YAAY,IAAI,IAAI,GAAG,MAAM,OAAO,SAAS,UAAU,IAAI,QAAM,GAAG,GAAG,IAAI,CAAC,EAAE,KAAK,SAAO,QAAQ,MAAS;AAAA,YACrH;AAAA,UACF;AAAA,QACF,OAAO;AACL,cAAI,GAAG,IAAI;AAAA,QACb;AAAA,MACF,CAAC;AACD,aAAO;AAAA,IACT,GAAG,CAAC,CAAC;AAAA,EACP;AACF;AAQA,SAAS,gBAAgB,WAAW;AAClC,MAAI,cAAc,QAAQ;AACxB,gBAAY,CAAC;AAAA,EACf;AAGA,QAAM,OAAO;AACb,QAAM,oBAA0B;AAAA,IAAY,eAAa,WAAW,WAAW,WAAW,WAAW;AAAA;AAAA,IAErG;AAAA,EAAI;AACJ,QAAM,mBAAyB;AAAA,IAAY,eAAa,WAAW,WAAW,WAAW,UAAU;AAAA;AAAA,IAEnG;AAAA,EAAI;AACJ,QAAM,eAAqB;AAAA,IAAY,eAAa,WAAW,WAAW,WAAW,MAAM;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAM3F,UAAU,IAAI,SAAO,OAAO,OAAO,SAAS,IAAI,IAAI;AAAA,EAAC;AACrD,SAAa,eAAQ,OAAO;AAAA,IAC1B;AAAA,IACA;AAAA,IACA;AAAA,EACF,IAAI,CAAC,mBAAmB,kBAAkB,YAAY,CAAC;AACzD;AAEA,IAAI,2BAA2B;AAC/B,SAAS,SAAS,aAAa,UAAU,YAAY;AACnD,UAAQ,aAAa;AAAA,IACnB,KAAK;AACH,aAAO;AAAA,IACT,KAAK;AACH,aAAO;AAAA,IACT;AACE,aAAO,YAAY;AAAA,EACvB;AACF;AACA,SAAS,qBAAqB,KAAK,aAAa;AAC9C,QAAM,WAAW,QAAQ,YAAY,QAAQ;AAC7C,QAAM,aAAa,QAAQ,cAAc,QAAQ;AACjD,SAAO,SAAS,aAAa,UAAU,UAAU;AACnD;AACA,SAAS,0BAA0B,KAAK,aAAa,KAAK;AACxD,QAAM,WAAW,QAAQ;AACzB,QAAM,aAAa,MAAM,QAAQ,aAAa,QAAQ;AACtD,SAAO,SAAS,aAAa,UAAU,UAAU,KAAK,QAAQ,WAAW,OAAO,OAAO,QAAQ;AACjG;AACA,SAAS,0BAA0B,KAAK,aAAa,KAAK;AACxD,QAAM,WAAW,MAAM,QAAQ,aAAa,QAAQ;AACpD,QAAM,aAAa,QAAQ;AAC3B,SAAO,SAAS,aAAa,UAAU,UAAU;AACnD;AACA,SAAS,2BAA2B,KAAK,aAAa,KAAK;AACzD,QAAM,WAAW,MAAM,QAAQ,cAAc,QAAQ;AACrD,QAAM,aAAa,QAAQ;AAC3B,SAAO,SAAS,aAAa,UAAU,UAAU;AACnD;AAMA,SAAS,kBAAkB,SAAS,OAAO;AACzC,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,IACA;AAAA,IACA,UAAU;AAAA,MACR;AAAA,MACA;AAAA,IACF;AAAA,EACF,IAAI;AACJ,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,IACA,YAAY,sBAAsB,MAAM;AAAA,IAAC;AAAA,IACzC,UAAU;AAAA,IACV,gBAAgB;AAAA,IAChB,cAAc;AAAA,IACd,OAAO;AAAA,IACP,SAAS;AAAA,IACT,MAAM;AAAA,IACN,UAAU;AAAA,IACV,kBAAkB;AAAA,IAClB,mBAAmB;AAAA,IACnB,qBAAqB;AAAA,IACrB,kBAAkB;AAAA,IAClB,cAAc;AAAA,IACd,OAAO;AAAA,IACP,qBAAqB;AAAA,IACrB;AAAA,EACF,IAAI;AACJ,MAAI,MAAuC;AACzC,QAAI,aAAa;AACf,UAAI,CAAC,MAAM;AACT,gBAAQ,KAAK,CAAC,qEAAqE,WAAW,EAAE,KAAK,GAAG,CAAC;AAAA,MAC3G;AACA,UAAI,CAAC,SAAS;AACZ,gBAAQ,KAAK,CAAC,6DAA6D,WAAW,EAAE,KAAK,GAAG,CAAC;AAAA,MACnG;AAAA,IACF;AACA,QAAI,gBAAgB,cAAc,OAAO,GAAG;AAC1C,cAAQ,KAAK,CAAC,+DAA+D,wDAAwD,EAAE,KAAK,GAAG,CAAC;AAAA,IAClJ;AAAA,EACF;AACA,QAAM,WAAW,wBAAwB;AACzC,QAAM,OAAO,gBAAgB;AAC7B,QAAM,aAAa,eAAe,mBAAmB;AACrD,QAAM,qBAA2B,cAAO,eAAe;AACvD,QAAM,WAAiB,cAAO,iBAAiB,OAAO,gBAAgB,EAAE;AACxE,QAAM,SAAe,cAAO,IAAI;AAChC,QAAM,uBAA6B,cAAO,IAAI;AAC9C,QAAM,wBAA8B,cAAO,UAAU;AACrD,QAAM,qBAA2B,cAAO,CAAC,CAAC,QAAQ;AAClD,QAAM,iBAAuB,cAAO,KAAK;AACzC,QAAM,yBAA+B,cAAO,KAAK;AACjD,QAAM,qBAAqBf,cAAa,eAAe;AACvD,QAAM,gBAAgBA,cAAa,IAAI;AACvC,QAAM,wBAAwBA,cAAa,kBAAkB;AAC7D,QAAM,CAAC,UAAU,WAAW,IAAU,gBAAS;AAC/C,QAAM,CAAC,WAAW,YAAY,IAAU,gBAAS;AACjD,QAAM,YAAY,eAAe,SAAUgB,UAASC,WAAU,qBAAqB;AACjF,QAAI,wBAAwB,QAAQ;AAClC,4BAAsB;AAAA,IACxB;AACA,UAAMC,QAAOF,SAAQ,QAAQC,UAAS,OAAO;AAC7C,QAAI,CAACC;AAAM;AACX,QAAI,SAAS;AACX,kBAAYA,MAAK,EAAE;AACnB,cAAQ,OAAO,SAAS,KAAK,OAAO,KAAK,gBAAgBA,KAAI;AAC7D,UAAI,gBAAgB;AAClB,uBAAe,UAAUA;AAAA,MAC3B;AAAA,IACF,OAAO;AACL,mBAAaA,OAAM;AAAA,QACjB,eAAe;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,QASf,MAAM,MAAM,KAAK,SAAS,IAAI,4BAA4B,eAAe,UAAU;AAAA,MACrF,CAAC;AAAA,IACH;AACA,0BAAsB,MAAM;AAC1B,YAAM,wBAAwB,sBAAsB;AACpD,YAAM,uBAAuB,yBAAyBA,UAAS,uBAAuB,CAAC,qBAAqB;AAC5G,UAAI,sBAAsB;AAGxB,QAAAA,MAAK,kBAAkB,OAAO,SAASA,MAAK,eAAe,OAAO,0BAA0B,YAAY;AAAA,UACtG,OAAO;AAAA,UACP,QAAQ;AAAA,QACV,IAAI,qBAAqB;AAAA,MAC3B;AAAA,IACF,CAAC;AAAA,EACH,CAAC;AACD,EAAA3B,OAAM,MAAM;AACV,aAAS,cAAc,KAAK,EAAE,MAAM;AAAA,MAClC,IAAI,gBAAgB;AAClB,mCAA2B;AAC3B,eAAO;AAAA,MACT;AAAA,IACF,CAAC;AAAA,EACH,GAAG,CAAC,CAAC;AAIL,EAAAA,OAAM,MAAM;AACV,QAAI,CAAC,SAAS;AACZ;AAAA,IACF;AACA,QAAI,QAAQ,UAAU;AACpB,UAAI,mBAAmB,WAAW,iBAAiB,MAAM;AAGvD,+BAAuB,UAAU;AACjC,mBAAW,aAAa;AAAA,MAC1B;AAAA,IACF,WAAW,mBAAmB,SAAS;AAIrC,eAAS,UAAU;AACnB,4BAAsB,QAAQ,IAAI;AAAA,IACpC;AAAA,EACF,GAAG,CAAC,SAAS,MAAM,UAAU,eAAe,UAAU,CAAC;AAIvD,EAAAA,OAAM,MAAM;AACV,QAAI,CAAC,SAAS;AACZ;AAAA,IACF;AACA,QAAI,QAAQ,UAAU;AACpB,UAAI,eAAe,MAAM;AACvB,uBAAe,UAAU;AACzB,YAAI,iBAAiB,MAAM;AACzB;AAAA,QACF;AAGA,YAAI,mBAAmB,SAAS;AAC9B,mBAAS,UAAU;AACnB,oBAAU,SAAS,QAAQ;AAAA,QAC7B;AAGA,YAAI,CAAC,mBAAmB,WAAW,mBAAmB,YAAY,OAAO,WAAW,QAAQ,mBAAmB,YAAY,QAAQ,OAAO,WAAW,OAAO;AAC1J,cAAI,OAAO;AACX,gBAAM,uBAAuB,MAAM;AACjC,gBAAI,QAAQ,QAAQ,CAAC,KAAK,MAAM;AAI9B,kBAAI,OAAO,GAAG;AACZ,sBAAM,YAAY,OAAO,wBAAwB;AACjD,0BAAU,oBAAoB;AAAA,cAChC;AACA;AAAA,YACF,OAAO;AACL,uBAAS,UAAU,OAAO,WAAW,QAAQ,0BAA0B,OAAO,SAAS,aAAa,GAAG,KAAK,SAAS,YAAY,SAAS,mBAAmB,OAAO,IAAI,YAAY,SAAS,mBAAmB,OAAO;AACvN,qBAAO,UAAU;AACjB,yBAAW,SAAS,OAAO;AAAA,YAC7B;AAAA,UACF;AACA,+BAAqB;AAAA,QACvB;AAAA,MACF,WAAW,CAAC,mBAAmB,SAAS,WAAW,GAAG;AACpD,iBAAS,UAAU;AACnB,kBAAU,SAAS,UAAU,uBAAuB,OAAO;AAC3D,+BAAuB,UAAU;AAAA,MACnC;AAAA,IACF;AAAA,EACF,GAAG,CAAC,SAAS,MAAM,UAAU,aAAa,eAAe,QAAQ,SAAS,aAAa,KAAK,YAAY,WAAW,kBAAkB,CAAC;AAItI,EAAAA,OAAM,MAAM;AACV,QAAI,aAAa;AACjB,QAAI,CAAC,WAAW,YAAY,CAAC,QAAQ,WAAW,CAAC,mBAAmB,SAAS;AAC3E;AAAA,IACF;AACA,UAAM,QAAQ,KAAK,SAAS;AAC5B,UAAM,UAAU,cAAc,MAAM,KAAK,UAAQ,KAAK,OAAO,QAAQ,MAAM,OAAO,UAAU,sBAAsB,YAAY,YAAY,OAAO,SAAS,oBAAoB,SAAS;AACvL,UAAM,WAAW,cAAc,YAAY,QAAQ,CAAC;AACpD,UAAM,uBAAuB,MAAM,KAAK,UAAQ,KAAK,WAAW,SAAS,KAAK,QAAQ,SAAS,UAAU,QAAQ,CAAC;AAClH,QAAI,UAAU,CAAC,wBAAwB,qBAAqB,SAAS;AACnE,aAAO,MAAM;AAAA,QACX,eAAe;AAAA,MACjB,CAAC;AAAA,IACH;AAAA,EACF,GAAG,CAAC,SAAS,UAAU,MAAM,UAAU,OAAO,CAAC;AAC/C,EAAAA,OAAM,MAAM;AACV,QAAI,CAAC,WAAW,CAAC,QAAQ,CAAC,WAAW;AAAU;AAC/C,aAAS,mBAAmB2B,OAAM;AAChC,mBAAaA,MAAK,EAAE;AACpB,UAAI,gBAAgB;AAClB,uBAAe,UAAUA;AAAA,MAC3B;AAAA,IACF;AACA,SAAK,OAAO,GAAG,gBAAgB,kBAAkB;AACjD,WAAO,MAAM;AACX,WAAK,OAAO,IAAI,gBAAgB,kBAAkB;AAAA,IACpD;AAAA,EACF,GAAG,CAAC,SAAS,MAAM,SAAS,UAAU,cAAc,CAAC;AACrD,EAAA3B,OAAM,MAAM;AACV,0BAAsB,UAAU;AAChC,uBAAmB,UAAU,CAAC,CAAC;AAAA,EACjC,CAAC;AACD,EAAAA,OAAM,MAAM;AACV,QAAI,CAAC,MAAM;AACT,aAAO,UAAU;AAAA,IACnB;AAAA,EACF,GAAG,CAAC,IAAI,CAAC;AACT,QAAM,iBAAiB,eAAe;AACtC,QAAM,OAAa,eAAQ,MAAM;AAC/B,aAAS,kBAAkB,eAAe;AACxC,UAAI,CAAC;AAAM;AACX,YAAMA,SAAQ,QAAQ,QAAQ,QAAQ,aAAa;AACnD,UAAIA,WAAU,IAAI;AAChB,mBAAWA,MAAK;AAAA,MAClB;AAAA,IACF;AACA,UAAM4B,SAAQ;AAAA,MACZ,QAAQ,MAAM;AACZ,YAAI;AAAA,UACF;AAAA,QACF,IAAI;AACJ,0BAAkB,aAAa;AAAA,MACjC;AAAA,MACA,SAAS,WAAS;AAChB,YAAI;AAAA,UACF;AAAA,QACF,IAAI;AACJ,eAAO,cAAc,MAAM;AAAA,UACzB,eAAe;AAAA,QACjB,CAAC;AAAA,MACH;AAAA;AAAA,MAEA,GAAI,oBAAoB;AAAA,QACtB,YAAY,OAAO;AACjB,cAAI;AAAA,YACF;AAAA,UACF,IAAI;AACJ,4BAAkB,aAAa;AAAA,QACjC;AAAA,QACA,eAAe,OAAO;AACpB,cAAI;AAAA,YACF;AAAA,UACF,IAAI;AACJ,cAAI,CAAC,qBAAqB,WAAW,gBAAgB,SAAS;AAC5D;AAAA,UACF;AACA,mBAAS,UAAU;AACnB,oBAAU,SAAS,QAAQ;AAC3B,qBAAW,IAAI;AACf,cAAI,CAAC,SAAS;AACZ,yBAAa,KAAK,SAAS,SAAS;AAAA,cAClC,eAAe;AAAA,YACjB,CAAC;AAAA,UACH;AAAA,QACF;AAAA,MACF;AAAA,IACF;AACA,WAAOA;AAAA,EACT,GAAG,CAAC,MAAM,MAAM,WAAW,kBAAkB,SAAS,YAAY,OAAO,CAAC;AAC1E,SAAa,eAAQ,MAAM;AACzB,QAAI,CAAC,SAAS;AACZ,aAAO,CAAC;AAAA,IACV;AACA,UAAMC,mBAAkB,mBAAmB;AAC3C,aAAS,UAAU,OAAO;AACxB,2BAAqB,UAAU;AAC/B,qBAAe,UAAU;AAKzB,UAAI,CAAC,cAAc,WAAW,MAAM,kBAAkB,KAAK,SAAS,SAAS;AAC3E;AAAA,MACF;AACA,UAAI,UAAU,2BAA2B,MAAM,KAAK,aAAa,GAAG,GAAG;AACrE,kBAAU,KAAK;AACf,qBAAa,OAAO,MAAM,WAAW;AACrC,YAAI,cAAc,YAAY,KAAK,CAAC,SAAS;AAC3C,uBAAa,MAAM;AAAA,QACrB;AACA;AAAA,MACF;AACA,YAAM,eAAe,SAAS;AAC9B,YAAM,WAAW,YAAY,SAASA,gBAAe;AACrD,YAAM,WAAW,YAAY,SAASA,gBAAe;AACrD,UAAI,MAAM,QAAQ,QAAQ;AACxB,kBAAU,KAAK;AACf,iBAAS,UAAU;AACnB,mBAAW,SAAS,OAAO;AAAA,MAC7B;AACA,UAAI,MAAM,QAAQ,OAAO;AACvB,kBAAU,KAAK;AACf,iBAAS,UAAU;AACnB,mBAAW,SAAS,OAAO;AAAA,MAC7B;AAGA,UAAI,OAAO,GAAG;AACZ,iBAAS,UAAU,sBAAsB,SAAS;AAAA,UAChD;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA,iBAAAA;AAAA,UACA;AAAA,UACA;AAAA,UACA,WAAW,SAAS;AAAA,UACpB,WAAW;AAAA,QACb,CAAC;AACD,mBAAW,SAAS,OAAO;AAC3B,YAAI,gBAAgB,QAAQ;AAC1B;AAAA,QACF;AAAA,MACF;AACA,UAAI,qBAAqB,MAAM,KAAK,WAAW,GAAG;AAChD,kBAAU,KAAK;AAGf,YAAI,QAAQ,CAAC,WAAW,cAAc,MAAM,cAAc,aAAa,MAAM,MAAM,eAAe;AAChG,mBAAS,UAAU,0BAA0B,MAAM,KAAK,aAAa,GAAG,IAAI,WAAW;AACvF,qBAAW,SAAS,OAAO;AAC3B;AAAA,QACF;AACA,YAAI,0BAA0B,MAAM,KAAK,aAAa,GAAG,GAAG;AAC1D,cAAI,MAAM;AACR,qBAAS,UAAU,gBAAgB,WAAW,eAAe,iBAAiB,QAAQ,QAAQ,SAAS,KAAK,WAAW,qBAAqB,SAAS;AAAA,cACnJ,eAAe;AAAA,cACf,iBAAAA;AAAA,YACF,CAAC;AAAA,UACH,OAAO;AACL,qBAAS,UAAU,KAAK,IAAI,UAAU,qBAAqB,SAAS;AAAA,cAClE,eAAe;AAAA,cACf,iBAAAA;AAAA,YACF,CAAC,CAAC;AAAA,UACJ;AAAA,QACF,OAAO;AACL,cAAI,MAAM;AACR,qBAAS,UAAU,gBAAgB,WAAW,eAAe,iBAAiB,KAAK,QAAQ,QAAQ,SAAS,WAAW,qBAAqB,SAAS;AAAA,cACnJ,eAAe;AAAA,cACf,WAAW;AAAA,cACX,iBAAAA;AAAA,YACF,CAAC;AAAA,UACH,OAAO;AACL,qBAAS,UAAU,KAAK,IAAI,UAAU,qBAAqB,SAAS;AAAA,cAClE,eAAe;AAAA,cACf,WAAW;AAAA,cACX,iBAAAA;AAAA,YACF,CAAC,CAAC;AAAA,UACJ;AAAA,QACF;AACA,YAAI,mBAAmB,SAAS,SAAS,OAAO,GAAG;AACjD,qBAAW,IAAI;AAAA,QACjB,OAAO;AACL,qBAAW,SAAS,OAAO;AAAA,QAC7B;AAAA,MACF;AAAA,IACF;AACA,aAAS,kBAAkB,OAAO;AAChC,UAAI,oBAAoB,UAAU,eAAe,MAAM,WAAW,GAAG;AACnE,2BAAmB,UAAU;AAAA,MAC/B;AAAA,IACF;AACA,aAAS,oBAAoB,OAAO;AAElC,yBAAmB,UAAU;AAC7B,UAAI,oBAAoB,UAAU,sBAAsB,MAAM,WAAW,GAAG;AAC1E,2BAAmB,UAAU;AAAA,MAC/B;AAAA,IACF;AACA,UAAM,2BAA2B,WAAW,QAAQ,kBAAkB;AAAA,MACpE,yBAAyB,aAAa;AAAA,IACxC;AACA,UAAM,aAAa,QAAQ,QAAQ,KAAK,CAAAF,WAASA,SAAQ,OAAO,SAASA,MAAK,QAAQ,QAAQ;AAC9F,WAAO;AAAA,MACL,WAAW;AAAA,QACT,GAAG;AAAA,QACH,UAAU,OAAO;AACf,+BAAqB,UAAU;AAC/B,gBAAM,aAAa,MAAM,IAAI,QAAQ,OAAO,MAAM;AAClD,gBAAM,iBAAiB,0BAA0B,MAAM,KAAK,aAAa,GAAG;AAC5E,gBAAM,kBAAkB,2BAA2B,MAAM,KAAK,aAAa,GAAG;AAC9E,gBAAM,YAAY,qBAAqB,MAAM,KAAK,WAAW;AAC7D,gBAAM,mBAAmB,SAAS,iBAAiB,cAAc,MAAM,QAAQ,WAAW,MAAM,IAAI,KAAK,MAAM;AAC/G,cAAI,WAAW,MAAM;AACnB,kBAAM,WAAW,QAAQ,OAAO,SAAS,KAAK,SAAS,QAAQ,KAAK,UAAQ,KAAK,YAAY,IAAI;AACjG,kBAAM,cAAc,QAAQ,WAAW,eAAe,KAAK,SAAS,SAAS,SAAS,EAAE,IAAI;AAC5F,gBAAI,cAAc,eAAe,gBAAgB;AAC/C,oBAAM,cAAc,IAAI,cAAc,WAAW;AAAA,gBAC/C,KAAK,MAAM;AAAA,gBACX,SAAS;AAAA,cACX,CAAC;AACD,kBAAI,kBAAkB,iBAAiB;AACrC,oBAAI,sBAAsB;AAC1B,sBAAM,oBAAoB,uBAAuB,YAAY,YAAY,OAAO,SAAS,qBAAqB,SAAS,kBAAkB,MAAM;AAC/I,sBAAM,eAAe,mBAAmB,CAAC,mBAAmB,wBAAwB,YAAY,YAAY,OAAO,SAAS,sBAAsB,SAAS,eAAe,iBAAiB,aAAa;AACxM,oBAAI,cAAc;AAChB,4BAAU,KAAK;AACf,+BAAa,cAAc,WAAW;AACtC,+BAAa,MAAS;AAAA,gBACxB;AAAA,cACF;AACA,kBAAI,aAAa,YAAY,SAAS;AACpC,oBAAI,YAAY,QAAQ,QAAQ,YAAY,YAAY,MAAM,kBAAkB,YAAY,QAAQ,SAAS,cAAc;AACzH,sBAAI;AACJ,4BAAU,KAAK;AACf,mBAAC,wBAAwB,YAAY,QAAQ,SAAS,iBAAiB,OAAO,SAAS,sBAAsB,cAAc,WAAW;AACtI;AAAA,gBACF;AAAA,cACF;AAAA,YACF;AACA,mBAAO,UAAU,KAAK;AAAA,UACxB;AAIA,cAAI,CAAC,QAAQ,CAAC,sBAAsB,YAAY;AAC9C;AAAA,UACF;AACA,cAAI,iBAAiB;AACnB,mBAAO,UAAU,UAAU,YAAY,OAAO,MAAM;AAAA,UACtD;AACA,cAAI,QAAQ;AACV,gBAAI,gBAAgB;AAClB,wBAAU,KAAK;AACf,kBAAI,MAAM;AACR,yBAAS,UAAU,YAAY,SAASE,gBAAe;AACvD,2BAAW,SAAS,OAAO;AAAA,cAC7B,OAAO;AACL,6BAAa,MAAM,MAAM,WAAW;AAAA,cACtC;AAAA,YACF;AACA;AAAA,UACF;AACA,cAAI,WAAW;AACb,gBAAI,iBAAiB,MAAM;AACzB,uBAAS,UAAU;AAAA,YACrB;AACA,sBAAU,KAAK;AACf,gBAAI,CAAC,QAAQ,oBAAoB;AAC/B,2BAAa,MAAM,MAAM,WAAW;AAAA,YACtC,OAAO;AACL,wBAAU,KAAK;AAAA,YACjB;AACA,gBAAI,MAAM;AACR,yBAAW,SAAS,OAAO;AAAA,YAC7B;AAAA,UACF;AAAA,QACF;AAAA,QACA,UAAU;AACR,cAAI,MAAM;AACR,uBAAW,IAAI;AAAA,UACjB;AAAA,QACF;AAAA,QACA,eAAe;AAAA,QACf,aAAa;AAAA,QACb,SAAS;AAAA,MACX;AAAA,MACA,UAAU;AAAA,QACR,oBAAoB,gBAAgB,SAAS,SAAY;AAAA,QACzD,GAAG;AAAA,QACH;AAAA,QACA,gBAAgB;AACd,+BAAqB,UAAU;AAAA,QACjC;AAAA,MACF;AAAA,MACA;AAAA,IACF;AAAA,EACF,GAAG,CAAC,cAAc,MAAM,UAAU,WAAW,oBAAoB,eAAe,SAAS,SAAS,aAAa,KAAK,SAAS,MAAM,gBAAgB,QAAQ,eAAe,oBAAoB,aAAa,MAAM,MAAM,iBAAiB,YAAY,cAAc,MAAM,MAAM,cAAc,CAAC;AAC/R;AAOA,SAAS,QAAQ,SAAS,OAAO;AAC/B,MAAI,UAAU,QAAQ;AACpB,YAAQ,CAAC;AAAA,EACX;AACA,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,EACF,IAAI;AACJ,QAAM;AAAA,IACJ,UAAU;AAAA,IACV,OAAO;AAAA,EACT,IAAI;AACJ,QAAM,cAAc,MAAM;AAC1B,SAAa,eAAQ,MAAM;AACzB,UAAM,gBAAgB;AAAA,MACpB,IAAI;AAAA,MACJ;AAAA,IACF;AACA,QAAI,CAAC,SAAS;AACZ,aAAO,CAAC;AAAA,IACV;AACA,QAAI,SAAS,WAAW;AACtB,aAAO;AAAA,QACL,WAAW;AAAA,UACT,oBAAoB,OAAO,aAAa;AAAA,QAC1C;AAAA,QACA,UAAU;AAAA,MACZ;AAAA,IACF;AACA,WAAO;AAAA,MACL,WAAW;AAAA,QACT,iBAAiB,OAAO,SAAS;AAAA,QACjC,iBAAiB,SAAS,gBAAgB,WAAW;AAAA,QACrD,iBAAiB,OAAO,aAAa;AAAA,QACrC,GAAI,SAAS,aAAa;AAAA,UACxB,MAAM;AAAA,QACR;AAAA,QACA,GAAI,SAAS,UAAU;AAAA,UACrB,IAAI;AAAA,QACN;AAAA,MACF;AAAA,MACA,UAAU;AAAA,QACR,GAAG;AAAA,QACH,GAAI,SAAS,UAAU;AAAA,UACrB,mBAAmB;AAAA,QACrB;AAAA,MACF;AAAA,IACF;AAAA,EACF,GAAG,CAAC,SAAS,MAAM,MAAM,YAAY,WAAW,CAAC;AACnD;AAIA,IAAM,uBAAuB,SAAO,IAAI,QAAQ,0BAA0B,CAAC,GAAG,SAAS,MAAM,MAAM,MAAM,EAAE,YAAY,CAAC;AACxH,SAAS,qBAAqB,WAAW,MAAM;AAC7C,SAAO,OAAO,cAAc,aAAa,UAAU,IAAI,IAAI;AAC7D;AACA,SAAS,gBAAgB,MAAM,YAAY;AACzC,QAAM,CAAC,WAAW,YAAY,IAAU,gBAAS,IAAI;AACrD,MAAI,QAAQ,CAAC,WAAW;AACtB,iBAAa,IAAI;AAAA,EACnB;AACA,EAAM,iBAAU,MAAM;AACpB,QAAI,CAAC,MAAM;AACT,YAAM,UAAU,WAAW,MAAM,aAAa,KAAK,GAAG,UAAU;AAChE,aAAO,MAAM,aAAa,OAAO;AAAA,IACnC;AAAA,EACF,GAAG,CAAC,MAAM,UAAU,CAAC;AACrB,SAAO;AACT;AAMA,SAAS,oBAAoB,SAAS,OAAO;AAC3C,MAAI,UAAU,QAAQ;AACpB,YAAQ,CAAC;AAAA,EACX;AACA,QAAM;AAAA,IACJ;AAAA,IACA,UAAU;AAAA,MACR;AAAA,IACF;AAAA,EACF,IAAI;AACJ,QAAM;AAAA,IACJ,WAAW;AAAA,EACb,IAAI;AACJ,QAAM,mBAAmB,OAAO,aAAa;AAC7C,QAAM,iBAAiB,mBAAmB,WAAW,SAAS,UAAU;AACxE,QAAM,CAAC,WAAW,YAAY,IAAU,gBAAS,KAAK;AACtD,QAAM,CAAC,QAAQ,SAAS,IAAU,gBAAS,WAAW;AACtD,QAAM,YAAY,gBAAgB,MAAM,aAAa;AAMrD,EAAA7B,OAAM,MAAM;AACV,QAAI,aAAa,CAAC,WAAW;AAC3B,gBAAU,WAAW;AAAA,IACvB;AAAA,EACF,GAAG,CAAC,WAAW,SAAS,CAAC;AACzB,EAAAA,OAAM,MAAM;AACV,QAAI,CAAC;AAAU;AACf,QAAI,MAAM;AACR,gBAAU,SAAS;AACnB,YAAM,QAAQ,sBAAsB,MAAM;AACxC,kBAAU,MAAM;AAAA,MAClB,CAAC;AACD,aAAO,MAAM;AACX,6BAAqB,KAAK;AAAA,MAC5B;AAAA,IACF,OAAO;AACL,mBAAa,IAAI;AACjB,gBAAU,OAAO;AAAA,IACnB;AAAA,EACF,GAAG,CAAC,MAAM,QAAQ,CAAC;AACnB,SAAO;AAAA,IACL;AAAA,IACA;AAAA,EACF;AACF;AAMA,SAAS,oBAAoB,SAAS,OAAO;AAC3C,MAAI,UAAU,QAAQ;AACpB,YAAQ,CAAC;AAAA,EACX;AACA,QAAM;AAAA,IACJ,SAAS,mBAAmB;AAAA,MAC1B,SAAS;AAAA,IACX;AAAA,IACA,MAAM;AAAA,IACN,OAAO;AAAA,IACP,QAAQ;AAAA,IACR,WAAW;AAAA,EACb,IAAI;AACJ,QAAM,YAAY,QAAQ;AAC1B,QAAM,OAAO,UAAU,MAAM,GAAG,EAAE,CAAC;AACnC,QAAM,SAAe,eAAQ,OAAO;AAAA,IAClC;AAAA,IACA;AAAA,EACF,IAAI,CAAC,MAAM,SAAS,CAAC;AACrB,QAAM,mBAAmB,OAAO,aAAa;AAC7C,QAAM,gBAAgB,mBAAmB,WAAW,SAAS,SAAS;AACtE,QAAM,iBAAiB,mBAAmB,WAAW,SAAS,UAAU;AACxE,QAAM,CAAC,QAAQ,SAAS,IAAU,gBAAS,OAAO;AAAA,IAChD,GAAG,qBAAqB,iBAAiB,MAAM;AAAA,IAC/C,GAAG,qBAAqB,kBAAkB,MAAM;AAAA,EAClD,EAAE;AACF,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,EACF,IAAI,oBAAoB,SAAS;AAAA,IAC/B;AAAA,EACF,CAAC;AACD,QAAM,aAAaS,cAAa,gBAAgB;AAChD,QAAM,UAAUA,cAAa,aAAa;AAC1C,QAAM,WAAWA,cAAa,cAAc;AAC5C,QAAM,YAAYA,cAAa,eAAe;AAC9C,EAAAT,OAAM,MAAM;AACV,UAAM,gBAAgB,qBAAqB,WAAW,SAAS,MAAM;AACrE,UAAM,cAAc,qBAAqB,SAAS,SAAS,MAAM;AACjE,UAAM,eAAe,qBAAqB,UAAU,SAAS,MAAM;AACnE,UAAM,aAAa,qBAAqB,QAAQ,SAAS,MAAM,KAAK,OAAO,KAAK,aAAa,EAAE,OAAO,CAAC,KAAK,QAAQ;AAClH,UAAI,GAAG,IAAI;AACX,aAAO;AAAA,IACT,GAAG,CAAC,CAAC;AACL,QAAI,WAAW,WAAW;AACxB,gBAAU,CAAA8B,aAAW;AAAA,QACnB,oBAAoBA,QAAO;AAAA,QAC3B,GAAG;AAAA,QACH,GAAG;AAAA,MACL,EAAE;AAAA,IACJ;AACA,QAAI,WAAW,QAAQ;AACrB,gBAAU;AAAA,QACR,oBAAoB,OAAO,KAAK,UAAU,EAAE,IAAI,oBAAoB,EAAE,KAAK,GAAG;AAAA,QAC9E,oBAAoB,eAAe;AAAA,QACnC,GAAG;AAAA,QACH,GAAG;AAAA,MACL,CAAC;AAAA,IACH;AACA,QAAI,WAAW,SAAS;AACtB,YAAMA,UAAS,eAAe;AAC9B,gBAAU;AAAA,QACR,oBAAoB,OAAO,KAAKA,OAAM,EAAE,IAAI,oBAAoB,EAAE,KAAK,GAAG;AAAA,QAC1E,oBAAoB,gBAAgB;AAAA,QACpC,GAAG;AAAA,QACH,GAAGA;AAAA,MACL,CAAC;AAAA,IACH;AAAA,EACF,GAAG,CAAC,eAAe,UAAU,YAAY,SAAS,WAAW,cAAc,QAAQ,MAAM,CAAC;AAC1F,SAAO;AAAA,IACL;AAAA,IACA;AAAA,EACF;AACF;AAOA,SAAS,aAAa,SAAS,OAAO;AACpC,MAAI;AACJ,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,EACF,IAAI;AACJ,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,IACA,SAAS;AAAA,IACT,gBAAgB;AAAA,IAChB,UAAU;AAAA,IACV,YAAY;AAAA,IACZ,UAAU;AAAA,IACV,aAAa,CAAC;AAAA,IACd,gBAAgB;AAAA,EAClB,IAAI;AACJ,QAAM,eAAqB,cAAO;AAClC,QAAM,YAAkB,cAAO,EAAE;AACjC,QAAM,eAAqB,eAAQ,OAAO,iBAAiB,OAAO,gBAAgB,gBAAgB,OAAO,OAAO,EAAE;AAClH,QAAM,gBAAsB,cAAO,IAAI;AACvC,QAAM,UAAU,eAAe,gBAAgB;AAC/C,QAAM,iBAAiB,eAAe,uBAAuB;AAC7D,QAAM,eAAerB,cAAa,SAAS;AAC3C,QAAM,gBAAgBA,cAAa,UAAU;AAC7C,EAAAT,OAAM,MAAM;AACV,QAAI,MAAM;AACR,mBAAa,aAAa,OAAO;AACjC,oBAAc,UAAU;AACxB,gBAAU,UAAU;AAAA,IACtB;AAAA,EACF,GAAG,CAAC,IAAI,CAAC;AACT,EAAAA,OAAM,MAAM;AAEV,QAAI,QAAQ,UAAU,YAAY,IAAI;AACpC,UAAI;AACJ,mBAAa,WAAW,QAAQ,iBAAiB,OAAO,gBAAgB,gBAAgB,OAAO,QAAQ;AAAA,IACzG;AAAA,EACF,GAAG,CAAC,MAAM,eAAe,WAAW,CAAC;AACrC,SAAa,eAAQ,MAAM;AACzB,QAAI,CAAC,SAAS;AACZ,aAAO,CAAC;AAAA,IACV;AACA,aAAS,gBAAgB,OAAO;AAC9B,UAAI,OAAO;AACT,YAAI,CAAC,QAAQ,QAAQ,QAAQ;AAC3B,kBAAQ,QAAQ,SAAS;AACzB,yBAAe,KAAK;AAAA,QACtB;AAAA,MACF,OAAO;AACL,YAAI,QAAQ,QAAQ,QAAQ;AAC1B,kBAAQ,QAAQ,SAAS;AACzB,yBAAe,KAAK;AAAA,QACtB;AAAA,MACF;AAAA,IACF;AACA,aAAS,iBAAiB,MAAM,aAAa,QAAQ;AACnD,YAAM,MAAM,aAAa,UAAU,aAAa,QAAQ,aAAa,MAAM,IAAI,YAAY,KAAK,WAAS,QAAQ,OAAO,SAAS,KAAK,kBAAkB,EAAE,QAAQ,OAAO,kBAAkB,CAAC,OAAO,CAAC;AACpM,aAAO,MAAM,KAAK,QAAQ,GAAG,IAAI;AAAA,IACnC;AACA,aAAS,UAAU,OAAO;AACxB,YAAM,cAAc,QAAQ;AAC5B,UAAI,UAAU,QAAQ,SAAS,KAAK,UAAU,QAAQ,CAAC,MAAM,KAAK;AAChE,YAAI,iBAAiB,aAAa,aAAa,UAAU,OAAO,MAAM,IAAI;AACxE,0BAAgB,KAAK;AAAA,QACvB,WAAW,MAAM,QAAQ,KAAK;AAC5B,oBAAU,KAAK;AAAA,QACjB;AAAA,MACF;AACA,UAAI,eAAe,QAAQ,cAAc,QAAQ,SAAS,MAAM,GAAG;AAAA,MAEnE,MAAM,IAAI,WAAW;AAAA,MAErB,MAAM,WAAW,MAAM,WAAW,MAAM,QAAQ;AAC9C;AAAA,MACF;AACA,UAAI,QAAQ,MAAM,QAAQ,KAAK;AAC7B,kBAAU,KAAK;AACf,wBAAgB,IAAI;AAAA,MACtB;AAIA,YAAM,oCAAoC,YAAY,MAAM,UAAQ;AAClE,YAAI,QAAQ;AACZ,eAAO,SAAS,SAAS,KAAK,CAAC,MAAM,OAAO,SAAS,OAAO,kBAAkB,SAAS,UAAU,KAAK,CAAC,MAAM,OAAO,SAAS,QAAQ,kBAAkB,KAAK;AAAA,MAC9J,CAAC;AAID,UAAI,qCAAqC,UAAU,YAAY,MAAM,KAAK;AACxE,kBAAU,UAAU;AACpB,qBAAa,UAAU,cAAc;AAAA,MACvC;AACA,gBAAU,WAAW,MAAM;AAC3B,mBAAa,aAAa,OAAO;AACjC,mBAAa,UAAU,WAAW,MAAM;AACtC,kBAAU,UAAU;AACpB,qBAAa,UAAU,cAAc;AACrC,wBAAgB,KAAK;AAAA,MACvB,GAAG,OAAO;AACV,YAAM,YAAY,aAAa;AAC/B,YAAMA,SAAQ,iBAAiB,aAAa,CAAC,GAAG,YAAY,OAAO,aAAa,KAAK,CAAC,GAAG,GAAG,YAAY,MAAM,IAAI,aAAa,KAAK,CAAC,CAAC,GAAG,UAAU,OAAO;AAC1J,UAAIA,WAAU,IAAI;AAChB,gBAAQA,MAAK;AACb,sBAAc,UAAUA;AAAA,MAC1B,WAAW,MAAM,QAAQ,KAAK;AAC5B,kBAAU,UAAU;AACpB,wBAAgB,KAAK;AAAA,MACvB;AAAA,IACF;AACA,WAAO;AAAA,MACL,WAAW;AAAA,QACT;AAAA,MACF;AAAA,MACA,UAAU;AAAA,QACR;AAAA,QACA,QAAQ,OAAO;AACb,cAAI,MAAM,QAAQ,KAAK;AACrB,4BAAgB,KAAK;AAAA,UACvB;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAAA,EACF,GAAG,CAAC,SAAS,MAAM,SAAS,SAAS,SAAS,eAAe,cAAc,SAAS,cAAc,CAAC;AACrG;AAEA,SAAS,gCAAgC,OAAO,QAAQ;AACtD,SAAO;AAAA,IACL,GAAG;AAAA,IACH,OAAO;AAAA,MACL,GAAG,MAAM;AAAA,MACT,UAAU;AAAA,QACR,GAAG,MAAM,MAAM;AAAA,QACf;AAAA,MACF;AAAA,IACF;AAAA,EACF;AACF;AAMA,IAAM,QAAQ,YAAU;AAAA,EACtB,MAAM;AAAA,EACN,SAAS;AAAA,EACT,MAAM,GAAG,OAAO;AACd,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,MACA;AAAA,MACA,QAAQ,cAAc;AAAA,MACtB,OAAAA,SAAQ;AAAA,MACR,kBAAkB;AAAA,MAClB,6BAA6B;AAAA,MAC7B;AAAA,MACA,GAAG;AAAA,IACL,IAAI;AACJ,UAAM;AAAA,MACJ;AAAA,MACA,UAAU;AAAA,QACR;AAAA,MACF;AAAA,IACF,IAAI;AACJ,UAAM,OAAO,QAAQ,QAAQA,MAAK;AAClC,QAAI,MAAuC;AACzC,UAAI,CAAC,MAAM,UAAU,WAAW,QAAQ,GAAG;AACzC,gBAAQ,KAAK,CAAC,iEAAiE,qBAAqB,EAAE,KAAK,GAAG,CAAC;AAAA,MACjH;AAAA,IACF;AACA,QAAI,CAAC,MAAM;AACT,aAAO,CAAC;AAAA,IACV;AACA,UAAM,WAAW;AAAA,MACf,GAAG;AAAA,MACH,GAAI,MAAM,OAAO,CAAC,KAAK,YAAY,SAAS,YAAY,MAAM,UAAU,SAAS,IAAI,KAAK,eAAe,IAAI,WAAW,EAAE,GAAG,KAAK;AAAA,IACpI;AACA,UAAM,MAAM,aAAa,OAAO,SAAS,UAAU,YAAY;AAC/D,UAAM,WAAW,MAAM,eAAe,gCAAgC,UAAU,GAAG,YAAY,GAAG,qBAAqB;AACvH,UAAM,cAAc,MAAM,eAAe,UAAU;AAAA,MACjD,GAAG;AAAA,MACH,gBAAgB;AAAA,IAClB,CAAC;AACD,UAAM,QAAQ,KAAK,IAAI,GAAG,SAAS,GAAG;AACtC,UAAM,QAAQ,SAAS,IAAI;AAC3B,UAAM,YAAY,KAAK,IAAI,GAAG,GAAG,eAAe,QAAQ,KAAK,IAAI,GAAG,SAAS,MAAM,CAAC;AACpF,OAAG,MAAM,YAAY,YAAY;AACjC,OAAG,YAAY;AAGf,QAAI,kBAAkB;AACpB,UAAI,GAAG,eAAe,KAAK,eAAe,KAAK,IAAI,iBAAiB,QAAQ,QAAQ,SAAS,CAAC,IAAI,KAAK,YAAY,OAAO,CAAC,8BAA8B,YAAY,UAAU,CAAC,4BAA4B;AAC1M,yCAAU,MAAM,iBAAiB,IAAI,CAAC;AAAA,MACxC,OAAO;AACL,yCAAU,MAAM,iBAAiB,KAAK,CAAC;AAAA,MACzC;AAAA,IACF;AACA,QAAI,aAAa;AACf,kBAAY,UAAU,MAAM,eAAe,gCAAgC;AAAA,QACzE,GAAG;AAAA,QACH,GAAG;AAAA,MACL,GAAG,GAAG,YAAY,GAAG,qBAAqB;AAAA,IAC5C;AACA,WAAO;AAAA,MACL,GAAG;AAAA,IACL;AAAA,EACF;AACF;AAMA,SAAS,eAAe,SAAS,OAAO;AACtC,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,EACF,IAAI;AACJ,QAAM;AAAA,IACJ,UAAU;AAAA,IACV;AAAA,IACA;AAAA,IACA,UAAU;AAAA,EACZ,IAAI;AACJ,QAAM,WAAW,eAAe,iBAAiB;AACjD,QAAM,yBAA+B,cAAO,KAAK;AACjD,QAAM,mBAAyB,cAAO,IAAI;AAC1C,QAAM,qBAA2B,cAAO,IAAI;AAC5C,EAAM,iBAAU,MAAM;AACpB,QAAI,CAAC,SAAS;AACZ;AAAA,IACF;AACA,aAAS,QAAQ,GAAG;AAClB,UAAI,EAAE,WAAW,CAAC,MAAM,YAAY,WAAW,MAAM;AACnD;AAAA,MACF;AACA,YAAM,KAAK,EAAE;AACb,YAAM,UAAU,YAAY,QAAQ,OAAO;AAC3C,YAAM,aAAa,YAAY,QAAQ,UAAU;AACjD,YAAM,kBAAkB,GAAG,eAAe,GAAG;AAC7C,YAAM,OAAO,KAAK,IAAI,KAAK;AAC3B,YAAM,SAAS,KAAK,IAAI,QAAQ;AAChC,UAAI,GAAG,gBAAgB,GAAG,cAAc;AACtC;AAAA,MACF;AACA,UAAI,CAAC,WAAW,KAAK,KAAK,CAAC,cAAc,KAAK,GAAG;AAC/C,UAAE,eAAe;AACjB,yCAAU,MAAM;AACd,mBAAS,OAAK,IAAI,KAAK,MAAM,EAAE,IAAI,kBAAkB,IAAI,CAAC;AAAA,QAC5D,CAAC;AAAA,MACH,WAAW,WAAW,KAAK,aAAa,CAAC,GAAG;AAG1C,WAAG,aAAa;AAAA,MAClB;AAAA,IACF;AACA,UAAM,MAAM,aAAa,OAAO,SAAS,UAAU,YAAY,SAAS;AACxE,QAAI,QAAQ,IAAI;AACd,SAAG,iBAAiB,SAAS,OAAO;AAGpC,4BAAsB,MAAM;AAC1B,yBAAiB,UAAU,GAAG;AAC9B,YAAI,YAAY,WAAW,MAAM;AAC/B,6BAAmB,UAAU;AAAA,YAC3B,GAAG,YAAY;AAAA,UACjB;AAAA,QACF;AAAA,MACF,CAAC;AACD,aAAO,MAAM;AACX,yBAAiB,UAAU;AAC3B,2BAAmB,UAAU;AAC7B,WAAG,oBAAoB,SAAS,OAAO;AAAA,MACzC;AAAA,IACF;AAAA,EACF,GAAG,CAAC,SAAS,MAAM,SAAS,UAAU,aAAa,WAAW,QAAQ,CAAC;AACvE,SAAa,eAAQ,MAAM;AACzB,QAAI,CAAC,SAAS;AACZ,aAAO,CAAC;AAAA,IACV;AACA,WAAO;AAAA,MACL,UAAU;AAAA,QACR,YAAY;AACV,iCAAuB,UAAU;AAAA,QACnC;AAAA,QACA,UAAU;AACR,iCAAuB,UAAU;AAAA,QACnC;AAAA,QACA,gBAAgB;AACd,iCAAuB,UAAU;AAAA,QACnC;AAAA,QACA,WAAW;AACT,gBAAM,MAAM,aAAa,OAAO,SAAS,UAAU,YAAY,SAAS;AACxE,cAAI,CAAC,YAAY,WAAW,CAAC,MAAM,CAAC,uBAAuB,SAAS;AAClE;AAAA,UACF;AACA,cAAI,iBAAiB,YAAY,MAAM;AACrC,kBAAM,aAAa,GAAG,YAAY,iBAAiB;AACnD,gBAAI,YAAY,QAAQ,SAAS,QAAQ,aAAa,MAAM,YAAY,QAAQ,MAAM,QAAQ,aAAa,GAAG;AAC5G,+CAAU,MAAM,SAAS,OAAK,IAAI,UAAU,CAAC;AAAA,YAC/C;AAAA,UACF;AAGA,gCAAsB,MAAM;AAC1B,6BAAiB,UAAU,GAAG;AAAA,UAChC,CAAC;AAAA,QACH;AAAA,MACF;AAAA,IACF;AAAA,EACF,GAAG,CAAC,SAAS,aAAa,SAAS,UAAU,WAAW,QAAQ,CAAC;AACnE;AAEA,SAAS,iBAAiB,OAAO,SAAS;AACxC,QAAM,CAAC,GAAG,CAAC,IAAI;AACf,MAAI+B,YAAW;AACf,QAAM,SAAS,QAAQ;AACvB,WAAS,IAAI,GAAG,IAAI,SAAS,GAAG,IAAI,QAAQ,IAAI,KAAK;AACnD,UAAM,CAAC,IAAI,EAAE,IAAI,QAAQ,CAAC,KAAK,CAAC,GAAG,CAAC;AACpC,UAAM,CAAC,IAAI,EAAE,IAAI,QAAQ,CAAC,KAAK,CAAC,GAAG,CAAC;AACpC,UAAM,YAAY,MAAM,MAAM,MAAM,KAAK,MAAM,KAAK,OAAO,IAAI,OAAO,KAAK,MAAM;AACjF,QAAI,WAAW;AACb,MAAAA,YAAW,CAACA;AAAA,IACd;AAAA,EACF;AACA,SAAOA;AACT;AACA,SAAS,SAAS,OAAO,MAAM;AAC7B,SAAO,MAAM,CAAC,KAAK,KAAK,KAAK,MAAM,CAAC,KAAK,KAAK,IAAI,KAAK,SAAS,MAAM,CAAC,KAAK,KAAK,KAAK,MAAM,CAAC,KAAK,KAAK,IAAI,KAAK;AAClH;AAMA,SAAS,YAAY,SAAS;AAC5B,MAAI,YAAY,QAAQ;AACtB,cAAU,CAAC;AAAA,EACb;AACA,QAAM;AAAA,IACJ,SAAS;AAAA,IACT,qBAAqB;AAAA,IACrB,gBAAgB;AAAA,EAClB,IAAI;AACJ,MAAIC;AACJ,MAAI,YAAY;AAChB,MAAI,QAAQ;AACZ,MAAI,QAAQ;AACZ,MAAI,iBAAiB,YAAY,IAAI;AACrC,WAAS,eAAe,GAAG,GAAG;AAC5B,UAAM,cAAc,YAAY,IAAI;AACpC,UAAM,cAAc,cAAc;AAClC,QAAI,UAAU,QAAQ,UAAU,QAAQ,gBAAgB,GAAG;AACzD,cAAQ;AACR,cAAQ;AACR,uBAAiB;AACjB,aAAO;AAAA,IACT;AACA,UAAM,SAAS,IAAI;AACnB,UAAM,SAAS,IAAI;AACnB,UAAM,WAAW,KAAK,KAAK,SAAS,SAAS,SAAS,MAAM;AAC5D,UAAM,QAAQ,WAAW;AAEzB,YAAQ;AACR,YAAQ;AACR,qBAAiB;AACjB,WAAO;AAAA,EACT;AACA,QAAM,KAAK,UAAQ;AACjB,QAAI;AAAA,MACF;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF,IAAI;AACJ,WAAO,SAAS,YAAY,OAAO;AACjC,eAAS,QAAQ;AACf,qBAAaA,UAAS;AACtB,gBAAQ;AAAA,MACV;AACA,mBAAaA,UAAS;AACtB,UAAI,CAAC,SAAS,gBAAgB,CAAC,SAAS,YAAY,aAAa,QAAQ,KAAK,QAAQ,KAAK,MAAM;AAC/F;AAAA,MACF;AACA,YAAM;AAAA,QACJ;AAAA,QACA;AAAA,MACF,IAAI;AACJ,YAAM,cAAc,CAAC,SAAS,OAAO;AACrC,YAAM,SAAS,UAAU,KAAK;AAC9B,YAAM,UAAU,MAAM,SAAS;AAC/B,YAAM,mBAAmB,SAAS,SAAS,UAAU,MAAM;AAC3D,YAAM,oBAAoB,SAAS,SAAS,cAAc,MAAM;AAChE,YAAM,UAAU,SAAS,aAAa,sBAAsB;AAC5D,YAAM,OAAO,SAAS,SAAS,sBAAsB;AACrD,YAAM,OAAO,UAAU,MAAM,GAAG,EAAE,CAAC;AACnC,YAAM,uBAAuB,IAAI,KAAK,QAAQ,KAAK,QAAQ;AAC3D,YAAM,wBAAwB,IAAI,KAAK,SAAS,KAAK,SAAS;AAC9D,YAAM,sBAAsB,SAAS,aAAa,OAAO;AACzD,YAAM,kBAAkB,KAAK,QAAQ,QAAQ;AAC7C,YAAM,mBAAmB,KAAK,SAAS,QAAQ;AAC/C,YAAM,QAAQ,kBAAkB,UAAU,MAAM;AAChD,YAAM,SAAS,kBAAkB,UAAU,MAAM;AACjD,YAAM,OAAO,mBAAmB,UAAU,MAAM;AAChD,YAAM,UAAU,mBAAmB,UAAU,MAAM;AACnD,UAAI,kBAAkB;AACpB,oBAAY;AACZ,YAAI,CAAC,SAAS;AACZ;AAAA,QACF;AAAA,MACF;AACA,UAAI,mBAAmB;AACrB,oBAAY;AAAA,MACd;AACA,UAAI,qBAAqB,CAAC,SAAS;AACjC,oBAAY;AACZ;AAAA,MACF;AAIA,UAAI,WAAW,UAAU,MAAM,aAAa,KAAK,SAAS,SAAS,UAAU,MAAM,aAAa,GAAG;AACjG;AAAA,MACF;AAGA,UAAI,QAAQ,YAAY,KAAK,SAAS,SAAS,MAAM,EAAE,KAAK,WAAS;AACnE,YAAI;AAAA,UACF;AAAA,QACF,IAAI;AACJ,eAAO,WAAW,OAAO,SAAS,QAAQ;AAAA,MAC5C,CAAC,GAAG;AACF;AAAA,MACF;AAMA,UAAI,SAAS,SAAS,KAAK,QAAQ,SAAS,KAAK,SAAS,YAAY,KAAK,QAAQ,MAAM,KAAK,SAAS,UAAU,KAAK,QAAQ,QAAQ,KAAK,SAAS,WAAW,KAAK,QAAQ,OAAO,GAAG;AACpL,eAAO,MAAM;AAAA,MACf;AAOA,UAAI,WAAW,CAAC;AAChB,cAAQ,MAAM;AAAA,QACZ,KAAK;AACH,qBAAW,CAAC,CAAC,MAAM,QAAQ,MAAM,CAAC,GAAG,CAAC,MAAM,KAAK,SAAS,CAAC,GAAG,CAAC,OAAO,KAAK,SAAS,CAAC,GAAG,CAAC,OAAO,QAAQ,MAAM,CAAC,CAAC;AAChH;AAAA,QACF,KAAK;AACH,qBAAW,CAAC,CAAC,MAAM,KAAK,MAAM,CAAC,GAAG,CAAC,MAAM,QAAQ,SAAS,CAAC,GAAG,CAAC,OAAO,QAAQ,SAAS,CAAC,GAAG,CAAC,OAAO,KAAK,MAAM,CAAC,CAAC;AAChH;AAAA,QACF,KAAK;AACH,qBAAW,CAAC,CAAC,KAAK,QAAQ,GAAG,MAAM,GAAG,CAAC,KAAK,QAAQ,GAAG,GAAG,GAAG,CAAC,QAAQ,OAAO,GAAG,GAAG,GAAG,CAAC,QAAQ,OAAO,GAAG,MAAM,CAAC;AAChH;AAAA,QACF,KAAK;AACH,qBAAW,CAAC,CAAC,QAAQ,QAAQ,GAAG,MAAM,GAAG,CAAC,QAAQ,QAAQ,GAAG,GAAG,GAAG,CAAC,KAAK,OAAO,GAAG,GAAG,GAAG,CAAC,KAAK,OAAO,GAAG,MAAM,CAAC;AAChH;AAAA,MACJ;AACA,eAAS,WAAW,OAAO;AACzB,YAAI,CAACd,IAAGC,EAAC,IAAI;AACb,gBAAQ,MAAM;AAAA,UACZ,KAAK,OACH;AACE,kBAAM,iBAAiB,CAAC,kBAAkBD,KAAI,SAAS,IAAI,uBAAuBA,KAAI,SAAS,IAAIA,KAAI,SAAS,GAAGC,KAAI,SAAS,CAAC;AACjI,kBAAM,iBAAiB,CAAC,kBAAkBD,KAAI,SAAS,IAAI,uBAAuBA,KAAI,SAAS,IAAIA,KAAI,SAAS,GAAGC,KAAI,SAAS,CAAC;AACjI,kBAAM,eAAe,CAAC,CAAC,KAAK,MAAM,uBAAuB,KAAK,SAAS,SAAS,kBAAkB,KAAK,SAAS,SAAS,KAAK,GAAG,GAAG,CAAC,KAAK,OAAO,uBAAuB,kBAAkB,KAAK,SAAS,SAAS,KAAK,MAAM,KAAK,SAAS,MAAM,CAAC;AACjP,mBAAO,CAAC,gBAAgB,gBAAgB,GAAG,YAAY;AAAA,UACzD;AAAA,UACF,KAAK,UACH;AACE,kBAAM,iBAAiB,CAAC,kBAAkBD,KAAI,SAAS,IAAI,uBAAuBA,KAAI,SAAS,IAAIA,KAAI,SAAS,GAAGC,KAAI,MAAM;AAC7H,kBAAM,iBAAiB,CAAC,kBAAkBD,KAAI,SAAS,IAAI,uBAAuBA,KAAI,SAAS,IAAIA,KAAI,SAAS,GAAGC,KAAI,MAAM;AAC7H,kBAAM,eAAe,CAAC,CAAC,KAAK,MAAM,uBAAuB,KAAK,MAAM,SAAS,kBAAkB,KAAK,MAAM,SAAS,KAAK,MAAM,GAAG,CAAC,KAAK,OAAO,uBAAuB,kBAAkB,KAAK,MAAM,SAAS,KAAK,SAAS,KAAK,MAAM,MAAM,CAAC;AAC3O,mBAAO,CAAC,gBAAgB,gBAAgB,GAAG,YAAY;AAAA,UACzD;AAAA,UACF,KAAK,QACH;AACE,kBAAM,iBAAiB,CAACD,KAAI,SAAS,GAAG,mBAAmBC,KAAI,SAAS,IAAI,wBAAwBA,KAAI,SAAS,IAAIA,KAAI,SAAS,CAAC;AACnI,kBAAM,iBAAiB,CAACD,KAAI,SAAS,GAAG,mBAAmBC,KAAI,SAAS,IAAI,wBAAwBA,KAAI,SAAS,IAAIA,KAAI,SAAS,CAAC;AACnI,kBAAM,eAAe,CAAC,CAAC,wBAAwB,KAAK,QAAQ,SAAS,mBAAmB,KAAK,QAAQ,SAAS,KAAK,MAAM,KAAK,GAAG,GAAG,CAAC,wBAAwB,mBAAmB,KAAK,QAAQ,SAAS,KAAK,OAAO,KAAK,QAAQ,QAAQ,KAAK,MAAM,CAAC;AACnP,mBAAO,CAAC,GAAG,cAAc,gBAAgB,cAAc;AAAA,UACzD;AAAA,UACF,KAAK,SACH;AACE,kBAAM,iBAAiB,CAACD,KAAI,QAAQ,mBAAmBC,KAAI,SAAS,IAAI,wBAAwBA,KAAI,SAAS,IAAIA,KAAI,SAAS,CAAC;AAC/H,kBAAM,iBAAiB,CAACD,KAAI,QAAQ,mBAAmBC,KAAI,SAAS,IAAI,wBAAwBA,KAAI,SAAS,IAAIA,KAAI,SAAS,CAAC;AAC/H,kBAAM,eAAe,CAAC,CAAC,wBAAwB,KAAK,OAAO,SAAS,mBAAmB,KAAK,OAAO,SAAS,KAAK,OAAO,KAAK,GAAG,GAAG,CAAC,wBAAwB,mBAAmB,KAAK,OAAO,SAAS,KAAK,QAAQ,KAAK,OAAO,QAAQ,KAAK,MAAM,CAAC;AACjP,mBAAO,CAAC,gBAAgB,gBAAgB,GAAG,YAAY;AAAA,UACzD;AAAA,QACJ;AAAA,MACF;AACA,UAAI,iBAAiB,CAAC,SAAS,OAAO,GAAG,QAAQ,GAAG;AAClD;AAAA,MACF,WAAW,aAAa,CAAC,qBAAqB;AAC5C,eAAO,MAAM;AAAA,MACf;AACA,UAAI,CAAC,WAAW,eAAe;AAC7B,cAAM,cAAc,eAAe,MAAM,SAAS,MAAM,OAAO;AAC/D,cAAM,uBAAuB;AAC7B,YAAI,gBAAgB,QAAQ,cAAc,sBAAsB;AAC9D,iBAAO,MAAM;AAAA,QACf;AAAA,MACF;AACA,UAAI,CAAC,iBAAiB,CAAC,SAAS,OAAO,GAAG,WAAW,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG;AAC7D,cAAM;AAAA,MACR,WAAW,CAAC,aAAa,eAAe;AACtC,QAAAa,aAAY,OAAO,WAAW,OAAO,EAAE;AAAA,MACzC;AAAA,IACF;AAAA,EACF;AACA,KAAG,YAAY;AAAA,IACb;AAAA,EACF;AACA,SAAO;AACT;", "names": ["React", "import_react", "getComputedStyle", "activeElement", "sides", "alignments", "placements", "platform", "max", "offset", "placements", "alignment", "sides", "side", "placement", "overflow", "getBoundingClientRect", "top", "bottom", "left", "right", "width", "height", "x", "y", "min", "sides", "alignments", "placements", "min", "max", "floor", "getNodeName", "isNode", "getWindow", "getDocumentElement", "isElement", "isHTMLElement", "isShadowRoot", "getComputedStyle", "getParentNode", "isLastTraversableNode", "getComputedStyle", "isHTMLElement", "isElement", "getWindow", "getDocumentElement", "getNodeName", "max", "getParentNode", "isLastTraversableNode", "min", "window", "timeoutId", "floor", "autoPlacement", "shift", "flip", "size", "hide", "arrow", "inline", "limitShift", "computePosition", "arrow", "platform", "computePosition", "data", "candidateSelectors", "candidate<PERSON><PERSON><PERSON>", "join", "NoElement", "Element", "matches", "prototype", "msMatchesSelector", "webkitMatchesSelector", "getRootNode", "element", "_element$getRootNode", "call", "ownerDocument", "isInert", "node", "lookUp", "_node$getAttribute", "inertAtt", "getAttribute", "inert", "result", "parentNode", "isContentEditable", "_node$getAttribute2", "attValue", "getCandidates", "el", "<PERSON><PERSON><PERSON><PERSON>", "filter", "candidates", "Array", "slice", "apply", "querySelectorAll", "unshift", "getCandidatesIteratively", "elements", "options", "elementsToCheck", "from", "length", "shift", "tagName", "assigned", "assignedElements", "content", "children", "nestedCandidates", "flatten", "push", "scopeParent", "validCandidate", "includes", "shadowRoot", "getShadowRoot", "validShadowRoot", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "hasTabIndex", "isNaN", "parseInt", "getTabIndex", "Error", "tabIndex", "test", "getSortOrderTabIndex", "isScope", "sortOrderedTabbables", "a", "b", "documentOrder", "isInput", "isHiddenInput", "type", "isDetailsWithSummary", "r", "some", "child", "getCheckedRadio", "nodes", "form", "i", "checked", "isTabbableRadio", "name", "radioScope", "queryRadios", "radioSet", "window", "CSS", "escape", "err", "console", "error", "message", "isRadio", "isNonTabbableRadio", "isNodeAttached", "_nodeRoot", "nodeRoot", "nodeRootHost", "host", "attached", "_nodeRootHost", "_nodeRootHost$ownerDo", "_node$ownerDocument", "contains", "_nodeRoot2", "_nodeRootHost2", "_nodeRootHost2$ownerD", "isZeroArea", "_node$getBoundingClie", "getBoundingClientRect", "width", "height", "isHidden", "_ref", "displayCheck", "getComputedStyle", "visibility", "isDirectSummary", "nodeUnderDetails", "parentElement", "originalNode", "rootNode", "assignedSlot", "getClientRects", "isDisabledFromFieldset", "disabled", "item", "isNodeMatchingSelectorFocusable", "isNodeMatchingSelectorTabbable", "isValidShadowRootTabbable", "shadowHostNode", "sortByOrder", "regularTabbables", "orderedTabbables", "for<PERSON>ach", "candidateTabindex", "sort", "reduce", "acc", "sortable", "concat", "tabbable", "container", "bind", "focusableCandidateSelector", "candidateSelectors", "concat", "join", "import_react_dom", "index", "offset", "map", "Composite", "CompositeItem", "React", "FloatingArrow", "arrow", "isRTL", "useLatestRef", "FocusGuard", "id", "root", "portalContext", "uniqueId", "Visually<PERSON><PERSON><PERSON><PERSON><PERSON>iss", "returnFocus", "FloatingOverlay", "x", "y", "pointerType", "getComputedStyle", "useFloating", "open", "node", "listRef", "indexRef", "item", "props", "disabledIndices", "styles", "isInside", "timeoutId"]}