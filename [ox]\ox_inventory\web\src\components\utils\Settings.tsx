import { autoUpdate, flip, FloatingPortal, offset, shift, useFloating, useTransitionStyles } from '@floating-ui/react';
import React, { useEffect } from 'react';
import { useAppDispatch, useAppSelector } from '../../store';
import HelpTooltip from '../inventory/HelpTooltip';
import SettingsPopup from '../inventory/SettingsPopup';

const Settings: React.FC = () => {
  const hoverData = useAppSelector((state) => state.menu);
  const dispatch = useAppDispatch();
  var x, y;

  const { refs, context, floatingStyles } = useFloating({
    whileElementsMounted: autoUpdate,
    middleware: [flip(), shift(), offset({ mainAxis: 10, crossAxis: 10 })],
    open: hoverData.settings,
    placement: 'right-end',
  });

  const { isMounted, styles } = useTransitionStyles(context, {
    duration: 200,
  });

  const handleMouseMove = ({ clientX, clientY }: MouseEvent | React.MouseEvent<unknown, MouseEvent>) => {
    refs.setPositionReference({
      getBoundingClientRect() {
        x = clientX
        y = clientY

        return {
          width: 0,
          height: 0,
          x: clientX,
          y: clientY,
          left: clientX,
          top: clientY,
          right: clientX,
          bottom: clientY,
        };
      },
    });
  };

  useEffect(() => {
    var elem = document.getElementById('settings');
    elem.addEventListener('click', handleMouseMove,);

    return () => {
      elem.removeEventListener('click', handleMouseMove,);
    };
  }, []);

  return (
    <>
      {isMounted && hoverData.settings && (
        <SettingsPopup
          ref={refs.setFloating}
          style={{position:'absolute', left:`50vw`, top:`0px`, transform:"translate("+x+"px,"+y+"px)", zIndex:'100', pointerEvents:'all' }}
        />
      )}
    </>
  );
};

export default Settings;
