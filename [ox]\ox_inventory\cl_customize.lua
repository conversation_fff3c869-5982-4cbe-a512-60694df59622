require '@qbx_core.modules.playerdata'

local events = {
    hat = 'hat',
    visor = 'visor',
    hair = 'hair',
    mask = 'mask',
    glasses = 'glasses',
    ear = 'ear',
    shirt = 'shirt',
    pants = 'pants',
    vest = 'vest',
    gloves = 'gloves',
    watch = 'watch',
    bracelet = 'bracelet',
    shoes = 'shoes',
    necklace = 'neck',
    bag = 'bagoff',
}


RegisterNUICallback('clothesToggle', function(data, cb)
    if events[data.toggle] ~= '' then
        -- TriggerClientEvent(events[data.toggle])
        ExecuteCommand(events[data.toggle])
    end
	cb(1)
end)

function OnInventoryOpen(source)
    local apartmentType = 'Apartment'
    local apartment = 'No apartment found'
    if GetResourceState('snipe-motel') == 'started' then
        apartmentType = 'Motel Room'
        apartment = exports["snipe-motel"]:currentPlayerRoom() or 'No Room at motel.'
    end
    SendNUIMessage({
        action = 'init',
        data = {
			locale = uiLocales,
			items = ItemData,
			imagepath = client.imagepath,
			inventoryimagepath = client.inventoryimagepath,
			playerData = {
				h1 = QBX.PlayerData.charinfo.firstname..' '..QBX.PlayerData.charinfo.lastname,
				h2 = QBX.PlayerData.citizenid,
				t1 = "Date of Birth",
				b1 = QBX.PlayerData.charinfo.birthdate,
				t2 = apartmentType,
				b2 = apartment,
				t3 = "Bank",
				b3 = QBX.PlayerData.money.bank,
				t4 = "Job",
				b4 = qbx.string.capitalize(QBX.PlayerData.job.name)..' - '..QBX.PlayerData.job.grade.name..' ['..(QBX.PlayerData.job.onduty and 'On Duty' or 'Off Duty')..']',
			},
            prefs = json.decode(GetResourceKvpString('preferences'))
		}
    })
end

AddEventHandler('onInventoryOpen', OnInventoryOpen)

-- PrimaryColor: string,
-- StreamerMode: boolean,

RegisterNUICallback('setPreferences', function(data, cb) 
    cb(1)
    if data then
        SetResourceKvp('preferences', json.encode(data))
    end
end)

-- onFocusCapture={(e)=>fetchNui('focusKeyboard')} onBlur={(e)=>fetchNui('releaseKeyboard')}

RegisterNUICallback('focusKeyboard', function(data, cb) 
    cb(1)
    SetNuiFocusKeepInput(false)
end)

RegisterNUICallback('releaseKeyboard', function(data, cb) 
    cb(1)
    SetNuiFocusKeepInput(true)
end)

RegisterNUICallback('openDonoMenu', function(data, cb)
    if events[data.toggle] ~= '' then
        client.closeInventory()
        TriggerEvent('ak4y-vipSystemv2:openMenu')
        -- ExecuteCommand(events[data.toggle])
    end
	cb(1)
end)