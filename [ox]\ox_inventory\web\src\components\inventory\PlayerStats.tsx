import BodyDamage from './BodyDamage';
import PlayerInfo from './PlayerInfo';
import BodyIcon from '../../../inventory-images/body.png';
import InventoryGrid from './InventoryGrid';
import { useAppSelector } from '../../store';

const PlayerStats: React.FC = () => {

  return (
    <section className="status">
      <div className="body-wrapper">
        <BodyDamage />
      </div>
      <PlayerInfo />
    </section>
  );
};

export default PlayerStats;
