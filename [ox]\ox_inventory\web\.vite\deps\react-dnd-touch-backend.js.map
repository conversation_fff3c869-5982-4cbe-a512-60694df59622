{"version": 3, "sources": ["../../node_modules/react-dnd-touch-backend/src/interfaces.ts", "../../node_modules/react-dnd-touch-backend/src/OptionsReader.ts", "../../node_modules/react-dnd-touch-backend/src/utils/math.ts", "../../node_modules/react-dnd-touch-backend/src/utils/predicates.ts", "../../node_modules/react-dnd-touch-backend/src/utils/offsets.ts", "../../node_modules/react-dnd-touch-backend/src/utils/supportsPassive.ts", "../../node_modules/react-dnd-touch-backend/src/TouchBackendImpl.ts", "../../node_modules/react-dnd-touch-backend/src/index.ts"], "sourcesContent": ["export interface EventName {\n\tstart?: 'mousedown' | 'touchstart'\n\tmove?: 'mousemove' | 'touchmove'\n\tend?: 'mouseup' | 'touchend'\n\tcontextmenu?: 'contextmenu'\n\tkeydown?: 'keydown'\n}\n\nexport interface TouchBackendOptions {\n\tdelay: number\n\tdelayTouchStart: number\n\tenableTouchEvents: boolean\n\tenableKeyboardEvents: boolean\n\tenableMouseEvents: boolean\n\tignoreContextMenu: boolean\n\tenableHoverOutsideTarget: boolean\n\tdelayMouseStart: number\n\ttouchSlop: number\n\tscrollAngleRanges?: AngleRange[] | undefined\n\trootElement: Node | undefined\n\n\tgetDropTargetElementsAtPoint?:\n\t\t| undefined\n\t\t| ((x: number, y: number, dropTargets: HTMLElement[]) => HTMLElement[])\n}\n\nexport interface AngleRange {\n\tstart: number\n\tend: number\n}\n\nexport enum ListenerType {\n\tmouse = 'mouse',\n\ttouch = 'touch',\n\tkeyboard = 'keyboard',\n}\n\nexport interface TouchBackendContext {\n\twindow?: Window\n\tdocument?: Document\n}\n", "import type {\n\t<PERSON><PERSON><PERSON><PERSON><PERSON>,\n\tTouchBackendContext,\n\tTouchBackendOptions,\n} from './interfaces.js'\n\nexport class OptionsReader implements TouchBackendOptions {\n\tpublic constructor(\n\t\tprivate args: Partial<TouchBackendOptions>,\n\t\tprivate context: TouchBackendContext,\n\t) {}\n\n\tpublic get delay(): number {\n\t\treturn this.args.delay ?? 0\n\t}\n\n\tpublic get scrollAngleRanges(): AngleRange[] | undefined {\n\t\treturn this.args.scrollAngleRanges\n\t}\n\n\tpublic get getDropTargetElementsAtPoint():\n\t\t| ((x: number, y: number, elements: HTMLElement[]) => HTMLElement[])\n\t\t| undefined {\n\t\treturn this.args.getDropTargetElementsAtPoint\n\t}\n\n\tpublic get ignoreContextMenu(): boolean {\n\t\treturn this.args.ignoreContextMenu ?? false\n\t}\n\n\tpublic get enableHoverOutsideTarget(): boolean {\n\t\treturn this.args.enableHoverOutsideTarget ?? false\n\t}\n\n\tpublic get enableKeyboardEvents(): boolean {\n\t\treturn this.args.enableKeyboardEvents ?? false\n\t}\n\n\tpublic get enableMouseEvents(): boolean {\n\t\treturn this.args.enableMouseEvents ?? false\n\t}\n\n\tpublic get enableTouchEvents(): boolean {\n\t\treturn this.args.enableTouchEvents ?? true\n\t}\n\n\tpublic get touchSlop(): number {\n\t\treturn this.args.touchSlop || 0\n\t}\n\n\tpublic get delayTouchStart(): number {\n\t\treturn this.args?.delayTouchStart ?? this.args?.delay ?? 0\n\t}\n\n\tpublic get delayMouseStart(): number {\n\t\treturn this.args?.delayMouseStart ?? this.args?.delay ?? 0\n\t}\n\n\tpublic get window(): Window | undefined {\n\t\tif (this.context && this.context.window) {\n\t\t\treturn this.context.window\n\t\t} else if (typeof window !== 'undefined') {\n\t\t\treturn window\n\t\t}\n\t\treturn undefined\n\t}\n\n\tpublic get document(): Document | undefined {\n\t\tif (this.context?.document) {\n\t\t\treturn this.context.document\n\t\t}\n\n\t\tif (this.window) {\n\t\t\treturn this.window.document\n\t\t}\n\n\t\treturn undefined\n\t}\n\n\tpublic get rootElement(): Node | undefined {\n\t\treturn this.args?.rootElement || (this.document as any as Node)\n\t}\n}\n", "import type { AngleRange } from '../interfaces.js'\n\nexport function distance(\n\tx1: number,\n\ty1: number,\n\tx2: number,\n\ty2: number,\n): number {\n\treturn Math.sqrt(\n\t\tMath.pow(Math.abs(x2 - x1), 2) + Math.pow(Math.abs(y2 - y1), 2),\n\t)\n}\n\nexport function inAngleRanges(\n\tx1: number,\n\ty1: number,\n\tx2: number,\n\ty2: number,\n\tangleRanges: AngleRange[] | undefined,\n): boolean {\n\tif (!angleRanges) {\n\t\treturn false\n\t}\n\n\tconst angle = (Math.atan2(y2 - y1, x2 - x1) * 180) / Math.PI + 180\n\n\tfor (let i = 0; i < angleRanges.length; ++i) {\n\t\tconst ar = angleRanges[i]\n\t\tif (\n\t\t\tar &&\n\t\t\t(ar.start == null || angle >= ar.start) &&\n\t\t\t(ar.end == null || angle <= ar.end)\n\t\t) {\n\t\t\treturn true\n\t\t}\n\t}\n\n\treturn false\n}\n", "// Used for MouseEvent.buttons (note the s on the end).\nconst MouseButtons = {\n\tLeft: 1,\n\tRight: 2,\n\tCenter: 4,\n}\n\n// Used for e.button (note the lack of an s on the end).\nconst MouseButton = {\n\tLeft: 0,\n\tCenter: 1,\n\tRight: 2,\n}\n\n/**\n * Only touch events and mouse events where the left button is pressed should initiate a drag.\n * @param {MouseEvent | TouchEvent} e The event\n */\nexport function eventShouldStartDrag(e: MouseEvent): boolean {\n\t// For touch events, button will be undefined. If e.button is defined,\n\t// then it should be MouseButton.Left.\n\treturn e.button === undefined || e.button === MouseButton.Left\n}\n\n/**\n * Only touch events and mouse events where the left mouse button is no longer held should end a drag.\n * It's possible the user mouse downs with the left mouse button, then mouse down and ups with the right mouse button.\n * We don't want releasing the right mouse button to end the drag.\n * @param {MouseEvent | TouchEvent} e The event\n */\nexport function eventShouldEndDrag(e: MouseEvent): boolean {\n\t// Touch events will have buttons be undefined, while mouse events will have e.buttons's left button\n\t// bit field unset if the left mouse button has been released\n\treturn e.buttons === undefined || (e.buttons & MouseButtons.Left) === 0\n}\n\nexport function isTouchEvent(\n\te: Touch | TouchEvent | MouseEvent,\n): e is TouchEvent {\n\treturn !!(e as TouchEvent).targetTouches\n}\n", "import type { XYCoord } from 'dnd-core'\n\nimport { isTouchEvent } from './predicates.js'\n\nconst ELEMENT_NODE = 1\n\nexport function getNodeClientOffset(node: Element): XYCoord | undefined {\n\tconst el = node.nodeType === ELEMENT_NODE ? node : node.parentElement\n\tif (!el) {\n\t\treturn undefined\n\t}\n\tconst { top, left } = el.getBoundingClientRect()\n\treturn { x: left, y: top }\n}\n\nexport function getEventClientTouchOffset(\n\te: TouchEvent,\n\tlastTargetTouchFallback?: Touch,\n): XYCoord | undefined {\n\tif (e.targetTouches.length === 1) {\n\t\treturn getEventClientOffset(e.targetTouches[0] as Touch)\n\t} else if (lastTargetTouchFallback && e.touches.length === 1) {\n\t\tif ((e.touches[0] as Touch).target === lastTargetTouchFallback.target) {\n\t\t\treturn getEventClientOffset(e.touches[0] as Touch)\n\t\t}\n\t}\n\treturn\n}\n\nexport function getEventClientOffset(\n\te: TouchEvent | Touch | MouseEvent,\n\tlastTargetTouchFallback?: Touch,\n): XYCoord | undefined {\n\tif (isTouchEvent(e)) {\n\t\treturn getEventClientTouchOffset(e, lastTargetTouchFallback)\n\t} else {\n\t\treturn {\n\t\t\tx: e.clientX,\n\t\t\ty: e.clientY,\n\t\t}\n\t}\n}\n", "export const supportsPassive = ((): boolean => {\n\t// simular to <PERSON><PERSON><PERSON><PERSON>'s test\n\tlet supported = false\n\ttry {\n\t\taddEventListener(\n\t\t\t'test',\n\t\t\t() => {\n\t\t\t\t// do nothing\n\t\t\t},\n\t\t\tObject.defineProperty({}, 'passive', {\n\t\t\t\tget() {\n\t\t\t\t\tsupported = true\n\t\t\t\t\treturn true\n\t\t\t\t},\n\t\t\t}),\n\t\t)\n\t} catch (e) {\n\t\t// do nothing\n\t}\n\treturn supported\n})()\n", "import { invariant } from '@react-dnd/invariant'\nimport type {\n\tBackend,\n\tDragD<PERSON><PERSON><PERSON>,\n\tDragDropManager,\n\tDragDropMonitor,\n\tIdentifier,\n\tUnsubscribe,\n\tXYCoord,\n} from 'dnd-core'\n\nimport type {\n\tEventName,\n\tTouchBackendContext,\n\tTouchBackendOptions,\n} from './interfaces.js'\nimport { ListenerType } from './interfaces.js'\nimport { OptionsReader } from './OptionsReader.js'\nimport { distance, inAngleRanges } from './utils/math.js'\nimport { getEventClientOffset, getNodeClientOffset } from './utils/offsets.js'\nimport {\n\teventShouldEndDrag,\n\teventShouldStartDrag,\n\tisTouchEvent,\n} from './utils/predicates.js'\nimport { supportsPassive } from './utils/supportsPassive.js'\n\nconst eventNames: Record<ListenerType, EventName> = {\n\t[ListenerType.mouse]: {\n\t\tstart: 'mousedown',\n\t\tmove: 'mousemove',\n\t\tend: 'mouseup',\n\t\tcontextmenu: 'contextmenu',\n\t},\n\t[ListenerType.touch]: {\n\t\tstart: 'touchstart',\n\t\tmove: 'touchmove',\n\t\tend: 'touchend',\n\t},\n\t[ListenerType.keyboard]: {\n\t\tkeydown: 'keydown',\n\t},\n}\n\nexport class TouchBackendImpl implements Backend {\n\tprivate options: OptionsReader\n\n\t// React-DnD Dependencies\n\tprivate actions: DragDropActions\n\tprivate monitor: DragDropMonitor\n\n\t// Internal State\n\tprivate static isSetUp: boolean\n\tpublic sourceNodes: Map<Identifier, HTMLElement>\n\tpublic sourcePreviewNodes: Map<string, HTMLElement>\n\tpublic sourcePreviewNodeOptions: Map<string, any>\n\tpublic targetNodes: Map<string, HTMLElement>\n\tprivate _mouseClientOffset: Partial<XYCoord>\n\tprivate _isScrolling: boolean\n\tprivate listenerTypes: ListenerType[]\n\tprivate moveStartSourceIds: string[] | undefined\n\tprivate waitingForDelay: boolean | undefined\n\tprivate timeout: ReturnType<typeof setTimeout> | undefined\n\tprivate dragOverTargetIds: string[] | undefined\n\tprivate draggedSourceNode: HTMLElement | undefined\n\tprivate draggedSourceNodeRemovalObserver: MutationObserver | undefined\n\n\t// Patch for iOS 13, discussion over #1585\n\tprivate lastTargetTouchFallback: Touch | undefined\n\n\tpublic constructor(\n\t\tmanager: DragDropManager,\n\t\tcontext: TouchBackendContext,\n\t\toptions: Partial<TouchBackendOptions>,\n\t) {\n\t\tthis.options = new OptionsReader(options, context)\n\t\tthis.actions = manager.getActions()\n\t\tthis.monitor = manager.getMonitor()\n\n\t\tthis.sourceNodes = new Map()\n\t\tthis.sourcePreviewNodes = new Map()\n\t\tthis.sourcePreviewNodeOptions = new Map()\n\t\tthis.targetNodes = new Map()\n\t\tthis.listenerTypes = []\n\t\tthis._mouseClientOffset = {}\n\t\tthis._isScrolling = false\n\n\t\tif (this.options.enableMouseEvents) {\n\t\t\tthis.listenerTypes.push(ListenerType.mouse)\n\t\t}\n\n\t\tif (this.options.enableTouchEvents) {\n\t\t\tthis.listenerTypes.push(ListenerType.touch)\n\t\t}\n\n\t\tif (this.options.enableKeyboardEvents) {\n\t\t\tthis.listenerTypes.push(ListenerType.keyboard)\n\t\t}\n\t}\n\n\t/**\n\t * Generate profiling statistics for the HTML5Backend.\n\t */\n\tpublic profile(): Record<string, number> {\n\t\treturn {\n\t\t\tsourceNodes: this.sourceNodes.size,\n\t\t\tsourcePreviewNodes: this.sourcePreviewNodes.size,\n\t\t\tsourcePreviewNodeOptions: this.sourcePreviewNodeOptions.size,\n\t\t\ttargetNodes: this.targetNodes.size,\n\t\t\tdragOverTargetIds: this.dragOverTargetIds?.length || 0,\n\t\t}\n\t}\n\n\t// public for test\n\tpublic get document(): Document | undefined {\n\t\treturn this.options.document\n\t}\n\n\tpublic setup(): void {\n\t\tconst root = this.options.rootElement\n\t\tif (!root) {\n\t\t\treturn\n\t\t}\n\n\t\tinvariant(\n\t\t\t!TouchBackendImpl.isSetUp,\n\t\t\t'Cannot have two Touch backends at the same time.',\n\t\t)\n\t\tTouchBackendImpl.isSetUp = true\n\n\t\tthis.addEventListener(root, 'start', this.getTopMoveStartHandler() as any)\n\t\tthis.addEventListener(\n\t\t\troot,\n\t\t\t'start',\n\t\t\tthis.handleTopMoveStartCapture as any,\n\t\t\ttrue,\n\t\t)\n\t\tthis.addEventListener(root, 'move', this.handleTopMove as any)\n\t\tthis.addEventListener(root, 'move', this.handleTopMoveCapture, true)\n\t\tthis.addEventListener(\n\t\t\troot,\n\t\t\t'end',\n\t\t\tthis.handleTopMoveEndCapture as any,\n\t\t\ttrue,\n\t\t)\n\n\t\tif (this.options.enableMouseEvents && !this.options.ignoreContextMenu) {\n\t\t\tthis.addEventListener(\n\t\t\t\troot,\n\t\t\t\t'contextmenu',\n\t\t\t\tthis.handleTopMoveEndCapture as any,\n\t\t\t)\n\t\t}\n\n\t\tif (this.options.enableKeyboardEvents) {\n\t\t\tthis.addEventListener(\n\t\t\t\troot,\n\t\t\t\t'keydown',\n\t\t\t\tthis.handleCancelOnEscape as any,\n\t\t\t\ttrue,\n\t\t\t)\n\t\t}\n\t}\n\n\tpublic teardown(): void {\n\t\tconst root = this.options.rootElement\n\t\tif (!root) {\n\t\t\treturn\n\t\t}\n\n\t\tTouchBackendImpl.isSetUp = false\n\t\tthis._mouseClientOffset = {}\n\n\t\tthis.removeEventListener(\n\t\t\troot,\n\t\t\t'start',\n\t\t\tthis.handleTopMoveStartCapture as any,\n\t\t\ttrue,\n\t\t)\n\t\tthis.removeEventListener(root, 'start', this.handleTopMoveStart as any)\n\t\tthis.removeEventListener(root, 'move', this.handleTopMoveCapture, true)\n\t\tthis.removeEventListener(root, 'move', this.handleTopMove as any)\n\t\tthis.removeEventListener(\n\t\t\troot,\n\t\t\t'end',\n\t\t\tthis.handleTopMoveEndCapture as any,\n\t\t\ttrue,\n\t\t)\n\n\t\tif (this.options.enableMouseEvents && !this.options.ignoreContextMenu) {\n\t\t\tthis.removeEventListener(\n\t\t\t\troot,\n\t\t\t\t'contextmenu',\n\t\t\t\tthis.handleTopMoveEndCapture as any,\n\t\t\t)\n\t\t}\n\n\t\tif (this.options.enableKeyboardEvents) {\n\t\t\tthis.removeEventListener(\n\t\t\t\troot,\n\t\t\t\t'keydown',\n\t\t\t\tthis.handleCancelOnEscape as any,\n\t\t\t\ttrue,\n\t\t\t)\n\t\t}\n\n\t\tthis.uninstallSourceNodeRemovalObserver()\n\t}\n\n\tprivate addEventListener<K extends keyof EventName>(\n\t\tsubject: Node,\n\t\tevent: K,\n\t\thandler: (e: any) => void,\n\t\tcapture = false,\n\t) {\n\t\tconst options = supportsPassive ? { capture, passive: false } : capture\n\n\t\tthis.listenerTypes.forEach(function (listenerType) {\n\t\t\tconst evt = eventNames[listenerType][event]\n\n\t\t\tif (evt) {\n\t\t\t\tsubject.addEventListener(evt as any, handler as any, options)\n\t\t\t}\n\t\t})\n\t}\n\n\tprivate removeEventListener<K extends keyof EventName>(\n\t\tsubject: Node,\n\t\tevent: K,\n\t\thandler: (e: any) => void,\n\t\tcapture = false,\n\t) {\n\t\tconst options = supportsPassive ? { capture, passive: false } : capture\n\n\t\tthis.listenerTypes.forEach(function (listenerType) {\n\t\t\tconst evt = eventNames[listenerType][event]\n\n\t\t\tif (evt) {\n\t\t\t\tsubject.removeEventListener(evt as any, handler as any, options)\n\t\t\t}\n\t\t})\n\t}\n\n\tpublic connectDragSource(sourceId: string, node: HTMLElement): Unsubscribe {\n\t\tconst handleMoveStart = this.handleMoveStart.bind(this, sourceId)\n\t\tthis.sourceNodes.set(sourceId, node)\n\n\t\tthis.addEventListener(node, 'start', handleMoveStart)\n\n\t\treturn (): void => {\n\t\t\tthis.sourceNodes.delete(sourceId)\n\t\t\tthis.removeEventListener(node, 'start', handleMoveStart)\n\t\t}\n\t}\n\n\tpublic connectDragPreview(\n\t\tsourceId: string,\n\t\tnode: HTMLElement,\n\t\toptions: unknown,\n\t): Unsubscribe {\n\t\tthis.sourcePreviewNodeOptions.set(sourceId, options)\n\t\tthis.sourcePreviewNodes.set(sourceId, node)\n\n\t\treturn (): void => {\n\t\t\tthis.sourcePreviewNodes.delete(sourceId)\n\t\t\tthis.sourcePreviewNodeOptions.delete(sourceId)\n\t\t}\n\t}\n\n\tpublic connectDropTarget(targetId: string, node: HTMLElement): Unsubscribe {\n\t\tconst root = this.options.rootElement\n\t\tif (!this.document || !root) {\n\t\t\treturn (): void => {\n\t\t\t\t/* noop */\n\t\t\t}\n\t\t}\n\n\t\tconst handleMove = (e: MouseEvent | TouchEvent) => {\n\t\t\tif (!this.document || !root || !this.monitor.isDragging()) {\n\t\t\t\treturn\n\t\t\t}\n\n\t\t\tlet coords\n\n\t\t\t/**\n\t\t\t * Grab the coordinates for the current mouse/touch position\n\t\t\t */\n\t\t\tswitch (e.type) {\n\t\t\t\tcase eventNames.mouse.move:\n\t\t\t\t\tcoords = {\n\t\t\t\t\t\tx: (e as MouseEvent).clientX,\n\t\t\t\t\t\ty: (e as MouseEvent).clientY,\n\t\t\t\t\t}\n\t\t\t\t\tbreak\n\n\t\t\t\tcase eventNames.touch.move:\n\t\t\t\t\tcoords = {\n\t\t\t\t\t\tx: (e as TouchEvent).touches[0]?.clientX || 0,\n\t\t\t\t\t\ty: (e as TouchEvent).touches[0]?.clientY || 0,\n\t\t\t\t\t}\n\t\t\t\t\tbreak\n\t\t\t}\n\n\t\t\t/**\n\t\t\t * Use the coordinates to grab the element the drag ended on.\n\t\t\t * If the element is the same as the target node (or any of it's children) then we have hit a drop target and can handle the move.\n\t\t\t */\n\t\t\tconst droppedOn =\n\t\t\t\tcoords != null\n\t\t\t\t\t? this.document.elementFromPoint(coords.x, coords.y)\n\t\t\t\t\t: undefined\n\t\t\tconst childMatch = droppedOn && node.contains(droppedOn)\n\n\t\t\tif (droppedOn === node || childMatch) {\n\t\t\t\treturn this.handleMove(e, targetId)\n\t\t\t}\n\t\t}\n\n\t\t/**\n\t\t * Attaching the event listener to the body so that touchmove will work while dragging over multiple target elements.\n\t\t */\n\t\tthis.addEventListener(this.document.body, 'move', handleMove as any)\n\t\tthis.targetNodes.set(targetId, node)\n\n\t\treturn (): void => {\n\t\t\tif (this.document) {\n\t\t\t\tthis.targetNodes.delete(targetId)\n\t\t\t\tthis.removeEventListener(this.document.body, 'move', handleMove as any)\n\t\t\t}\n\t\t}\n\t}\n\n\tprivate getSourceClientOffset = (sourceId: string): XYCoord | undefined => {\n\t\tconst element = this.sourceNodes.get(sourceId)\n\t\treturn element && getNodeClientOffset(element)\n\t}\n\n\tpublic handleTopMoveStartCapture = (e: Event): void => {\n\t\tif (!eventShouldStartDrag(e as MouseEvent)) {\n\t\t\treturn\n\t\t}\n\n\t\tthis.moveStartSourceIds = []\n\t}\n\n\tpublic handleMoveStart = (sourceId: string): void => {\n\t\t// Just because we received an event doesn't necessarily mean we need to collect drag sources.\n\t\t// We only collect start collecting drag sources on touch and left mouse events.\n\t\tif (Array.isArray(this.moveStartSourceIds)) {\n\t\t\tthis.moveStartSourceIds.unshift(sourceId)\n\t\t}\n\t}\n\n\tprivate getTopMoveStartHandler() {\n\t\tif (!this.options.delayTouchStart && !this.options.delayMouseStart) {\n\t\t\treturn this.handleTopMoveStart\n\t\t}\n\n\t\treturn this.handleTopMoveStartDelay\n\t}\n\n\tpublic handleTopMoveStart = (e: MouseEvent | TouchEvent): void => {\n\t\tif (!eventShouldStartDrag(e as MouseEvent)) {\n\t\t\treturn\n\t\t}\n\n\t\t// Don't prematurely preventDefault() here since it might:\n\t\t// 1. Mess up scrolling\n\t\t// 2. Mess up long tap (which brings up context menu)\n\t\t// 3. If there's an anchor link as a child, tap won't be triggered on link\n\n\t\tconst clientOffset = getEventClientOffset(e)\n\t\tif (clientOffset) {\n\t\t\tif (isTouchEvent(e)) {\n\t\t\t\tthis.lastTargetTouchFallback = e.targetTouches[0]\n\t\t\t}\n\t\t\tthis._mouseClientOffset = clientOffset\n\t\t}\n\t\tthis.waitingForDelay = false\n\t}\n\n\tpublic handleTopMoveStartDelay = (e: Event): void => {\n\t\tif (!eventShouldStartDrag(e as MouseEvent)) {\n\t\t\treturn\n\t\t}\n\n\t\tconst delay =\n\t\t\te.type === eventNames.touch.start\n\t\t\t\t? this.options.delayTouchStart\n\t\t\t\t: this.options.delayMouseStart\n\t\tthis.timeout = setTimeout(\n\t\t\tthis.handleTopMoveStart.bind(this, e as any),\n\t\t\tdelay,\n\t\t) as any as ReturnType<typeof setTimeout>\n\t\tthis.waitingForDelay = true\n\t}\n\n\tpublic handleTopMoveCapture = (): void => {\n\t\tthis.dragOverTargetIds = []\n\t}\n\n\tpublic handleMove = (\n\t\t_evt: MouseEvent | TouchEvent,\n\t\ttargetId: string,\n\t): void => {\n\t\tif (this.dragOverTargetIds) {\n\t\t\tthis.dragOverTargetIds.unshift(targetId)\n\t\t}\n\t}\n\n\tpublic handleTopMove = (e: TouchEvent | MouseEvent): void => {\n\t\tif (this.timeout) {\n\t\t\tclearTimeout(this.timeout)\n\t\t}\n\t\tif (!this.document || this.waitingForDelay) {\n\t\t\treturn\n\t\t}\n\t\tconst { moveStartSourceIds, dragOverTargetIds } = this\n\t\tconst enableHoverOutsideTarget = this.options.enableHoverOutsideTarget\n\n\t\tconst clientOffset = getEventClientOffset(e, this.lastTargetTouchFallback)\n\n\t\tif (!clientOffset) {\n\t\t\treturn\n\t\t}\n\n\t\t// If the touch move started as a scroll, or is is between the scroll angles\n\t\tif (\n\t\t\tthis._isScrolling ||\n\t\t\t(!this.monitor.isDragging() &&\n\t\t\t\tinAngleRanges(\n\t\t\t\t\tthis._mouseClientOffset.x || 0,\n\t\t\t\t\tthis._mouseClientOffset.y || 0,\n\t\t\t\t\tclientOffset.x,\n\t\t\t\t\tclientOffset.y,\n\t\t\t\t\tthis.options.scrollAngleRanges,\n\t\t\t\t))\n\t\t) {\n\t\t\tthis._isScrolling = true\n\t\t\treturn\n\t\t}\n\n\t\t// If we're not dragging and we've moved a little, that counts as a drag start\n\t\tif (\n\t\t\t!this.monitor.isDragging() &&\n\t\t\t// eslint-disable-next-line no-prototype-builtins\n\t\t\tthis._mouseClientOffset.hasOwnProperty('x') &&\n\t\t\tmoveStartSourceIds &&\n\t\t\tdistance(\n\t\t\t\tthis._mouseClientOffset.x || 0,\n\t\t\t\tthis._mouseClientOffset.y || 0,\n\t\t\t\tclientOffset.x,\n\t\t\t\tclientOffset.y,\n\t\t\t) > (this.options.touchSlop ? this.options.touchSlop : 0)\n\t\t) {\n\t\t\tthis.moveStartSourceIds = undefined\n\n\t\t\tthis.actions.beginDrag(moveStartSourceIds, {\n\t\t\t\tclientOffset: this._mouseClientOffset,\n\t\t\t\tgetSourceClientOffset: this.getSourceClientOffset,\n\t\t\t\tpublishSource: false,\n\t\t\t})\n\t\t}\n\n\t\tif (!this.monitor.isDragging()) {\n\t\t\treturn\n\t\t}\n\n\t\tconst sourceNode = this.sourceNodes.get(\n\t\t\tthis.monitor.getSourceId() as string,\n\t\t)\n\t\tthis.installSourceNodeRemovalObserver(sourceNode)\n\t\tthis.actions.publishDragSource()\n\n\t\tif (e.cancelable) e.preventDefault()\n\n\t\t// Get the node elements of the hovered DropTargets\n\t\tconst dragOverTargetNodes: HTMLElement[] = (dragOverTargetIds || [])\n\t\t\t.map((key) => this.targetNodes.get(key))\n\t\t\t.filter((e) => !!e) as HTMLElement[]\n\n\t\t// Get the a ordered list of nodes that are touched by\n\t\tconst elementsAtPoint = this.options.getDropTargetElementsAtPoint\n\t\t\t? this.options.getDropTargetElementsAtPoint(\n\t\t\t\t\tclientOffset.x,\n\t\t\t\t\tclientOffset.y,\n\t\t\t\t\tdragOverTargetNodes,\n\t\t\t  )\n\t\t\t: this.document.elementsFromPoint(clientOffset.x, clientOffset.y)\n\t\t// Extend list with parents that are not receiving elementsFromPoint events (size 0 elements and svg groups)\n\t\tconst elementsAtPointExtended: Element[] = []\n\t\tfor (const nodeId in elementsAtPoint) {\n\t\t\t// eslint-disable-next-line no-prototype-builtins\n\t\t\tif (!elementsAtPoint.hasOwnProperty(nodeId)) {\n\t\t\t\tcontinue\n\t\t\t}\n\t\t\tlet currentNode: Element | undefined | null = elementsAtPoint[nodeId]\n\t\t\tif (currentNode != null) {\n\t\t\t\telementsAtPointExtended.push(currentNode)\n\t\t\t}\n\t\t\twhile (currentNode) {\n\t\t\t\tcurrentNode = currentNode.parentElement\n\t\t\t\tif (\n\t\t\t\t\tcurrentNode &&\n\t\t\t\t\telementsAtPointExtended.indexOf(currentNode) === -1\n\t\t\t\t) {\n\t\t\t\t\telementsAtPointExtended.push(currentNode)\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t\tconst orderedDragOverTargetIds: string[] = elementsAtPointExtended\n\t\t\t// Filter off nodes that arent a hovered DropTargets nodes\n\t\t\t.filter((node) => dragOverTargetNodes.indexOf(node as HTMLElement) > -1)\n\t\t\t// Map back the nodes elements to targetIds\n\t\t\t.map((node) => this._getDropTargetId(node))\n\t\t\t// Filter off possible null rows\n\t\t\t.filter((node) => !!node)\n\t\t\t.filter((id, index, ids) => ids.indexOf(id) === index) as string[]\n\n\t\t// Invoke hover for drop targets when source node is still over and pointer is outside\n\t\tif (enableHoverOutsideTarget) {\n\t\t\tfor (const targetId in this.targetNodes) {\n\t\t\t\tconst targetNode = this.targetNodes.get(targetId)\n\t\t\t\tif (\n\t\t\t\t\tsourceNode &&\n\t\t\t\t\ttargetNode &&\n\t\t\t\t\ttargetNode.contains(sourceNode) &&\n\t\t\t\t\torderedDragOverTargetIds.indexOf(targetId) === -1\n\t\t\t\t) {\n\t\t\t\t\torderedDragOverTargetIds.unshift(targetId)\n\t\t\t\t\tbreak\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\n\t\t// Reverse order because dnd-core reverse it before calling the DropTarget drop methods\n\t\torderedDragOverTargetIds.reverse()\n\n\t\tthis.actions.hover(orderedDragOverTargetIds, {\n\t\t\tclientOffset: clientOffset,\n\t\t})\n\t}\n\n\t/**\n\t *\n\t * visible for testing\n\t */\n\tpublic _getDropTargetId = (node: Element): Identifier | undefined => {\n\t\tconst keys = this.targetNodes.keys()\n\t\tlet next = keys.next()\n\t\twhile (next.done === false) {\n\t\t\tconst targetId = next.value\n\t\t\tif (node === this.targetNodes.get(targetId)) {\n\t\t\t\treturn targetId\n\t\t\t} else {\n\t\t\t\tnext = keys.next()\n\t\t\t}\n\t\t}\n\t\treturn undefined\n\t}\n\n\tpublic handleTopMoveEndCapture = (e: Event): void => {\n\t\tthis._isScrolling = false\n\t\tthis.lastTargetTouchFallback = undefined\n\n\t\tif (!eventShouldEndDrag(e as MouseEvent)) {\n\t\t\treturn\n\t\t}\n\n\t\tif (!this.monitor.isDragging() || this.monitor.didDrop()) {\n\t\t\tthis.moveStartSourceIds = undefined\n\t\t\treturn\n\t\t}\n\n\t\tif (e.cancelable) e.preventDefault()\n\n\t\tthis._mouseClientOffset = {}\n\n\t\tthis.uninstallSourceNodeRemovalObserver()\n\t\tthis.actions.drop()\n\t\tthis.actions.endDrag()\n\t}\n\n\tpublic handleCancelOnEscape = (e: KeyboardEvent): void => {\n\t\tif (e.key === 'Escape' && this.monitor.isDragging()) {\n\t\t\tthis._mouseClientOffset = {}\n\n\t\t\tthis.uninstallSourceNodeRemovalObserver()\n\t\t\tthis.actions.endDrag()\n\t\t}\n\t}\n\n\tprivate installSourceNodeRemovalObserver(node: HTMLElement | undefined) {\n\t\tthis.uninstallSourceNodeRemovalObserver()\n\n\t\tthis.draggedSourceNode = node\n\t\tthis.draggedSourceNodeRemovalObserver = new MutationObserver(() => {\n\t\t\tif (node && !node.parentElement) {\n\t\t\t\tthis.resurrectSourceNode()\n\t\t\t\tthis.uninstallSourceNodeRemovalObserver()\n\t\t\t}\n\t\t})\n\n\t\tif (!node || !node.parentElement) {\n\t\t\treturn\n\t\t}\n\n\t\tthis.draggedSourceNodeRemovalObserver.observe(node.parentElement, {\n\t\t\tchildList: true,\n\t\t})\n\t}\n\n\tprivate resurrectSourceNode() {\n\t\tif (this.document && this.draggedSourceNode) {\n\t\t\tthis.draggedSourceNode.style.display = 'none'\n\t\t\tthis.draggedSourceNode.removeAttribute('data-reactid')\n\t\t\tthis.document.body.appendChild(this.draggedSourceNode)\n\t\t}\n\t}\n\n\tprivate uninstallSourceNodeRemovalObserver() {\n\t\tif (this.draggedSourceNodeRemovalObserver) {\n\t\t\tthis.draggedSourceNodeRemovalObserver.disconnect()\n\t\t}\n\n\t\tthis.draggedSourceNodeRemovalObserver = undefined\n\t\tthis.draggedSourceNode = undefined\n\t}\n}\n", "import type { BackendFactory, DragDropManager } from 'dnd-core'\n\nimport type { TouchBackendContext, TouchBackendOptions } from './interfaces.js'\nimport { TouchBackendImpl } from './TouchBackendImpl.js'\n\nexport * from './interfaces.js'\nexport * from './TouchBackendImpl.js'\n\nexport const TouchBackend: BackendFactory = function createBackend(\n\tmanager: DragDropManager,\n\tcontext: TouchBackendContext = {},\n\toptions: Partial<TouchBackendOptions> = {},\n): TouchBackendImpl {\n\treturn new TouchBackendImpl(manager, context, options)\n}\n"], "mappings": ";;;;;;AAAA,IA+BO;UAAKA,eAAY;AAAZA,EAAAA,cACXC,OAAK,IAALA;AADWD,EAAAA,cAEXE,OAAK,IAALA;AAFWF,EAAAA,cAGXG,UAAQ,IAARA;GAHWH,iBAAAA,eAAY,CAAA,EAAA;;;ACzBjB,IAAMI,gBAAN,MAAmB;EAMzB,IAAWC,QAAgB;QACnB;AAAP,YAAO,SAAA,KAAKC,KAAKD,WAAK,QAAf,WAAe,SAAf,SAAmB;;EAG3B,IAAWE,oBAA8C;AACxD,WAAO,KAAKD,KAAKC;;EAGlB,IAAWC,+BAEE;AACZ,WAAO,KAAKF,KAAKE;;EAGlB,IAAWC,oBAA6B;QAChC;AAAP,YAAO,qBAAA,KAAKH,KAAKG,uBAAiB,QAA3B,uBAA2B,SAA3B,qBAA+B;;EAGvC,IAAWC,2BAAoC;QACvC;AAAP,YAAO,4BAAA,KAAKJ,KAAKI,8BAAwB,QAAlC,8BAAkC,SAAlC,4BAAsC;;EAG9C,IAAWC,uBAAgC;QACnC;AAAP,YAAO,wBAAA,KAAKL,KAAKK,0BAAoB,QAA9B,0BAA8B,SAA9B,wBAAkC;;EAG1C,IAAWC,oBAA6B;QAChC;AAAP,YAAO,qBAAA,KAAKN,KAAKM,uBAAiB,QAA3B,uBAA2B,SAA3B,qBAA+B;;EAGvC,IAAWC,oBAA6B;QAChC;AAAP,YAAO,qBAAA,KAAKP,KAAKO,uBAAiB,QAA3B,uBAA2B,SAA3B,qBAA+B;;EAGvC,IAAWC,YAAoB;AAC9B,WAAO,KAAKR,KAAKQ,aAAa;;EAG/B,IAAWC,kBAA0B;QAC7B,KAA8B;QAA9B,MAAA;AAAP,YAAO,QAAA,QAAA,MAAA,KAAKT,UAAI,QAAT,QAAS,SAAT,SAAA,IAAWS,qBAAe,QAA1B,SAA0B,SAA1B,QAA8B,OAAA,KAAKT,UAAI,QAAT,SAAS,SAAT,SAAA,KAAWD,WAAK,QAA9C,SAA8C,SAA9C,OAAkD;;EAG1D,IAAWW,kBAA0B;QAC7B,KAA8B;QAA9B,MAAA;AAAP,YAAO,QAAA,QAAA,MAAA,KAAKV,UAAI,QAAT,QAAS,SAAT,SAAA,IAAWU,qBAAe,QAA1B,SAA0B,SAA1B,QAA8B,OAAA,KAAKV,UAAI,QAAT,SAAS,SAAT,SAAA,KAAWD,WAAK,QAA9C,SAA8C,SAA9C,OAAkD;;EAG1D,IAAWY,SAA6B;AACvC,QAAI,KAAKC,WAAW,KAAKA,QAAQD,QAAQ;AACxC,aAAO,KAAKC,QAAQD;eACV,OAAOA,WAAW,aAAa;AACzC,aAAOA;;AAER,WAAOE;;EAGR,IAAWC,WAAiC;QACvC;AAAJ,SAAI,MAAA,KAAKF,aAAO,QAAZ,QAAY,SAAZ,SAAA,IAAcE,UAAU;AAC3B,aAAO,KAAKF,QAAQE;;AAGrB,QAAI,KAAKH,QAAQ;AAChB,aAAO,KAAKA,OAAOG;;AAGpB,WAAOD;;EAGR,IAAWE,cAAgC;QACnC;AAAP,aAAO,MAAA,KAAKf,UAAI,QAAT,QAAS,SAAT,SAAA,IAAWe,gBAAgB,KAAKD;;EAzExC,YACSd,MACAY,SACP;SAFOZ,OAAAA;SACAY,UAAAA;;;;;ACPH,SAASI,SACfC,IACAC,IACAC,IACAC,IACS;AACT,SAAOC,KAAKC,KACXD,KAAKE,IAAIF,KAAKG,IAAIL,KAAKF,EAAE,GAAG,CAAC,IAAII,KAAKE,IAAIF,KAAKG,IAAIJ,KAAKF,EAAE,GAAG,CAAC,CAAC;;AAI1D,SAASO,cACfR,IACAC,IACAC,IACAC,IACAM,aACU;AACV,MAAI,CAACA,aAAa;AACjB,WAAO;;AAGR,QAAMC,QAASN,KAAKO,MAAMR,KAAKF,IAAIC,KAAKF,EAAE,IAAI,MAAOI,KAAKQ,KAAK;AAE/D,WAASC,IAAI,GAAGA,IAAIJ,YAAYK,QAAQ,EAAED,GAAG;AAC5C,UAAME,KAAKN,YAAYI,CAAC;AACxB,QACCE,OACCA,GAAGC,SAAS,QAAQN,SAASK,GAAGC,WAChCD,GAAGE,OAAO,QAAQP,SAASK,GAAGE,MAC9B;AACD,aAAO;;;AAIT,SAAO;;;;ACpCR,IAAMC,eAAe;EACpBC,MAAM;EACNC,OAAO;EACPC,QAAQ;;AAIT,IAAMC,cAAc;EACnBH,MAAM;EACNE,QAAQ;EACRD,OAAO;;AAOD,SAASG,qBAAqBC,GAAwB;AAG5D,SAAOA,EAAEC,WAAWC,UAAaF,EAAEC,WAAWH,YAAYH;;AASpD,SAASQ,mBAAmBH,GAAwB;AAG1D,SAAOA,EAAEI,YAAYF,WAAcF,EAAEI,UAAUV,aAAaC,UAAU;;AAGhE,SAASU,aACfL,GACkB;AAClB,SAAO,CAAC,CAAEA,EAAiBM;;;;ACnC5B,IAAMC,eAAe;AAEd,SAASC,oBAAoBC,MAAoC;AACvE,QAAMC,KAAKD,KAAKE,aAAaJ,eAAeE,OAAOA,KAAKG;AACxD,MAAI,CAACF,IAAI;AACR,WAAOG;;AAER,QAAM,EAAEC,KAAKC,KAAI,IAAKL,GAAGM,sBAAqB;AAC9C,SAAO;IAAEC,GAAGF;IAAMG,GAAGJ;;;AAGf,SAASK,0BACfC,GACAC,yBACsB;AACtB,MAAID,EAAEE,cAAcC,WAAW,GAAG;AACjC,WAAOC,qBAAqBJ,EAAEE,cAAc,CAAC,CAAC;aACpCD,2BAA2BD,EAAEK,QAAQF,WAAW,GAAG;AAC7D,QAAKH,EAAEK,QAAQ,CAAC,EAAYC,WAAWL,wBAAwBK,QAAQ;AACtE,aAAOF,qBAAqBJ,EAAEK,QAAQ,CAAC,CAAC;;;AAG1C;;AAGM,SAASD,qBACfJ,GACAC,yBACsB;AACtB,MAAIM,aAAaP,CAAC,GAAG;AACpB,WAAOD,0BAA0BC,GAAGC,uBAAuB;SACrD;AACN,WAAO;MACNJ,GAAGG,EAAEQ;MACLV,GAAGE,EAAES;;;;;;ACtCD,IAAMC,mBAAmB,MAAe;AAE9C,MAAIC,YAAY;AAChB,MAAI;AACHC,qBACC,QACA,MAAM;OAGNC,OAAOC,eAAe,CAAA,GAAI,WAAW;MACpCC,MAAM;AACLJ,oBAAY;AACZ,eAAO;;KAER,CAAC;WAEKK,GAAG;;AAGZ,SAAOL;GACN;;;ACOF,IAAMM,aAA8C;EACnD,CAACC,aAAaC,KAAK,GAAG;IACrBC,OAAO;IACPC,MAAM;IACNC,KAAK;IACLC,aAAa;;EAEd,CAACL,aAAaM,KAAK,GAAG;IACrBJ,OAAO;IACPC,MAAM;IACNC,KAAK;;EAEN,CAACJ,aAAaO,QAAQ,GAAG;IACxBC,SAAS;;;AAIJ,IAAMC,mBAAN,MAAMA,kBAAgB;;;;EA2DrBC,UAAkC;QAMpB;AALpB,WAAO;MACNC,aAAa,KAAKA,YAAYC;MAC9BC,oBAAoB,KAAKA,mBAAmBD;MAC5CE,0BAA0B,KAAKA,yBAAyBF;MACxDG,aAAa,KAAKA,YAAYH;MAC9BI,qBAAmB,MAAA,KAAKA,uBAAiB,QAAtB,QAAsB,SAAtB,SAAA,IAAwBC,WAAU;;;;EAKvD,IAAWC,WAAiC;AAC3C,WAAO,KAAKC,QAAQD;;EAGdE,QAAc;AACpB,UAAMC,OAAO,KAAKF,QAAQG;AAC1B,QAAI,CAACD,MAAM;AACV;;AAGDE,cACC,CAACd,kBAAiBe,SAClB,kDAAkD;AAEnDf,sBAAiBe,UAAU;AAE3B,SAAKC,iBAAiBJ,MAAM,SAAS,KAAKK,uBAAsB,CAAE;AAClE,SAAKD,iBACJJ,MACA,SACA,KAAKM,2BACL,IAAI;AAEL,SAAKF,iBAAiBJ,MAAM,QAAQ,KAAKO,aAAa;AACtD,SAAKH,iBAAiBJ,MAAM,QAAQ,KAAKQ,sBAAsB,IAAI;AACnE,SAAKJ,iBACJJ,MACA,OACA,KAAKS,yBACL,IAAI;AAGL,QAAI,KAAKX,QAAQY,qBAAqB,CAAC,KAAKZ,QAAQa,mBAAmB;AACtE,WAAKP,iBACJJ,MACA,eACA,KAAKS,uBAAuB;;AAI9B,QAAI,KAAKX,QAAQc,sBAAsB;AACtC,WAAKR,iBACJJ,MACA,WACA,KAAKa,sBACL,IAAI;;;EAKAC,WAAiB;AACvB,UAAMd,OAAO,KAAKF,QAAQG;AAC1B,QAAI,CAACD,MAAM;AACV;;AAGDZ,sBAAiBe,UAAU;AAC3B,SAAKY,qBAAqB,CAAA;AAE1B,SAAKC,oBACJhB,MACA,SACA,KAAKM,2BACL,IAAI;AAEL,SAAKU,oBAAoBhB,MAAM,SAAS,KAAKiB,kBAAkB;AAC/D,SAAKD,oBAAoBhB,MAAM,QAAQ,KAAKQ,sBAAsB,IAAI;AACtE,SAAKQ,oBAAoBhB,MAAM,QAAQ,KAAKO,aAAa;AACzD,SAAKS,oBACJhB,MACA,OACA,KAAKS,yBACL,IAAI;AAGL,QAAI,KAAKX,QAAQY,qBAAqB,CAAC,KAAKZ,QAAQa,mBAAmB;AACtE,WAAKK,oBACJhB,MACA,eACA,KAAKS,uBAAuB;;AAI9B,QAAI,KAAKX,QAAQc,sBAAsB;AACtC,WAAKI,oBACJhB,MACA,WACA,KAAKa,sBACL,IAAI;;AAIN,SAAKK,mCAAkC;;EAGhCd,iBACPe,SACAC,OACAC,SACAC,UAAU,OACT;AACD,UAAMxB,UAAUyB,kBAAkB;MAAED;MAASE,SAAS;QAAUF;AAEhE,SAAKG,cAAcC,QAAQ,SAAUC,cAAc;AAClD,YAAMC,MAAMlD,WAAWiD,YAAY,EAAEP,KAAK;AAE1C,UAAIQ,KAAK;AACRT,gBAAQf,iBAAiBwB,KAAYP,SAAgBvB,OAAO;;KAE7D;;EAGMkB,oBACPG,SACAC,OACAC,SACAC,UAAU,OACT;AACD,UAAMxB,UAAUyB,kBAAkB;MAAED;MAASE,SAAS;QAAUF;AAEhE,SAAKG,cAAcC,QAAQ,SAAUC,cAAc;AAClD,YAAMC,MAAMlD,WAAWiD,YAAY,EAAEP,KAAK;AAE1C,UAAIQ,KAAK;AACRT,gBAAQH,oBAAoBY,KAAYP,SAAgBvB,OAAO;;KAEhE;;EAGK+B,kBAAkBC,UAAkBC,MAAgC;AAC1E,UAAMC,kBAAkB,KAAKA,gBAAgBC,KAAK,MAAMH,QAAQ;AAChE,SAAKxC,YAAY4C,IAAIJ,UAAUC,IAAI;AAEnC,SAAK3B,iBAAiB2B,MAAM,SAASC,eAAe;AAEpD,WAAO,MAAY;AAClB,WAAK1C,YAAY6C,OAAOL,QAAQ;AAChC,WAAKd,oBAAoBe,MAAM,SAASC,eAAe;;;EAIlDI,mBACNN,UACAC,MACAjC,SACc;AACd,SAAKL,yBAAyByC,IAAIJ,UAAUhC,OAAO;AACnD,SAAKN,mBAAmB0C,IAAIJ,UAAUC,IAAI;AAE1C,WAAO,MAAY;AAClB,WAAKvC,mBAAmB2C,OAAOL,QAAQ;AACvC,WAAKrC,yBAAyB0C,OAAOL,QAAQ;;;EAIxCO,kBAAkBC,UAAkBP,MAAgC;AAC1E,UAAM/B,OAAO,KAAKF,QAAQG;AAC1B,QAAI,CAAC,KAAKJ,YAAY,CAACG,MAAM;AAC5B,aAAO,MAAY;MACR;;AAIZ,UAAMuC,aAAa,CAACC,MAA+B;AAClD,UAAI,CAAC,KAAK3C,YAAY,CAACG,QAAQ,CAAC,KAAKyC,QAAQC,WAAU,GAAI;AAC1D;;AAGD,UAAIC;AAKJ,cAAQH,EAAEI,MAAI;QACb,KAAKlE,WAAWE,MAAME;AACrB6D,mBAAS;YACRE,GAAIL,EAAiBM;YACrBC,GAAIP,EAAiBQ;;AAEtB;QAED,KAAKtE,WAAWO,MAAMH;cAEjB,KACA;AAFJ6D,mBAAS;YACRE,KAAG,MAACL,EAAiBS,QAAQ,CAAC,OAAC,QAA5B,QAA4B,SAA5B,SAAA,IAA8BH,YAAW;YAC5CC,KAAG,OAACP,EAAiBS,QAAQ,CAAC,OAAC,QAA5B,SAA4B,SAA5B,SAAA,KAA8BD,YAAW;;AAE7C;;AAOF,YAAME,YACLP,UAAU,OACP,KAAK9C,SAASsD,iBAAiBR,OAAOE,GAAGF,OAAOI,CAAC,IACjDK;AACJ,YAAMC,aAAaH,aAAanB,KAAKuB,SAASJ,SAAS;AAEvD,UAAIA,cAAcnB,QAAQsB,YAAY;AACrC,eAAO,KAAKd,WAAWC,GAAGF,QAAQ;;;AAOpC,SAAKlC,iBAAiB,KAAKP,SAAS0D,MAAM,QAAQhB,UAAU;AAC5D,SAAK7C,YAAYwC,IAAII,UAAUP,IAAI;AAEnC,WAAO,MAAY;AAClB,UAAI,KAAKlC,UAAU;AAClB,aAAKH,YAAYyC,OAAOG,QAAQ;AAChC,aAAKtB,oBAAoB,KAAKnB,SAAS0D,MAAM,QAAQhB,UAAU;;;;EA0B1DlC,yBAAyB;AAChC,QAAI,CAAC,KAAKP,QAAQ0D,mBAAmB,CAAC,KAAK1D,QAAQ2D,iBAAiB;AACnE,aAAO,KAAKxC;;AAGb,WAAO,KAAKyC;;EA0OLC,iCAAiC5B,MAA+B;AACvE,SAAKb,mCAAkC;AAEvC,SAAK0C,oBAAoB7B;AACzB,SAAK8B,mCAAmC,IAAIC,iBAAiB,MAAM;AAClE,UAAI/B,QAAQ,CAACA,KAAKgC,eAAe;AAChC,aAAKC,oBAAmB;AACxB,aAAK9C,mCAAkC;;KAExC;AAED,QAAI,CAACa,QAAQ,CAACA,KAAKgC,eAAe;AACjC;;AAGD,SAAKF,iCAAiCI,QAAQlC,KAAKgC,eAAe;MACjEG,WAAW;KACX;;EAGMF,sBAAsB;AAC7B,QAAI,KAAKnE,YAAY,KAAK+D,mBAAmB;AAC5C,WAAKA,kBAAkBO,MAAMC,UAAU;AACvC,WAAKR,kBAAkBS,gBAAgB,cAAc;AACrD,WAAKxE,SAAS0D,KAAKe,YAAY,KAAKV,iBAAiB;;;EAI/C1C,qCAAqC;AAC5C,QAAI,KAAK2C,kCAAkC;AAC1C,WAAKA,iCAAiCU,WAAU;;AAGjD,SAAKV,mCAAmCT;AACxC,SAAKQ,oBAAoBR;;EA5iB1B,YACCoB,SACAC,SACA3E,SACC;AAkQF,SAAQ4E,wBAAwB,CAAC5C,aAA0C;AAC1E,YAAM6C,UAAU,KAAKrF,YAAYsF,IAAI9C,QAAQ;AAC7C,aAAO6C,WAAWE,oBAAoBF,OAAO;;AAG9C,SAAOrE,4BAA4B,CAACkC,MAAmB;AACtD,UAAI,CAACsC,qBAAqBtC,CAAC,GAAiB;AAC3C;;AAGD,WAAKuC,qBAAqB,CAAA;;AAG3B,SAAO/C,kBAAkB,CAACF,aAA2B;AAGpD,UAAIkD,MAAMC,QAAQ,KAAKF,kBAAkB,GAAG;AAC3C,aAAKA,mBAAmBG,QAAQpD,QAAQ;;;AAY1C,SAAOb,qBAAqB,CAACuB,MAAqC;AACjE,UAAI,CAACsC,qBAAqBtC,CAAC,GAAiB;AAC3C;;AAQD,YAAM2C,eAAeC,qBAAqB5C,CAAC;AAC3C,UAAI2C,cAAc;AACjB,YAAIE,aAAa7C,CAAC,GAAG;AACpB,eAAK8C,0BAA0B9C,EAAE+C,cAAc,CAAC;;AAEjD,aAAKxE,qBAAqBoE;;AAE3B,WAAKK,kBAAkB;;AAGxB,SAAO9B,0BAA0B,CAAClB,MAAmB;AACpD,UAAI,CAACsC,qBAAqBtC,CAAC,GAAiB;AAC3C;;AAGD,YAAMiD,QACLjD,EAAEI,SAASlE,WAAWO,MAAMJ,QACzB,KAAKiB,QAAQ0D,kBACb,KAAK1D,QAAQ2D;AACjB,WAAKiC,UAAUC,WACd,KAAK1E,mBAAmBgB,KAAK,MAAMO,CAAC,GACpCiD,KAAK;AAEN,WAAKD,kBAAkB;;AAGxB,SAAOhF,uBAAuB,MAAY;AACzC,WAAKb,oBAAoB,CAAA;;AAG1B,SAAO4C,aAAa,CACnBqD,MACAtD,aACU;AACV,UAAI,KAAK3C,mBAAmB;AAC3B,aAAKA,kBAAkBuF,QAAQ5C,QAAQ;;;AAIzC,SAAO/B,gBAAgB,CAACiC,OAAqC;AAC5D,UAAI,KAAKkD,SAAS;AACjBG,qBAAa,KAAKH,OAAO;;AAE1B,UAAI,CAAC,KAAK7F,YAAY,KAAK2F,iBAAiB;AAC3C;;AAED,YAAM,EAAET,oBAAoBpF,kBAAiB,IAAK;AAClD,YAAMmG,2BAA2B,KAAKhG,QAAQgG;AAE9C,YAAMX,eAAeC,qBAAqB5C,IAAG,KAAK8C,uBAAuB;AAEzE,UAAI,CAACH,cAAc;AAClB;;AAID,UACC,KAAKY,gBACJ,CAAC,KAAKtD,QAAQC,WAAU,KACxBsD,cACC,KAAKjF,mBAAmB8B,KAAK,GAC7B,KAAK9B,mBAAmBgC,KAAK,GAC7BoC,aAAatC,GACbsC,aAAapC,GACb,KAAKjD,QAAQmG,iBAAiB,GAE/B;AACD,aAAKF,eAAe;AACpB;;AAID,UACC,CAAC,KAAKtD,QAAQC,WAAU;MAExB,KAAK3B,mBAAmBmF,eAAe,GAAG,KAC1CnB,sBACAoB,SACC,KAAKpF,mBAAmB8B,KAAK,GAC7B,KAAK9B,mBAAmBgC,KAAK,GAC7BoC,aAAatC,GACbsC,aAAapC,CAAC,KACV,KAAKjD,QAAQsG,YAAY,KAAKtG,QAAQsG,YAAY,IACtD;AACD,aAAKrB,qBAAqB3B;AAE1B,aAAKiD,QAAQC,UAAUvB,oBAAoB;UAC1CI,cAAc,KAAKpE;UACnB2D,uBAAuB,KAAKA;UAC5B6B,eAAe;SACf;;AAGF,UAAI,CAAC,KAAK9D,QAAQC,WAAU,GAAI;AAC/B;;AAGD,YAAM8D,aAAa,KAAKlH,YAAYsF,IACnC,KAAKnC,QAAQgE,YAAW,CAAE;AAE3B,WAAK9C,iCAAiC6C,UAAU;AAChD,WAAKH,QAAQK,kBAAiB;AAE9B,UAAIlE,GAAEmE;AAAYnE,WAAEoE,eAAc;AAGlC,YAAMC,uBAAsClH,qBAAqB,CAAA,GAC/DmH;QAAI,CAACC,QAAQ,KAAKrH,YAAYkF,IAAImC,GAAG;MAAC,EACtCC;QAAO,CAACxE,MAAM,CAAC,CAACA;MAAC;AAGnB,YAAMyE,kBAAkB,KAAKnH,QAAQoH,+BAClC,KAAKpH,QAAQoH,6BACb/B,aAAatC,GACbsC,aAAapC,GACb8D,mBAAmB,IAEnB,KAAKhH,SAASsH,kBAAkBhC,aAAatC,GAAGsC,aAAapC,CAAC;AAEjE,YAAMqE,0BAAqC,CAAA;AAC3C,iBAAWC,UAAUJ,iBAAiB;AAErC,YAAI,CAACA,gBAAgBf,eAAemB,MAAM,GAAG;AAC5C;;AAED,YAAIC,cAA0CL,gBAAgBI,MAAM;AACpE,YAAIC,eAAe,MAAM;AACxBF,kCAAwBG,KAAKD,WAAW;;AAEzC,eAAOA,aAAa;AACnBA,wBAAcA,YAAYvD;AAC1B,cACCuD,eACAF,wBAAwBI,QAAQF,WAAW,MAAM,IAChD;AACDF,oCAAwBG,KAAKD,WAAW;;;;AAI3C,YAAMG,2BAAqCL,wBAEzCJ;QAAO,CAACjF,SAAS8E,oBAAoBW,QAAQzF,IAAI,IAAmB;MAAE,EAEtE+E;QAAI,CAAC/E,SAAS,KAAK2F,iBAAiB3F,IAAI;MAAC,EAEzCiF;QAAO,CAACjF,SAAS,CAAC,CAACA;MAAI,EACvBiF;QAAO,CAACW,IAAIC,OAAOC,QAAQA,IAAIL,QAAQG,EAAE,MAAMC;MAAK;AAGtD,UAAI9B,0BAA0B;AAC7B,mBAAWxD,YAAY,KAAK5C,aAAa;AACxC,gBAAMoI,aAAa,KAAKpI,YAAYkF,IAAItC,QAAQ;AAChD,cACCkE,cACAsB,cACAA,WAAWxE,SAASkD,UAAU,KAC9BiB,yBAAyBD,QAAQlF,QAAQ,MAAM,IAC9C;AACDmF,qCAAyBvC,QAAQ5C,QAAQ;AACzC;;;;AAMHmF,+BAAyBM,QAAO;AAEhC,WAAK1B,QAAQ2B,MAAMP,0BAA0B;QAC5CtC;OACA;;AAOF,SAAOuC,mBAAmB,CAAC3F,SAA0C;AACpE,YAAMkG,OAAO,KAAKvI,YAAYuI,KAAI;AAClC,UAAIC,OAAOD,KAAKC,KAAI;AACpB,aAAOA,KAAKC,SAAS,OAAO;AAC3B,cAAM7F,WAAW4F,KAAKE;AACtB,YAAIrG,SAAS,KAAKrC,YAAYkF,IAAItC,QAAQ,GAAG;AAC5C,iBAAOA;eACD;AACN4F,iBAAOD,KAAKC,KAAI;;;AAGlB,aAAO9E;;AAGR,SAAO3C,0BAA0B,CAAC+B,MAAmB;AACpD,WAAKuD,eAAe;AACpB,WAAKT,0BAA0BlC;AAE/B,UAAI,CAACiF,mBAAmB7F,CAAC,GAAiB;AACzC;;AAGD,UAAI,CAAC,KAAKC,QAAQC,WAAU,KAAM,KAAKD,QAAQ6F,QAAO,GAAI;AACzD,aAAKvD,qBAAqB3B;AAC1B;;AAGD,UAAIZ,EAAEmE;AAAYnE,UAAEoE,eAAc;AAElC,WAAK7F,qBAAqB,CAAA;AAE1B,WAAKG,mCAAkC;AACvC,WAAKmF,QAAQkC,KAAI;AACjB,WAAKlC,QAAQmC,QAAO;;AAGrB,SAAO3H,uBAAuB,CAAC2B,MAA2B;AACzD,UAAIA,EAAEuE,QAAQ,YAAY,KAAKtE,QAAQC,WAAU,GAAI;AACpD,aAAK3B,qBAAqB,CAAA;AAE1B,aAAKG,mCAAkC;AACvC,aAAKmF,QAAQmC,QAAO;;;AAjgBrB,SAAK1I,UAAU,IAAI2I,cAAc3I,SAAS2E,OAAO;AACjD,SAAK4B,UAAU7B,QAAQkE,WAAU;AACjC,SAAKjG,UAAU+B,QAAQmE,WAAU;AAEjC,SAAKrJ,cAAc,oBAAIsJ,IAAG;AAC1B,SAAKpJ,qBAAqB,oBAAIoJ,IAAG;AACjC,SAAKnJ,2BAA2B,oBAAImJ,IAAG;AACvC,SAAKlJ,cAAc,oBAAIkJ,IAAG;AAC1B,SAAKnH,gBAAgB,CAAA;AACrB,SAAKV,qBAAqB,CAAA;AAC1B,SAAKgF,eAAe;AAEpB,QAAI,KAAKjG,QAAQY,mBAAmB;AACnC,WAAKe,cAAc8F,KAAK5I,aAAaC,KAAK;;AAG3C,QAAI,KAAKkB,QAAQ+I,mBAAmB;AACnC,WAAKpH,cAAc8F,KAAK5I,aAAaM,KAAK;;AAG3C,QAAI,KAAKa,QAAQc,sBAAsB;AACtC,WAAKa,cAAc8F,KAAK5I,aAAaO,QAAQ;;;;;;ACxFzC,IAAM4J,eAA+B,SAASC,cACpDC,SACAC,UAA+B,CAAA,GAC/BC,UAAwC,CAAA,GACrB;AACnB,SAAO,IAAIC,iBAAiBH,SAASC,SAASC,OAAO;;", "names": ["ListenerType", "mouse", "touch", "keyboard", "OptionsReader", "delay", "args", "scrollAngleRanges", "getDropTargetElementsAtPoint", "ignoreContextMenu", "enableHoverOutsideTarget", "enableKeyboardEvents", "enableMouseEvents", "enableTouchEvents", "touchSlop", "delayTouchStart", "delayMouseStart", "window", "context", "undefined", "document", "rootElement", "distance", "x1", "y1", "x2", "y2", "Math", "sqrt", "pow", "abs", "inAngleRanges", "angleRanges", "angle", "atan2", "PI", "i", "length", "ar", "start", "end", "MouseButtons", "Left", "Right", "Center", "MouseB<PERSON>on", "eventShouldStartDrag", "e", "button", "undefined", "eventShouldEndDrag", "buttons", "isTouchEvent", "targetTouches", "ELEMENT_NODE", "getNodeClientOffset", "node", "el", "nodeType", "parentElement", "undefined", "top", "left", "getBoundingClientRect", "x", "y", "getEventClientTouchOffset", "e", "lastTargetTouchFallback", "targetTouches", "length", "getEventClientOffset", "touches", "target", "isTouchEvent", "clientX", "clientY", "supportsPassive", "supported", "addEventListener", "Object", "defineProperty", "get", "e", "eventNames", "ListenerType", "mouse", "start", "move", "end", "contextmenu", "touch", "keyboard", "keydown", "TouchBackendImpl", "profile", "sourceNodes", "size", "sourcePreviewNodes", "sourcePreviewNodeOptions", "targetNodes", "dragOverTargetIds", "length", "document", "options", "setup", "root", "rootElement", "invariant", "isSetUp", "addEventListener", "getTopMoveStartHandler", "handleTopMoveStartCapture", "handleTopMove", "handleTopMoveCapture", "handleTopMoveEndCapture", "enableMouseEvents", "ignoreContextMenu", "enableKeyboardEvents", "handleCancelOnEscape", "teardown", "_mouseClientOffset", "removeEventListener", "handleTopMoveStart", "uninstallSourceNodeRemovalObserver", "subject", "event", "handler", "capture", "supportsPassive", "passive", "listenerTypes", "for<PERSON>ach", "listenerType", "evt", "connectDragSource", "sourceId", "node", "handleMoveStart", "bind", "set", "delete", "connectDragPreview", "connectDropTarget", "targetId", "handleMove", "e", "monitor", "isDragging", "coords", "type", "x", "clientX", "y", "clientY", "touches", "droppedOn", "elementFromPoint", "undefined", "childMatch", "contains", "body", "delayTouchStart", "delayMouseStart", "handleTopMoveStartDelay", "installSourceNodeRemovalObserver", "draggedSourceNode", "draggedSourceNodeRemovalObserver", "MutationObserver", "parentElement", "resurrectSourceNode", "observe", "childList", "style", "display", "removeAttribute", "append<PERSON><PERSON><PERSON>", "disconnect", "manager", "context", "getSourceClientOffset", "element", "get", "getNodeClientOffset", "eventShouldStartDrag", "moveStartSourceIds", "Array", "isArray", "unshift", "clientOffset", "getEventClientOffset", "isTouchEvent", "lastTargetTouchFallback", "targetTouches", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "delay", "timeout", "setTimeout", "_evt", "clearTimeout", "enableHoverOutsideTarget", "_isScrolling", "inAngleRanges", "scrollAngleRanges", "hasOwnProperty", "distance", "touchSlop", "actions", "beginDrag", "publishSource", "sourceNode", "getSourceId", "publishDragSource", "cancelable", "preventDefault", "dragOverTargetNodes", "map", "key", "filter", "elementsAtPoint", "getDropTargetElementsAtPoint", "elementsFromPoint", "elementsAtPointExtended", "nodeId", "currentNode", "push", "indexOf", "orderedDragOverTargetIds", "_getDropTargetId", "id", "index", "ids", "targetNode", "reverse", "hover", "keys", "next", "done", "value", "eventShouldEndDrag", "didDrop", "drop", "endDrag", "OptionsReader", "getActions", "getMonitor", "Map", "enableTouchEvents", "TouchBackend", "createBackend", "manager", "context", "options", "TouchBackendImpl"]}