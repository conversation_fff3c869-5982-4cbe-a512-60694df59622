import { CaseReducer, PayloadAction } from '@reduxjs/toolkit';
import { getItemData, itemDurability } from '../helpers';
import { findInventoryByID, findPlayerInventory, inventorySlice, selectPlayerInventory } from '../store/inventory';
import { Items } from '../store/items';
import { Inventory, InventoryType, Slot, State } from '../typings';
import { useAppSelector } from '../store';

export type ItemsPayload = { item: Slot; inventory?: InventoryType };

interface Payload {
  items?: ItemsPayload | ItemsPayload[];
  itemCount?: Record<string, number>;
  weightData?: { inventoryId: string; maxWeight: number };
  slotsData?: { inventoryId: string; slots: number };
  playerInventories?: Array<Inventory>;
  targetInventories?: Array<Inventory>;
}

export const refreshSlotsReducer: CaseReducer<State, PayloadAction<Payload>> = (state, action) => {
  if (action.payload.items) {
    if (!Array.isArray(action.payload.items)) action.payload.items = [action.payload.items];
    const curTime = Math.floor(Date.now() / 1000);
    Object.values(action.payload.items)
      .filter((data) => !!data)
      .forEach((data) => {
        const targetInventory = data.inventory
          ? findInventoryByID(data.inventory, state)
          ? findInventoryByID(data.inventory, state)
          : findPlayerInventory(state)
          : findPlayerInventory(state);

        data.item.durability = itemDurability(data.item.metadata, curTime);
        targetInventory.items[data.item.slot - 1] = data.item;
      });

    // Janky workaround to force a state rerender for crafting inventory to
    // run canCraftItem checks
    state.targetInventories.forEach(element => {
      if(element.type == InventoryType.CRAFTING)
        element = {...element}
    });
  }

  if (action.payload.itemCount) {
    const items = Object.entries(action.payload.itemCount);

    for (let i = 0; i < items.length; i++) {
      const item = items[i][0];
      const count = items[i][1];

      if (Items[item]!) {
        Items[item]!.count += count;
      } else console.log(`Item data for ${item} is undefined`);
    }
  }

  // Refresh maxWeight when SetMaxWeight is ran while an inventory is open
  if (action.payload.weightData) {
    const inventoryId = action.payload.weightData.inventoryId;
    const inventoryMaxWeight = action.payload.weightData.maxWeight;
    const inv = findInventoryByID(inventoryId, state)

    if (!inv) return;

    inv.maxWeight = inventoryMaxWeight;
  }

  if (action.payload.slotsData) {
    const {inventoryId} = action.payload.slotsData;
    const {slots} = action.payload.slotsData;

    const inv =
      state.playerInventories.find((element) => element.id == inventoryId) != undefined
        ? 'player'
        : state.targetInventories.find((element) => element.id == inventoryId) != undefined
        ? 'target'
        : null;

    if (!inv) return;

    useAppSelector(selectPlayerInventory).slots = slots;
    inventorySlice.caseReducers.setupInventory(state, {
      type: 'setupInventory',
      payload: {
        playerInventories: inv === 'player' ? state.playerInventories : undefined,
        targetInventories: inv === 'target' ? state.targetInventories : undefined,
      },
    });
  }

  // if (action.payload.playerInventories) {
  //   const curTime = Math.floor(Date.now() / 1000);
  //   action.payload.playerInventories.forEach(element => {
  //     var inventory = state.playerInventories.find((e) => { e.id = element.id})
  //     if (inventory == undefined) {
  //         state.playerInventories.push({
  //         ...element,
  //         items: Array.from(Array(element.slots), (_, index) => {
  //           const item = Object.values(element.items).find((item) => item?.slot === index + 1) || {
  //             slot: index + 1,
  //           };

  //           if (!item.name) return item;

  //           if (typeof Items[item.name] === 'undefined') {
  //             getItemData(item.name);
  //           }

  //           item.durability = itemDurability(item.metadata, curTime);
  //           return item;
  //         }),
  //       })
  //     } else {
  //       inventory = {
  //         id: inventory.id,
  //         type: inventory.type,
  //         slots: inventory.slots,
  //         items: Array.from(Array(element.slots), (_, index) => {
  //           const item = Object.values(element.items).find((item) => item?.slot === index + 1) || {
  //             slot: index + 1,
  //           };

  //           if (!item.name) return item;

  //           if (typeof Items[item.name] === 'undefined') {
  //             getItemData(item.name);
  //           }

  //           item.durability = itemDurability(item.metadata, curTime);
  //           return item;
  //         }),
  //       }
  //     }
  //   });
  //   for (let i = 0; i < state.playerInventories.length; i++) {
  //     var inventory = action.payload.playerInventories.find(e => {e.id == state.playerInventories[i].id})
  //     if(inventory == undefined) {
  //       // state.playerInventories.splice(i, 1)
  //       console.log(action.payload.playerInventories, state.playerInventories)
  //     }
  //   }
  // }

  // if (action.payload.targetInventories) {
  //   const curTime = Math.floor(Date.now() / 1000);
  //   action.payload.targetInventories.forEach(element => {
  //     var inventory = state.targetInventories.find((e) => { e.id = element.id})
  //     if (inventory == undefined) {
  //         state.targetInventories.push({
  //         ...element,
  //         items: Array.from(Array(element.slots), (_, index) => {
  //           const item = Object.values(element.items).find((item) => item?.slot === index + 1) || {
  //             slot: index + 1,
  //           };

  //           if (!item.name) return item;

  //           if (typeof Items[item.name] === 'undefined') {
  //             getItemData(item.name);
  //           }

  //           item.durability = itemDurability(item.metadata, curTime);
  //           return item;
  //         }),
  //       })
  //     } else {
  //       inventory = {
  //         id: inventory.id,
  //         type: inventory.type,
  //         slots: inventory.slots,
  //         items: Array.from(Array(element.slots), (_, index) => {
  //           const item = Object.values(element.items).find((item) => item?.slot === index + 1) || {
  //             slot: index + 1,
  //           };

  //           if (!item.name) return item;

  //           if (typeof Items[item.name] === 'undefined') {
  //             getItemData(item.name);
  //           }

  //           item.durability = itemDurability(item.metadata, curTime);
  //           return item;
  //         }),
  //       }
  //     }
  //   });
  //   for (let i = 0; i < state.targetInventories.length; i++) {
  //     var inventory = action.payload.targetInventories.find(e => {e.id == state.targetInventories[i].id})
  //     if(inventory == undefined) {
  //       // state.targetInventories.splice(i, 1)
  //       console.log(action.payload.targetInventories, state.targetInventories)
  //     }
  //   }
  // }
};
